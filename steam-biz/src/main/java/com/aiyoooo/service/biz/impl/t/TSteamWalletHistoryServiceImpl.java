package com.aiyoooo.service.biz.impl.t;

import com.aiyoooo.service.biz.service.t.TSteamWalletHistoryService;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamWalletHistory;
import com.aiyoooo.service.dao.mapper.TSteamWalletHistoryMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Steam钱包历史记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class TSteamWalletHistoryServiceImpl extends ServiceImpl<TSteamWalletHistoryMapper, TSteamWalletHistory> implements TSteamWalletHistoryService {

    @Override
    public void doSyncWalletHistory(TSteamAccount account) {
        List<TSteamWalletHistory> walletHistory = SteamUtil.getWalletHistory(account);
        for (TSteamWalletHistory tSteamWalletHistory : walletHistory) {
            List<TSteamWalletHistory> child = new ArrayList<>();
            child.add(tSteamWalletHistory);
            baseMapper.insertOrUpdateBatch(child);
        }
    }

    /**
     * 迁移历史数据
     * 将指定天数前的钱包历史记录迁移到归档表
     *
     * @param days 天数，如30表示迁移30天前的数据
     * @return 迁移的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int migrateHistoryData(int days) {
        log.info("开始迁移{}天前的钱包历史记录", days);
        
        try {
            // 执行迁移操作
            int migratedCount = baseMapper.migrateToArchive(days);
            
            if (migratedCount > 0) {
                log.info("成功迁移{}条钱包历史记录到归档表", migratedCount);
                
                // 从原表中删除已迁移的数据
                Date cutoffDate = new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L);
                LambdaQueryWrapper<TSteamWalletHistory> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.lt(TSteamWalletHistory::getCreateDatetime, cutoffDate);
                int deletedCount = baseMapper.delete(deleteWrapper);
                
                log.info("成功从原表删除{}条已迁移的数据", deletedCount);
                return deletedCount;
            } else {
                log.info("没有找到需要迁移的钱包历史记录");
                return 0;
            }
        } catch (Exception e) {
            log.error("迁移钱包历史记录失败", e);
            throw new RuntimeException("迁移钱包历史记录失败: " + e.getMessage(), e);
        }
    }
}