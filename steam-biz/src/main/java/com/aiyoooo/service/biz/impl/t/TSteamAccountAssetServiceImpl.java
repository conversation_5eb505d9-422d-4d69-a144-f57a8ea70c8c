package com.aiyoooo.service.biz.impl.t;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetService;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.common.util.DateUtil;
import com.aiyoooo.service.common.util.RedisUtil;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAssetHistory;
import com.aiyoooo.service.dao.enums.EAccountAssetFlowDirectionEnum;
import com.aiyoooo.service.dao.enums.EBoolean;
import com.aiyoooo.service.dao.enums.ESteamAssetStatusEnum;
import com.aiyoooo.service.dao.mapper.TSteamAccountAssetHistoryMapper;
import com.aiyoooo.service.dao.mapper.TSteamAccountAssetMapper;
import com.aiyoooo.service.dao.mapper.TSteamAccountMapper;
import com.aiyoooo.service.dao.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
public class TSteamAccountAssetServiceImpl extends ServiceImpl<TSteamAccountAssetMapper, TSteamAccountAsset> implements TSteamAccountAssetService {

    @Resource
    private TSteamAccountAssetHistoryMapper tSteamAccountAssetHistoryMapper;

    @Resource
    private TSteamAccountMapper tSteamAccountMapper;

    @Resource
    private TSteamAccountAssetMapper tSteamAccountAssetMapper;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public void doSaveAsset(SteamAPIResponseInventoryAsset asset, TSteamAccount account, SteamAPIResponseInventoryDescription steamAPIResponseInventoryDescription) {
        Date tradeStartDate = null;
        List<SteamAPIResponseInvestoryOwnerDescription> ownerDescriptions = steamAPIResponseInventoryDescription.getOwnerDescriptions();
        if (CollectionUtils.isNotEmpty(ownerDescriptions)) {
            for (SteamAPIResponseInvestoryOwnerDescription ownerDescription : ownerDescriptions) {
                // 遍历数组
                if (ownerDescription.getType().equals("html")) {
                    String value = ownerDescription.getValue();
                    // 提取时间部分 (如: 2024 12月 28 (8:00:00))
                    String regex = "(\\d{4} \\d{1,2}月 \\d{1,2} \\(\\d{1,2}:\\d{1,2}:\\d{1,2}\\))";
                    if (value.matches(".*" + regex + ".*")) {
                        // 使用正则表达式提取日期和时间
                        String timeString = value.replaceAll(".*(" + regex + ").*", "$1");

                        // 格式化时间字符串以便转换为 Date
                        // 需要将时间格式从 "2024 12月 28 (8:00:00)" 转换为 "2024-12-28 08:00:00"
                        timeString = timeString.replaceAll("(\\d{4}) (\\d{1,2})月 (\\d{1,2}) \\((\\d{1,2}:\\d{1,2}:\\d{1,2})\\)",
                                "$1-$2-$3 $4");
                        tradeStartDate = DateUtil.strToDate(timeString, DateUtil.YYYY_MM_DD_HH_MM_SS);
                        //根据当前时区设置交易开始时间
                        tradeStartDate = DateUtil.getRelativeDateOfSecond(tradeStartDate, DateUtil.getCurrentZoneUTCSecond());
                    }
                }
            }
        }

        TSteamAccountAsset steamAccountAsset = new TSteamAccountAsset();
        steamAccountAsset.setAssetid(asset.getAssetid());
        steamAccountAsset.setAccountId(account.getId());
        steamAccountAsset.setClassid(asset.getClassid());
        steamAccountAsset.setInstanceid(asset.getInstanceid());
        steamAccountAsset.setMarketName(steamAPIResponseInventoryDescription.getMarketName());
        steamAccountAsset.setMarketHashName(steamAPIResponseInventoryDescription.getMarketHashName());
        steamAccountAsset.setInventoryStatus(ESteamAssetStatusEnum.AVAILABLE.getCode());
        if (tradeStartDate != null) {
            steamAccountAsset.setInventoryStatus(ESteamAssetStatusEnum.COOLDOWN.getCode());
            steamAccountAsset.setTradeStartDatetime(tradeStartDate);
        }
        steamAccountAsset.setDescription(JSONUtil.toJsonStr(steamAPIResponseInventoryDescription));
        try {
            //获取磨损度
            BigDecimal exteriorValue = SteamUtil.getAssetExteriorValue(SteamUtil.getSteamIdByCookie(account), asset.getAssetid(),
                    steamAPIResponseInventoryDescription.getActions().get(0).getD());
            steamAccountAsset.setExteriorValue(exteriorValue);
        } catch (Exception e) {
            log.error("同步磨損度異常[" + asset.getAssetid() + "]");
        }
        steamAccountAsset.setCheckExist(EBoolean.YES.getCode());
        steamAccountAsset.setLastRefreshDatetime(new Date());

        //入库时间

        QueryWrapper<TSteamAccountAssetHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("assetid", asset.getAssetid());
        queryWrapper.eq("flow_direction", EAccountAssetFlowDirectionEnum.IN.getCode());
        queryWrapper.orderByDesc("trade_date");
        queryWrapper.last("LIMIT 1");
        TSteamAccountAssetHistory assetHistory = tSteamAccountAssetHistoryMapper.selectOne(queryWrapper);
        if (assetHistory != null) {
            steamAccountAsset.setStockInDatetime(assetHistory.getTradeDate());
            if (StringUtils.isNotBlank(assetHistory.getSteamId())) {
                steamAccountAsset.setSourceSteamId(assetHistory.getSteamId());
            }
        }
        this.saveOrUpdate(steamAccountAsset);
    }

    @Override
    public void doSyncAsset(TSteamAccount account) {
        //把所有该账号的资产check
        LambdaUpdateWrapper<TSteamAccountAsset> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TSteamAccountAsset::getAccountId, account.getId())
                .in(TSteamAccountAsset::getInventoryStatus, ESteamAssetStatusEnum.AVAILABLE.getCode(), ESteamAssetStatusEnum.COOLDOWN.getCode());

        TSteamAccountAsset update = new TSteamAccountAsset();
        update.setCheckExist(EBoolean.NO.getCode());
        this.update(update, updateWrapper);

        String startAssetid = "";
        int count = 200;

        int totalInventoryCount = 0;
        int totalFlag = 0;
        while (true) {
            SteamAPIResponseInventory inventory = SteamUtil.getInventoryByUserId(account, "730", String.valueOf(count), startAssetid, "schinese");
            if (null == inventory || CollectionUtils.isEmpty(inventory.getAssets())) {
                break;
            }

            if (totalInventoryCount == 0) {
                totalInventoryCount = inventory.getTotalInventoryCount();
            }

            Map<String, SteamAPIResponseInventoryDescription> descriptionMap = inventory.getDescriptions().stream()
                    .collect(
                            Collectors.toMap(steamAPIResponseInventoryDescription -> steamAPIResponseInventoryDescription.getClassid()
                                            + "_" + steamAPIResponseInventoryDescription.getInstanceid(),
                                    steamAPIResponseInventoryDescription -> steamAPIResponseInventoryDescription,
                                    (existing, replacement) -> existing));

            List<TSteamAccountAsset> accountAssetList = new ArrayList<>();

            for (SteamAPIResponseInventoryAsset asset : inventory.getAssets()) {
                SteamAPIResponseInventoryDescription steamAPIResponseInventoryDescription = descriptionMap.get(asset.getClassid().concat("_").concat(asset.getInstanceid()));

                if (null != steamAPIResponseInventoryDescription) {
                    buildAssetList(accountAssetList, asset, account, steamAPIResponseInventoryDescription);
                }
            }
            totalFlag = totalFlag + inventory.getAssets().size();

            if (CollectionUtils.isNotEmpty(accountAssetList)) {
                fetchExteriorValueConcurrently(accountAssetList, account, descriptionMap);
                tSteamAccountAssetMapper.upsertBatch(accountAssetList);
//                tSteamAccountAssetMapper.updateStockInDatetimeFromHistory(account.getId());
            }

            if (StringUtils.isBlank(inventory.getLastAssetid())) {
                break;
            }
            startAssetid = inventory.getLastAssetid();
        }

        if (totalFlag != totalInventoryCount) {
            //说明token失效了
            log.error("获取库存数据失败，总数量" + totalInventoryCount + ";返回数量" + totalFlag);
            SteamUtil.sendCookieValid(account.getId());

            return;
        }

        updateWrapper.eq(TSteamAccountAsset::getCheckExist, EBoolean.NO.getCode());
        update = new TSteamAccountAsset();
        update.setInventoryStatus(ESteamAssetStatusEnum.OUT_OF_STOCK.getCode());
        this.update(update, updateWrapper);

    }


    @Override
    public void getFirstAsset(TSteamAccount account, int count, String startAssetid) {
        SteamAPIResponseInventory inventory = SteamUtil.getInventoryByUserId(account, "730", String.valueOf(count), startAssetid, "schinese");
        if (CollectionUtils.isEmpty(inventory.getAssets())) {
            return;
        }

        Map<String, SteamAPIResponseInventoryDescription> descriptionMap = inventory.getDescriptions().stream()
                .collect(
                        Collectors.toMap(steamAPIResponseInventoryDescription -> steamAPIResponseInventoryDescription.getClassid()
                                        + "_" + steamAPIResponseInventoryDescription.getInstanceid(),
                                steamAPIResponseInventoryDescription -> steamAPIResponseInventoryDescription,
                                (existing, replacement) -> existing));

        List<TSteamAccountAsset> accountAssetList = new ArrayList<>();

        for (SteamAPIResponseInventoryAsset asset : inventory.getAssets()) {
            SteamAPIResponseInventoryDescription steamAPIResponseInventoryDescription = descriptionMap.get(asset.getClassid().concat("_").concat(asset.getInstanceid()));

            if (null != steamAPIResponseInventoryDescription) {
                buildAssetList(accountAssetList, asset, account, steamAPIResponseInventoryDescription);
            }
        }

        if (CollectionUtils.isNotEmpty(accountAssetList)) {
            fetchExteriorValueConcurrently(accountAssetList, account, descriptionMap);
            tSteamAccountAssetMapper.upsertBatch(accountAssetList);
        }
    }


    private void buildAssetList(List<TSteamAccountAsset> accountAssetList, SteamAPIResponseInventoryAsset asset, TSteamAccount account, SteamAPIResponseInventoryDescription steamAPIResponseInventoryDescription) {
        Date tradeStartDate = null;
        List<SteamAPIResponseInvestoryOwnerDescription> ownerDescriptions = steamAPIResponseInventoryDescription.getOwnerDescriptions();
        if (CollectionUtils.isNotEmpty(ownerDescriptions)) {
            for (SteamAPIResponseInvestoryOwnerDescription ownerDescription : ownerDescriptions) {
                // 遍历数组
                if (ownerDescription.getType().equals("html")) {
                    String value = ownerDescription.getValue();
                    // 提取时间部分 (如: 2024 12月 28 (8:00:00))
                    String regex = "(\\d{4} \\d{1,2}月 \\d{1,2} \\(\\d{1,2}:\\d{1,2}:\\d{1,2}\\))";
                    if (value.matches(".*" + regex + ".*")) {
                        // 使用正则表达式提取日期和时间
                        String timeString = value.replaceAll(".*(" + regex + ").*", "$1");

                        // 格式化时间字符串以便转换为 Date
                        // 需要将时间格式从 "2024 12月 28 (8:00:00)" 转换为 "2024-12-28 08:00:00"
                        timeString = timeString.replaceAll("(\\d{4}) (\\d{1,2})月 (\\d{1,2}) \\((\\d{1,2}:\\d{1,2}:\\d{1,2})\\)",
                                "$1-$2-$3 $4");
                        tradeStartDate = DateUtil.strToDate(timeString, DateUtil.YYYY_MM_DD_HH_MM_SS);
                        //根据当前时区设置交易开始时间
                        tradeStartDate = DateUtil.getRelativeDateOfSecond(tradeStartDate, DateUtil.getCurrentZoneUTCSecond());
                    }
                }
            }
        }

        TSteamAccountAsset steamAccountAsset = new TSteamAccountAsset();
        steamAccountAsset.setAssetid(asset.getAssetid());
        steamAccountAsset.setAccountId(account.getId());
        steamAccountAsset.setClassid(asset.getClassid());
        steamAccountAsset.setInstanceid(asset.getInstanceid());
        steamAccountAsset.setMarketName(steamAPIResponseInventoryDescription.getMarketName());
        steamAccountAsset.setMarketHashName(steamAPIResponseInventoryDescription.getMarketHashName());
        steamAccountAsset.setInventoryStatus(ESteamAssetStatusEnum.AVAILABLE.getCode());
        if (tradeStartDate != null) {
            steamAccountAsset.setInventoryStatus(ESteamAssetStatusEnum.COOLDOWN.getCode());
            steamAccountAsset.setTradeStartDatetime(tradeStartDate);
        }
        steamAccountAsset.setDescription(JSONUtil.toJsonStr(steamAPIResponseInventoryDescription));
        steamAccountAsset.setCheckExist(EBoolean.YES.getCode());
        steamAccountAsset.setLastRefreshDatetime(new Date());
        accountAssetList.add(steamAccountAsset);
    }

    @Override
    public void syncTradeOutHistory(TSteamAccount account) {
        account = tSteamAccountMapper.selectById(account.getId());

        Long startTime = 0L;
        Long existTime = 0L;

        if (StringUtils.isNotBlank(account.getAssetLastHistoryJson())) {
            JSONObject jsonObject = JSONUtil.parseObj(account.getAssetLastHistoryJson());
            if (jsonObject.containsKey("tradeHistoryId")) {
                startTime = jsonObject.getLong("tradeHistoryId");
                existTime = startTime;
            }
        }

        List<TSteamAccountAsset> accountAssetList = new ArrayList<>();
        List<TSteamAccountAssetHistory> historyList = new ArrayList<>();


        while (true) {
            SteamAPIResponseTradeHistory tradeHistory = SteamUtil.getTradeHistory(account, startTime);
            List<SteamAPIResponseTradeHistoryDetail> trades = tradeHistory.getTrades();
            if (CollectionUtils.isEmpty(trades)) {
                break;
            }
            for (int i = 0; i < trades.size(); i++) {
                SteamAPIResponseTradeHistoryDetail trade = trades.get(i);

                if (CollectionUtils.isEmpty(trade.getAssets_given())) {
                    continue;
                }


                String tradeid = trade.getTradeid();
                String steamid = trade.getSteamid_other();
                Long time_init = trade.getTime_init();
                if (i == 0) {
                    System.out.println(time_init);
                    startTime = time_init;
                }

                if (time_init < existTime) {
                    break;
                }

                Date trade_date = new Date(time_init * 1000);
//                SteamPlayerVo playerInfo = SteamUtil.getPlayerInfo(steamid);

                List<SteamAPIResponseInventoryAsset> assetsGiven = trade.getAssets_given();

                for (int x = 0; x < assetsGiven.size(); x++) {
                    SteamAPIResponseInventoryAsset steamAPIResponseInventoryAsset = assetsGiven.get(x);
                    String assetid = steamAPIResponseInventoryAsset.getAssetid();
                    TSteamAccountAsset accountAsset = new TSteamAccountAsset();
                    accountAsset.setAssetid(assetid);
                    accountAsset.setAccountId(account.getId());
                    accountAsset.setStockOutDatetime(trade_date);
                    accountAsset.setTargetSteamId(steamid);
                    accountAssetList.add(accountAsset);
                    //构建出库记录

                    TSteamAccountAssetHistory assetHistory = new TSteamAccountAssetHistory();
                    assetHistory.setHistoryItemId("history" + tradeid + "_item" + x);
                    assetHistory.setTradeDate(trade_date);
                    assetHistory.setTradeType("交易");
                    assetHistory.setDescription("与 steamId=[" + steamid + "]进行交易");
                    assetHistory.setName("");
                    assetHistory.setAssetid(assetid);
                    assetHistory.setClassid(steamAPIResponseInventoryAsset.getClassid());
                    assetHistory.setInstanceid(steamAPIResponseInventoryAsset.getInstanceid());
                    assetHistory.setSteamId(steamid);
                    assetHistory.setFlowDirection(com.aiyoooo.service.dao.enums.EAccountAssetFlowDirectionEnum.OUT.getCode());
                    assetHistory.setCreateDatetime(new Date());
                    assetHistory.setAccountId(account.getId());
                    historyList.add(assetHistory);
                }
            }


            if (!tradeHistory.getMore()) {
                break;
            }
        }

        if (CollectionUtils.isNotEmpty(accountAssetList)) {
            baseMapper.batchUpdateByAssetId(accountAssetList);
        }


        if (CollectionUtils.isNotEmpty(historyList)) {
            tSteamAccountAssetHistoryMapper.upsertBatch(historyList);
        }

        TSteamAccount updateAccount = new TSteamAccount();
        updateAccount.setId(account.getId());
        if (StringUtils.isBlank(account.getAssetLastHistoryJson())) {
            Map<String, String> jsonObject = new HashMap<>();
            jsonObject.put("tradeHistoryId", startTime + "");
            updateAccount.setAssetLastHistoryJson(JSONUtil.toJsonStr(jsonObject));
        } else {
            JSONObject jsonObject = JSONUtil.parseObj(account.getAssetLastHistoryJson());
            jsonObject.put("tradeHistoryId", startTime + "");
            updateAccount.setAssetLastHistoryJson(JSONUtil.toJsonStr(jsonObject));
        }
        tSteamAccountMapper.updateById(updateAccount);
        account.setAssetLastHistoryJson(updateAccount.toString());
    }

    /**
     * 获取磨损度
     *
     * @param accountAssetList
     * @param account
     * @param descriptionMap
     */
    public void fetchExteriorValueConcurrently(List<TSteamAccountAsset> accountAssetList, TSteamAccount account, Map<String, SteamAPIResponseInventoryDescription> descriptionMap) {
        int threadCount = Math.min(30, accountAssetList.size()); // 可根据实际调整线程数
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        List<Future<?>> futures = new ArrayList<>();

        for (TSteamAccountAsset asset : accountAssetList) {
            if (redisUtil.hasKey("asset:exterior:" + asset.getAssetid())) {
                asset.setExteriorValue(new BigDecimal(redisUtil.get("asset:exterior:" + asset.getAssetid()).toString()));
                continue;
            }

            futures.add(executor.submit(() -> {
                try {
                    SteamAPIResponseInventoryDescription desc = descriptionMap.get(asset.getClassid() + "_" + asset.getInstanceid());
                    if (desc != null && desc.getActions() != null && !desc.getActions().isEmpty()) {
                        BigDecimal exteriorValue = SteamUtil.getAssetExteriorValue(SteamUtil.getSteamIdByCookie(account), asset.getAssetid(), desc.getActions().get(0).getD());
                        asset.setExteriorValue(exteriorValue);
                        redisUtil.set("asset:exterior:" + asset.getAssetid(), exteriorValue, 3600 * 24 * 7);
                    }
                } catch (Exception e) {
                    // 建议加上资产ID日志
                    log.error("同步磨損度異常[" + asset.getAssetid() + "]");
                }
            }));
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                // 这里一般不会抛异常，除非线程池本身出错
                log.error("线程池任务异常", e);
            }
        }

        executor.shutdown();
    }

}