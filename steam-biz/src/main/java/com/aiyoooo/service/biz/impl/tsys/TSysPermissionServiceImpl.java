package com.aiyoooo.service.biz.impl.tsys;

import com.aiyoooo.service.biz.service.tsys.TSysPermissionService;
import com.aiyoooo.service.dao.entity.tsys.TSysPermission;
import com.aiyoooo.service.dao.mapper.TSysPermissionMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 系统权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class TSysPermissionServiceImpl extends ServiceImpl<TSysPermissionMapper, TSysPermission> implements TSysPermissionService {

    @Override
    public List<TSysPermission> getPermissionsByAuthCode(String authCode) {
        return baseMapper.selectPermissionsByAuthCode(authCode);
    }

    @Override
    public boolean hasPermission(String authCode, String interfacePath) {
        int count = baseMapper.checkPermission(authCode, interfacePath);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissions(String authCode, List<String> interfacePaths) {
        try {
            List<TSysPermission> permissions = new ArrayList<>();
            for (String interfacePath : interfacePaths) {
                // 检查是否已存在
                LambdaQueryWrapper<TSysPermission> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TSysPermission::getAuthCode, authCode)
                           .eq(TSysPermission::getInterfacePath, interfacePath);
                
                if (baseMapper.selectCount(queryWrapper) == 0) {
                    TSysPermission permission = new TSysPermission();
                    permission.setAuthCode(authCode);
                    permission.setInterfacePath(interfacePath);
                    permissions.add(permission);
                }
            }
            
            if (!permissions.isEmpty()) {
                int result = baseMapper.insertBatch(permissions);
                log.info("为授权码 [{}] 分配了 {} 个权限", authCode, result);
                return result > 0;
            }
            return true;
        } catch (Exception e) {
            log.error("分配权限失败: authCode={}, interfacePaths={}", authCode, interfacePaths, e);
            throw new RuntimeException("分配权限失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removePermissions(String authCode, List<String> interfacePaths) {
        try {
            LambdaQueryWrapper<TSysPermission> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TSysPermission::getAuthCode, authCode)
                       .in(TSysPermission::getInterfacePath, interfacePaths);
            
            int result = baseMapper.delete(queryWrapper);
            log.info("为授权码 [{}] 移除了 {} 个权限", authCode, result);
            return result > 0;
        } catch (Exception e) {
            log.error("移除权限失败: authCode={}, interfacePaths={}", authCode, interfacePaths, e);
            throw new RuntimeException("移除权限失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAllPermissions(String authCode) {
        try {
            LambdaQueryWrapper<TSysPermission> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TSysPermission::getAuthCode, authCode);
            
            int result = baseMapper.delete(queryWrapper);
            log.info("为授权码 [{}] 移除了所有权限，共 {} 个", authCode, result);
            return result >= 0;
        } catch (Exception e) {
            log.error("移除所有权限失败: authCode={}", authCode, e);
            throw new RuntimeException("移除所有权限失败: " + e.getMessage(), e);
        }
    }
} 