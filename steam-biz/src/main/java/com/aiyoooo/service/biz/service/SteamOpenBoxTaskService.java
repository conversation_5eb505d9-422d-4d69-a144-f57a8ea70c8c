package com.aiyoooo.service.biz.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.service.t.TSteamOpenBoxTaskDetailService;
import com.aiyoooo.service.biz.service.t.TSteamOpenBoxTaskService;
import com.aiyoooo.service.biz.task.TaskProducer;
import com.aiyoooo.service.biz.util.HttpUtil;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.util.RedisLock;
import com.aiyoooo.service.dao.dto.OpenBoxCallbackDto;
import com.aiyoooo.service.dao.dto.OpenCaseCreateDto2;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamOpenBoxTask;
import com.aiyoooo.service.dao.entity.t.TSteamOpenBoxTaskDetail;
import com.aiyoooo.service.dao.enums.EOpenBoxNotifyTypeEnum;
import com.aiyoooo.service.dao.enums.EOpenBoxTaskStatusEnum;
import com.aiyoooo.service.dao.enums.ESteamAssetStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 开箱任务管理服务
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Slf4j
@Service
public class SteamOpenBoxTaskService {

    @Resource
    private TSteamOpenBoxTaskService tSteamOpenBoxTaskService;

    @Resource
    private TSteamOpenBoxTaskDetailService tSteamOpenBoxTaskDetailService;

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountAssetService tSteamAccountAssetService;

    @Resource
    private TaskProducer taskProducer;

    @Resource
    private RedisLock redisLock;


    private static final int BATCH_SIZE = 10; // 每批次最多10个箱子

    /**
     * 创建开箱任务
     */
    @Transactional(rollbackFor = Exception.class)
    public TSteamOpenBoxTask createOpenBoxTask(OpenCaseCreateDto2 request) {
        // 验证账号
        TSteamAccount account = tSteamAccountService.getById(request.getAccountId());
        if (account == null) {
            throw new BizException("Steam账户不存在");
        }

        // 检查是否存在正在进行的开箱任务
        checkExistingOpenBoxTask(request.getAccountId());

        // 同步库存
        tSteamAccountAssetService.doSyncAsset(account);

        // 查询可用的箱子和钥匙
        List<TSteamAccountAsset> boxes = getAvailableAssetsByName(request.getAccountId(), request.getBoxName());
        List<TSteamAccountAsset> keys = getAvailableAssetsByName(request.getAccountId(), request.getKeyName());

        log.info("找到箱子数量: {}, 钥匙数量: {}", boxes.size(), keys.size());

        // 验证数量
        if (boxes.size() < request.getCount()) {
            throw new BizException("可用箱子数量不足，需要: " + request.getCount() + ", 实际: " + boxes.size());
        }
        if (keys.size() < request.getCount()) {
            throw new BizException("可用钥匙数量不足，需要: " + request.getCount() + ", 实际: " + keys.size());
        }

//        // 检查资产是否被其他任务占用
//        checkAssetConflicts(request.getAccountId(), boxes.subList(0, request.getCount()), keys.subList(0, request.getCount()));

        // 创建主任务
        TSteamOpenBoxTask task = new TSteamOpenBoxTask();
        task.setAccountId(request.getAccountId());
        task.setBoxName(request.getBoxName());
        task.setKeyName(request.getKeyName());
        task.setTotalCount(request.getCount());
        task.setSuccessCount(0);
        task.setFailedCount(0);
        task.setStatus(EOpenBoxTaskStatusEnum.PENDING.getCode());
        task.setCallbackUrl(request.getCallbackUrl());
        task.setCreateTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());

        tSteamOpenBoxTaskService.save(task);

        // 创建详情任务（分批）
        createTaskDetails(task.getId(), boxes, keys, request.getCount());

        log.info("创建开箱任务成功，任务ID: {}, 总数量: {}", task.getId(), request.getCount());

        return task;
    }

    /**
     * 根据名称查询可用资产
     */
    private List<TSteamAccountAsset> getAvailableAssetsByName(Long accountId, String assetName) {
        LambdaQueryWrapper<TSteamAccountAsset> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSteamAccountAsset::getAccountId, accountId)
                .eq(TSteamAccountAsset::getInventoryStatus, ESteamAssetStatusEnum.AVAILABLE.getCode())
                .eq(TSteamAccountAsset::getMarketName, assetName)
                .orderByAsc(TSteamAccountAsset::getAssetid);

        return tSteamAccountAssetService.list(queryWrapper);
    }

    /**
     * 创建任务详情（分批）
     */
    private void createTaskDetails(Long taskId, List<TSteamAccountAsset> boxes, List<TSteamAccountAsset> keys, Integer count) {
        List<TSteamOpenBoxTaskDetail> details = new ArrayList<>();
        int batchNo = 1;
        int currentIndex = 0;

        while (currentIndex < count) {
            int batchSize = Math.min(BATCH_SIZE, count - currentIndex);

            // 收集当前批次的箱子和钥匙ID
            List<String> batchCaseIds = new ArrayList<>();
            List<String> batchKeyIds = new ArrayList<>();

            for (int i = 0; i < batchSize; i++) {
                batchCaseIds.add(boxes.get(currentIndex + i).getAssetid());
                batchKeyIds.add(keys.get(currentIndex + i).getAssetid());
            }

            // 创建一条批次记录
            TSteamOpenBoxTaskDetail detail = new TSteamOpenBoxTaskDetail();
            detail.setTaskId(taskId);
            detail.setBatchNo(batchNo);
            detail.setBatchCount(batchSize);
            detail.setCaseAssetIds(String.join(",", batchCaseIds));
            detail.setKeyAssetIds(String.join(",", batchKeyIds));
            detail.setStatus(EOpenBoxTaskStatusEnum.PENDING.getCode());
            detail.setCreateTime(LocalDateTime.now());
            detail.setUpdateTime(LocalDateTime.now());

            details.add(detail);

            currentIndex += batchSize;
            batchNo++;
        }

        tSteamOpenBoxTaskDetailService.saveBatch(details);
    }


    /**
     * 处理单个任务
     */
    public void processTask(Long taskId) {
        log.info("开始处理开箱任务，任务ID: {}", taskId);
        TSteamOpenBoxTask task = tSteamOpenBoxTaskService.getById(taskId);

        long time = System.currentTimeMillis() + 180000;
        if (!redisLock.lock("steam:openbox:" + task.getAccountId(), String.valueOf(time))) {
            return;
        }

        try {
            // 查询任务详情，按批次分组处理
            LambdaQueryWrapper<TSteamOpenBoxTaskDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TSteamOpenBoxTaskDetail::getTaskId, task.getId())
                    .eq(TSteamOpenBoxTaskDetail::getStatus, EOpenBoxTaskStatusEnum.PENDING.getCode())
                    .orderByAsc(TSteamOpenBoxTaskDetail::getBatchNo);

            List<TSteamOpenBoxTaskDetail> details = tSteamOpenBoxTaskDetailService.list(queryWrapper);

            if (CollectionUtil.isEmpty(details)) {
                log.warn("任务详情为空，任务ID: {}", task.getId());
                return;
            }

            TSteamAccount account = tSteamAccountService.getById(task.getAccountId());

            // 逐个处理批次详情
            for (TSteamOpenBoxTaskDetail detail : details) {
                try {
                    List<TSteamAccountAsset> results = tSteamOpenBoxTaskDetailService.processBatchDetail(account, detail);
                    // 批次完成后立即发送通知
                    sendBatchCallback(task, detail, results);

                } catch (Exception e) {
                    log.error("处理批次失败，任务ID: {}, 批次: {}", task.getId(), detail.getBatchNo(), e);
                    // 标记批次为失败
                    detail.setStatus(EOpenBoxTaskStatusEnum.FAILED.getCode());
                    detail.setErrorMessage(e.getMessage());
                    detail.setUpdateTime(LocalDateTime.now());
                    detail.setFinishTime(LocalDateTime.now());
                    tSteamOpenBoxTaskDetailService.updateById(detail);

                    sendBatchCallback(task, detail, new ArrayList<>());
                }
            }

            // 检查任务是否完成
            checkTaskCompletion(task);

        } finally {
            redisLock.unlock("steam:openbox:" + task.getAccountId(), String.valueOf(time));
        }
    }


    /**
     * 检查任务完成状态
     */
    private void checkTaskCompletion(TSteamOpenBoxTask task) {
        // 统计任务详情状态
        LambdaQueryWrapper<TSteamOpenBoxTaskDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSteamOpenBoxTaskDetail::getTaskId, task.getId());

        List<TSteamOpenBoxTaskDetail> allDetails = tSteamOpenBoxTaskDetailService.list(queryWrapper);

        int success = 0;
        int failed = 0;
        List<TSteamAccountAsset> assets = new ArrayList<>();
        for (TSteamOpenBoxTaskDetail detail : allDetails) {
            if (EOpenBoxTaskStatusEnum.FAILED.getCode().equals(detail.getStatus())) {
                failed = failed + detail.getBatchCount();
            } else if (EOpenBoxTaskStatusEnum.COMPLETED.getCode().equals(detail.getStatus())) {
                success = success + detail.getResultAssetIds().split(",").length;
                List<TSteamAccountAsset> list = JSONUtil.parseArray(detail.getResultAssetInfo()).toList(TSteamAccountAsset.class);
                assets.addAll(list);
            }
        }
        // 更新任务统计
        task.setSuccessCount(success);
        task.setFailedCount(failed);
        task.setUpdateTime(LocalDateTime.now());
        task.setStatus(EOpenBoxTaskStatusEnum.COMPLETED.getCode());
        task.setFinishTime(LocalDateTime.now());
        tSteamOpenBoxTaskService.updateById(task);


        TSteamOpenBoxTaskDetail detail = new TSteamOpenBoxTaskDetail();
        detail.setBatchCount(task.getTotalCount());
        detail.setBatchNo(-1);
        sendBatchCallback(task, detail, assets);
    }

    /**
     * 发送批次完成的回调通知
     */
    private void sendBatchCallback(TSteamOpenBoxTask task, TSteamOpenBoxTaskDetail batchDetail, List<TSteamAccountAsset> batchResults) {
        try {
            log.info("发送批次回调通知，任务ID: {}, 批次: {}, 回调URL: {}",
                    task.getId(), batchDetail.getBatchNo(), task.getCallbackUrl());

            // 构建批次的回调数据
            OpenBoxCallbackDto callbackDto = new OpenBoxCallbackDto();
            callbackDto.setTaskId(task.getId());
            callbackDto.setBatchNo(batchDetail.getBatchNo());
            callbackDto.setSuccessCount(batchResults.size());
            callbackDto.setFailedCount(batchDetail.getBatchCount() - batchResults.size());
            callbackDto.setNotifyType(batchDetail.getBatchNo() == -1 ? EOpenBoxNotifyTypeEnum.TASK_COMPLETED.getCode() : EOpenBoxNotifyTypeEnum.BATCH_COMPLETED.getCode());

            JSONConfig config = new JSONConfig();
            config.setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

            Map<String, Object> map = new HashMap<>();
            map.put("code", 0);
            JSONObject data = JSONUtil.parseObj(callbackDto, config);


            JSONArray assetArray = new JSONArray();
            for (TSteamAccountAsset accountAsset : batchResults) {
                JSONObject asset = JSONUtil.parseObj(accountAsset, config);
                asset.put("description", JSONUtil.parseObj(accountAsset.getDescription()));
                assetArray.add(asset);
            }
            data.put("assets", assetArray);
            map.put("data", data);

            // 发送HTTP请求
            String response = HttpUtil.post(task.getCallbackUrl(), map);
            log.info("批次回调通知发送成功，任务ID: {}, 批次: {}, 参数：{},响应: {}",
                    task.getId(), batchDetail.getBatchNo(), JSONUtil.toJsonStr(map), response);

        } catch (Exception e) {
            log.error("发送批次回调通知失败，任务ID: {}, 批次: {}, 回调URL: {}",
                    task.getId(), batchDetail.getBatchNo(), task.getCallbackUrl(), e);
        }
    }


    /**
     * 检查账号是否存在正在进行的开箱任务
     */
    private void checkExistingOpenBoxTask(Long accountId) {
        LambdaQueryWrapper<TSteamOpenBoxTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSteamOpenBoxTask::getAccountId, accountId)
                .in(TSteamOpenBoxTask::getStatus,
                        EOpenBoxTaskStatusEnum.PENDING.getCode(),
                        EOpenBoxTaskStatusEnum.PROCESSING.getCode())
                .orderByDesc(TSteamOpenBoxTask::getCreateTime)
                .last("LIMIT 1");

        TSteamOpenBoxTask existingTask = tSteamOpenBoxTaskService.getOne(queryWrapper);

        if (existingTask != null) {
            log.warn("账号{}存在正在进行的开箱任务，任务ID: {}, 状态: {}",
                    accountId, existingTask.getId(), existingTask.getStatus());
            throw new BizException("该账号存在正在进行的开箱任务，请等待完成后再创建新任务");
        }
    }

    /**
     * 获取账号当前的开箱任务状态
     */
    public OpenBoxCallbackDto getCurrentOpenBoxTask(Long taskId) {
        TSteamOpenBoxTask task = tSteamOpenBoxTaskService.getById(taskId);

        if (task ==null){
            throw new BizException("任务不存在!");
        }
        LambdaQueryWrapper<TSteamOpenBoxTaskDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSteamOpenBoxTaskDetail::getTaskId, task.getId());
        List<TSteamOpenBoxTaskDetail> allDetails = tSteamOpenBoxTaskDetailService.list(queryWrapper);

        int success = 0;
        int failed = 0;
        List<TSteamAccountAsset> assets = new ArrayList<>();
        for (TSteamOpenBoxTaskDetail detail : allDetails) {
            if (EOpenBoxTaskStatusEnum.FAILED.getCode().equals(detail.getStatus())) {
                failed = failed + detail.getBatchCount();
            } else if (EOpenBoxTaskStatusEnum.COMPLETED.getCode().equals(detail.getStatus())) {
                success = success + detail.getResultAssetIds().split(",").length;
                List<TSteamAccountAsset> list = JSONUtil.parseArray(detail.getResultAssetInfo()).toList(TSteamAccountAsset.class);
                assets.addAll(list);
            }
        }

        OpenBoxCallbackDto callbackDto = new OpenBoxCallbackDto();
        callbackDto.setTaskId(task.getId());
        callbackDto.setBatchNo(-1);
        callbackDto.setSuccessCount(assets.size());
        callbackDto.setFailedCount(task.getTotalCount() - assets.size());
        callbackDto.setNotifyType(EOpenBoxNotifyTypeEnum.TASK_COMPLETED.getCode());
        callbackDto.setAssets(assets);
        return callbackDto;
    }


    /**
     * 检查资产是否被其他任务占用
     */
    private void checkAssetConflicts(Long accountId, List<TSteamAccountAsset> boxes, List<TSteamAccountAsset> keys) {
        // 收集所有要使用的资产ID
        List<String> allAssetIds = new ArrayList<>();
        boxes.forEach(box -> allAssetIds.add(box.getAssetid()));
        keys.forEach(key -> allAssetIds.add(key.getAssetid()));

        // 查询该账号正在进行的任务
        LambdaQueryWrapper<TSteamOpenBoxTask> taskQueryWrapper = new LambdaQueryWrapper<>();
        taskQueryWrapper.eq(TSteamOpenBoxTask::getAccountId, accountId)
                .in(TSteamOpenBoxTask::getStatus,
                        EOpenBoxTaskStatusEnum.PENDING.getCode(),
                        EOpenBoxTaskStatusEnum.PROCESSING.getCode());

        List<TSteamOpenBoxTask> activeTasks = tSteamOpenBoxTaskService.list(taskQueryWrapper);

        if (CollectionUtil.isEmpty(activeTasks)) {
            return;
        }

        // 查询这些任务的详情，检查是否有资产冲突
        List<Long> taskIds = activeTasks.stream().map(TSteamOpenBoxTask::getId).collect(Collectors.toList());

        LambdaQueryWrapper<TSteamOpenBoxTaskDetail> detailQueryWrapper = new LambdaQueryWrapper<>();
        detailQueryWrapper.in(TSteamOpenBoxTaskDetail::getTaskId, taskIds)
                .in(TSteamOpenBoxTaskDetail::getStatus,
                        EOpenBoxTaskStatusEnum.PENDING.getCode(),
                        EOpenBoxTaskStatusEnum.PROCESSING.getCode());

        List<TSteamOpenBoxTaskDetail> activeDetails = tSteamOpenBoxTaskDetailService.list(detailQueryWrapper);

        // 检查资产冲突
        for (TSteamOpenBoxTaskDetail detail : activeDetails) {
            List<String> usedAssetIds = new ArrayList<>();

            // 解析已占用的箱子ID
            if (detail.getCaseAssetIds() != null) {
                String[] caseIds = detail.getCaseAssetIds().split(",");
                for (String caseId : caseIds) {
                    usedAssetIds.add(caseId.trim());
                }
            }

            // 解析已占用的钥匙ID
            if (detail.getKeyAssetIds() != null) {
                String[] keyIds = detail.getKeyAssetIds().split(",");
                for (String keyId : keyIds) {
                    usedAssetIds.add(keyId.trim());
                }
            }

            // 检查是否有冲突
            for (String assetId : allAssetIds) {
                if (usedAssetIds.contains(assetId)) {
                    log.warn("资产冲突检测：资产ID {} 已被任务 {} 占用", assetId, detail.getTaskId());
                    throw new BizException("部分资产已被其他开箱任务占用，请稍后重试");
                }
            }
        }

        log.info("资产冲突检查通过，账号: {}, 资产数量: {}", accountId, allAssetIds.size());
    }
}
