package com.aiyoooo.service.biz.util;

import java.util.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class SteamSuardUtil {

    protected static String generateSteamGuardCode(String sharedSecret) throws Exception {
        long unixTime = System.currentTimeMillis() / 1000L / 30L;
        byte[] key = Base64.getDecoder().decode(sharedSecret);

        // HMAC-SHA1
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(new SecretKeySpec(key, "HmacSHA1"));
        byte[] hmac = mac.doFinal(longToBytes(unixTime));

        // Dynamic Truncation
        int offset = hmac[hmac.length - 1] & 0xf;
        int code = (hmac[offset] & 0x7f) << 24
                | (hmac[offset + 1] & 0xff) << 16
                | (hmac[offset + 2] & 0xff) << 8
                | (hmac[offset + 3] & 0xff);

        // Convert to Base32
        String characters = "23456789BCDFGHJKMNPQRTVWXY";
        StringBuilder steamGuard = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            steamGuard.append(characters.charAt(code % characters.length()));
            code /= characters.length();
        }
        return steamGuard.toString();
    }

    private static byte[] longToBytes(long val) {
        byte[] buffer = new byte[8];
        for (int i = 7; val != 0; i--) {
            buffer[i] = (byte) (val & 0xFF);
            val >>>= 8;
        }
        return buffer;
    }

    public static void main(String[] args) throws Exception {
        String sharedSecret = "XXXPUrKcAEgfwBXaQlpeuNc/DeI=";

        String steamGuardCode = generateSteamGuardCode(sharedSecret);

        System.out.println(steamGuardCode);
    }

}




