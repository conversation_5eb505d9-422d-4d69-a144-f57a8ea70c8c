package com.aiyoooo.service.biz.service.tsys;

import com.aiyoooo.service.dao.entity.tsys.SysApikey;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * apikey 配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface TsysApikeyService extends IService<SysApikey> {


    SysApikey getByApiKey(String authCode);

    SysApikey verifySignature(Object obj, String signed, String authCode);


    String generateMd5Signature(Object obj, String authCode);

}
