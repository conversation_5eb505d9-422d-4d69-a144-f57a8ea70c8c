package com.aiyoooo.service.biz.service;

import com.aiyoooo.service.biz.service.t.TSteamWalletHistoryService;
import com.aiyoooo.service.dao.entity.t.TSteamWalletHistory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Steam相关服务
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
@Service
public class SteamWalletHistoryService {


    @Resource
    private TSteamWalletHistoryService tSteamWalletHistoryService;

    public List<TSteamWalletHistory> list(Long id) {
        LambdaQueryWrapper<TSteamWalletHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSteamWalletHistory::getAccountId, id);
        return tSteamWalletHistoryService.list(wrapper);
    }
}
