package com.aiyoooo.service.biz.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.util.HttpUtil;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.vo.SteamDataRes;
import com.aiyoooo.service.dao.dto.*;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamOpenBoxTask;
import com.aiyoooo.service.dao.enums.ESteamAssetStatusEnum;
import com.aiyoooo.service.dao.vo.OpenCaseApiVo;
import com.aiyoooo.service.dao.vo.SteamAPIResponseInventory;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.aiyoooo.service.dao.vo.SteamAccountAssetVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/25 12:46
 */
@Slf4j
@Service
public class SteamAccountAssetService {

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountAssetService tSteamAccountAssetService;

    @Resource
    private SteamOpenBoxTaskService steamOpenBoxTaskService;

    @Value("${open-case-url}")
    private String openCaseUrl;

    /**
     * 获取库存列表
     */
    public SteamAPIResponseInventory queryInventoryList(SteamAccountAssetInventoryDto request) {
        TSteamAccount tSteamAccount = tSteamAccountService.getById(request.getAccountId());
        if (tSteamAccount == null) {
            throw new BizException("steam账户不存在");
        }

        return SteamUtil.getInventoryByUserId(tSteamAccount, request.getAppid(), request.getCount(), request.getStartAssetid(),
                request.getLanguage());
    }

    public SteamAccountAssetVo queryAssetList(SteamAccountAssetDto request) {
        TSteamAccount tSteamAccount = tSteamAccountService.getById(request.getAccountId());
        if (tSteamAccount == null) {
            throw new BizException("steam账户不存在");
        }

        //实时获取下库存
        tSteamAccountAssetService.doSyncAsset(tSteamAccount);

        // 构建查询条件
        LambdaQueryWrapper<TSteamAccountAsset> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSteamAccountAsset::getAccountId, request.getAccountId());
        queryWrapper.in(TSteamAccountAsset::getInventoryStatus,
                ESteamAssetStatusEnum.AVAILABLE.getCode(),
                ESteamAssetStatusEnum.COOLDOWN.getCode());
        queryWrapper.orderByDesc(TSteamAccountAsset::getAssetid);

        // 如果有lastAssetid，则添加游标条件
        if (StringUtils.isNotBlank(request.getStartAssetid())) {
            queryWrapper.lt(TSteamAccountAsset::getAssetid, request.getStartAssetid());
        }

        // 执行分页查询
        Page<TSteamAccountAsset> page = new Page<>(1, request.getCount());
        Page<TSteamAccountAsset> resultPage = tSteamAccountAssetService.page(page, queryWrapper);

        // 构建返回结果
        SteamAccountAssetVo vo = new SteamAccountAssetVo();
        vo.setAssets(resultPage.getRecords());
        vo.setTotalCount((int) resultPage.getTotal());

        boolean hasMore = resultPage.getCurrent() < resultPage.getPages();
        vo.setHasMore(hasMore);

        // 设置lastAssetid
        if (!resultPage.getRecords().isEmpty()) {
            vo.setLastAssetid(resultPage.getRecords().get(resultPage.getRecords().size() - 1).getAssetid());
        }

        return vo;
    }


    public List<TSteamAccountAsset> getAssetListByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TSteamAccountAsset> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TSteamAccountAsset::getAssetid, ids);
        return tSteamAccountAssetService.list(queryWrapper);

    }

    @SneakyThrows
    public List<TSteamAccountAsset> openCsgoCases(OpenCaseCreateDto request) {
        TSteamAccount tSteamAccount = tSteamAccountService.getById(request.getAccountId());

        tSteamAccountAssetService.doSyncAsset(tSteamAccount);

        List<Long> idList = new ArrayList<>();
        for (CaseOpenApiRequestItem caseOpenApiRequestItem : request.getItemList()) {
            idList.add(caseOpenApiRequestItem.getCaseId());
            idList.add(caseOpenApiRequestItem.getKeyId());
        }
        List<TSteamAccountAsset> tSteamAccountAssets = getAssetListByIds(idList);
        if (tSteamAccountAssets.size() != idList.size()) {
            log.info("开箱失败,箱子不存在,{},{}", tSteamAccount.getSteamAccount(), JSONUtil.toJsonStr(idList));
            throw new BizException("箱子不存在");
        }

        for (TSteamAccountAsset tSteamAccountAsset : tSteamAccountAssets) {
            if (!tSteamAccountAsset.getInventoryStatus().equals(ESteamAssetStatusEnum.AVAILABLE.getCode())) {
                log.info("开箱失败,资产状态错误,{},{}", tSteamAccount.getSteamAccount(), JSONUtil.toJsonStr(idList));
                throw new BizException("资产状态错误");
            }
        }

        SteamDataRes steamInfo = SteamUtil.getSteamInfo(tSteamAccount.getMafile());
        Map<String, Object> map = new HashMap<>();
        map.put("username", tSteamAccount.getSteamAccount());
        map.put("password", tSteamAccount.getSteamPwd());
        map.put("sharedSecret", steamInfo.getSharedSecret());
        map.put("caseRequests", request.getItemList());

        String res = HttpUtil.post(openCaseUrl, map);
        JSON json = new JSONObject(res);
        OpenCaseApiVo caseApiVo = json.toBean(OpenCaseApiVo.class);

        if (!caseApiVo.getSuccess()) {
            log.info("开箱失败,请稍后再试,{},{}", tSteamAccount.getSteamAccount(), JSONUtil.toJsonStr(idList));
            throw new BizException("开箱失败,请稍后再试!");
        }

        List<Long> obtainedAssetIds = caseApiVo.getObtainedAssetIds();
        List<TSteamAccountAsset> assetList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            tSteamAccountAssetService.getFirstAsset(tSteamAccount, request.getItemList().size() + 10, "");
            assetList = getAssetListByIds(obtainedAssetIds);
            if (assetList.size() != obtainedAssetIds.size()) {
                Thread.sleep(1000);
            } else {
                break;
            }
        }

        log.info("开箱成功{},{}", tSteamAccount.getSteamAccount(), JSONUtil.toJsonStr(assetList.stream().map(TSteamAccountAsset::getAssetid).collect(Collectors.toList())));

        return assetList;
    }

    public static void main(String[] args) {
        String res = "{\"success\":true,\"obtainedAssetIds\":[***********],\"errorMessage\":null,\"errorCode\":null,\"processedCount\":1,\"successfulCount\":1,\"timestamp\":\"2025-08-14T16:14:43.9186836Z\"}";
        JSON json = new JSONObject(res);
        OpenCaseApiVo caseApiVo = json.toBean(OpenCaseApiVo.class);

        if (!caseApiVo.getSuccess()) {
            throw new BizException("开箱失败,请稍后再试!");
        }
        System.out.println(JSONUtil.toJsonStr(caseApiVo));
    }
}

    
    