package com.aiyoooo.service.biz.impl.t;

import cn.hutool.json.JSONObject;
import com.aiyoooo.service.biz.service.t.TSteamAccountInfoService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.task.TaskProducer;
import com.aiyoooo.service.biz.util.SteamSessionUtil;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.vo.SteamDataRes;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountInfo;
import com.aiyoooo.service.dao.enums.EBoolean;
import com.aiyoooo.service.dao.enums.ECookieFlag;
import com.aiyoooo.service.dao.enums.ESteamAccountStatus;
import com.aiyoooo.service.dao.mapper.TSteamAccountMapper;
import com.aiyoooo.service.dao.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * steam账号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Slf4j
@Service
public class TSteamAccountServiceImpl extends ServiceImpl<TSteamAccountMapper, TSteamAccount> implements TSteamAccountService {

    @Resource
    @Lazy
    private TaskProducer taskProducer;

    @Resource
    private TSteamAccountInfoService steamAccountInfoService;

    /**
     * 根据steam账号获取账号信息
     *
     * @param authCode
     * @param steamAccount Steam账号
     * @return 账号对象
     */
    @Override
    public TSteamAccount getBySteamAccount(String authCode, String steamAccount) {
        return this.getOne(new LambdaQueryWrapper<TSteamAccount>().eq(TSteamAccount::getSteamAccount, steamAccount).eq(TSteamAccount::getAuthCode,authCode));
    }

    @Override
    public void doSyncSession(TSteamAccount tSteamAccount) {
        try {
            //1.如果refreshToken=null，说明首次，登录处理
            //2.如果有refreshToken
            // 如果refreshToken != -1，就刷新下，如果不成功，就直接登录，登录不成功放到待处理池
            // 如果refreshToken = -1，就直接登录，登录不成功放到待处理池
            if (StringUtils.isBlank(tSteamAccount.getLastSessionid())) {
                firstLogin(tSteamAccount);
            } else if (StringUtils.isNotBlank(tSteamAccount.getRefreshToken())) {
                if("-1".equals(tSteamAccount.getRefreshToken())){
                    firstLogin(tSteamAccount);
                } else {
                    doRefreshToken(tSteamAccount);
                }
            }

        } catch (Exception e) {
            log.error("账号{}错误{},重置cookieFlag=2", tSteamAccount.getSteamAccount(), e.getMessage());

            TSteamAccount errData = new TSteamAccount();
            errData.setId(tSteamAccount.getId());
            errData.setCookieFlag(ECookieFlag.INVALID.getCode());
            errData.setUpdatedAt(LocalDateTime.now());
            errData.setRemark(e.getMessage());
            updateById(errData);
        }
    }

    private void firstLogin(TSteamAccount tSteamAccount) {
        SteamDataRes mafileDataVo = null;
        if (StringUtils.isNotBlank(tSteamAccount.getMafile())) {
            mafileDataVo = SteamUtil.getSteamInfo(tSteamAccount.getMafile());
        } else if (StringUtils.isNotBlank(tSteamAccount.getMafileDepart())) {
            mafileDataVo = SteamUtil.getSteamInfo(tSteamAccount.getMafileDepart());
        }

        if (mafileDataVo == null) {
            return;
        }
        if (!mafileDataVo.getAccountName().equals(tSteamAccount.getSteamAccount())) {
            throw new BizException("账号" + tSteamAccount.getSteamAccount() + "令牌和账号不对应");
        }

        LoginSteamDataDetailVo loginSteamDataDetailVo = SteamSessionUtil
                .getLoginSteam(tSteamAccount.getSteamAccount(), tSteamAccount.getSteamPwd(), mafileDataVo.getSharedSecret(), null);

        TSteamAccount data = new TSteamAccount();
        data.setId(tSteamAccount.getId());
        data.setAccessToken(loginSteamDataDetailVo.getAccessToken());
        data.setRefreshToken(loginSteamDataDetailVo.getRefreshToken());
        data.setLastSessionid(loginSteamDataDetailVo.getSessionId());
        data.setLastCookie(String.join(";", loginSteamDataDetailVo.getWebCookies()));

        data.setCookieFlag(ECookieFlag.VALID.getCode());
        data.setUpdatedAt(LocalDateTime.now());
        updateById(data);

        //初始化所有账号相关任务
        //taskProducer.initAccount(data);
    }

    @Override
    public TSteamAccount firstTokenLogin(TSteamAccount tSteamAccount, String tokenCode) {
        LoginSteamDataDetailVo loginSteamDataDetailVo = SteamSessionUtil
                .getLoginSteamByTokenCode(tSteamAccount.getSteamAccount(), tSteamAccount.getSteamPwd(), tokenCode,
                        null);


        TSteamAccount data = new TSteamAccount();
        data.setId(tSteamAccount.getId());
        data.setAccessToken(loginSteamDataDetailVo.getAccessToken());
        data.setRefreshToken(loginSteamDataDetailVo.getRefreshToken());
        data.setLastSessionid(loginSteamDataDetailVo.getSessionId());
        data.setLastCookie(String.join(";", loginSteamDataDetailVo.getWebCookies()));
        data.setCookieFlag(ECookieFlag.VALID.getCode());
        data.setUpdatedAt(LocalDateTime.now());

        updateById(data);

        return data;
    }

    @Override
    public void doSyncTradeUrl(TSteamAccount account) {
        String tradeUrl = SteamUtil.getTradeUrl(account);
        if (StringUtils.isNotBlank(tradeUrl)) {
            TSteamAccountInfo data = new TSteamAccountInfo();
            data.setAccountId(account.getId());
            data.setTradeUrl(tradeUrl);
            data.setSteamId(Objects.requireNonNull(SteamUtil.getSteamIdByTradeUrl(tradeUrl)).toString());
            steamAccountInfoService.saveOrUpdate(data);
        }
    }

    @Override
    public void doSyncSteamAccountBaseInfo(TSteamAccount account) {
        if(!EBoolean.YES.getCode().equals(account.getCookieFlag())){
            return;
        }

        TSteamAccountInfo update = new TSteamAccountInfo();
        update.setAccountId(account.getId());
        String steamId = SteamUtil.getSteamIdByCookie(account);
        update.setSteamId(steamId);
        try {
            SteamBalanceVo accountBalance = SteamUtil.getAccountBalance(account);
            update.setBalance(accountBalance.getBalance());
            update.setCurrency(accountBalance.getCurrency());
            update.setSteamEmail(accountBalance.getEmail());
            update.setProtectionType(accountBalance.getProtectType());
            update.setRegion(accountBalance.getPlace());

            Thread.sleep(2000);
        } catch (Exception e) {
            log.error("{}获取余额失败", account.getId());
        }

        try {
            SteamPlayerVo playerInfo = SteamUtil.getPlayerInfo(steamId);
            update.setPersonastate(playerInfo.getPersonastate());
            update.setNickname(playerInfo.getPersonaname());
            update.setAvatarUrl(playerInfo.getAvatarfull());
            update.setTimecreated(playerInfo.getTimecreated());

            Thread.sleep(2000);
        } catch (Exception e) {
            log.error("{}获取基本信息失败", account.getId());
        }
        try {
            JSONObject badges = SteamUtil.getBadges(steamId);
            update.setAccountLevel(badges.getJSONObject("response").getStr("player_level"));

            Thread.sleep(2000);
        } catch (Exception e) {
            log.error("{}获取账号等级失败", account.getId());
        }

        try {
            JSONObject playerBans = SteamUtil.getPlayerBans(steamId);
//            update.setHasRedFlag(playerBans.getInt("NumberOfGameBans") > 0);
            if(null !=playerBans){
                update.setHasRedFlag(playerBans.getBool("CommunityBanned"));
            }
//
        } catch (Exception e) {
            log.error("{}获取红信失败", account.getId());
        }

        steamAccountInfoService.saveOrUpdate(update);
    }

    @Override
    public void doSyncApikey(TSteamAccount account) {
        try {
            String apiKey = SteamUtil.getApiKey(account);
            if (StringUtils.isBlank(apiKey)) {
                return;
            }
            TSteamAccountInfo update = new TSteamAccountInfo();
            update.setAccountId(account.getId());
            update.setApikey(apiKey);
            steamAccountInfoService.saveOrUpdate(update);
        } catch (Exception e) {
            log.error("{}获取apikey失败",account.getId());
        }
    }


    @Override
    public void doRefreshToken(TSteamAccount tSteamAccount) {
        TSteamAccount data = new TSteamAccount();
        data.setId(tSteamAccount.getId());
        try {
            LoginSteamTokenDetailVo tokenDetailVo = SteamSessionUtil
                    .getAccessTokenByRefreshToken(tSteamAccount.getSteamAccount(), tSteamAccount.getRefreshToken(), null);

            data.setAccessToken(tokenDetailVo.getAccessToken());
            data.setRefreshToken(tokenDetailVo.getRefreshToken());
            StringBuilder sb = new StringBuilder();

            sb.append("steamLoginSecure=").append(SteamUtil.getSteamIdByCookie(tSteamAccount)).append("%7C%7C").append(data.getAccessToken())
                    .append(";sessionid=").append(tSteamAccount.getLastSessionid());
            data.setLastCookie(sb.toString());
            data.setCookieFlag(ECookieFlag.VALID.getCode());
            data.setUpdatedAt(LocalDateTime.now());
            updateById(data);

        } catch (Exception e) {
            log.error("刷新token错误，账号id[{}],原因[{}]", data.getId(), e.getMessage());
            data.setCookieFlag(ECookieFlag.TODO.getCode());
            data.setRefreshToken("-1");
            data.setUpdatedAt(LocalDateTime.now());
            updateById(data);
            log.info("steam账号[{}],刷新token失败", tSteamAccount.getSteamAccount());

//            try {
//                Thread.sleep(2000);
//            } catch (Exception e1) {
//            }
        }
    }

    public static void main(String[] args) {
        LoginSteamTokenDetailVo tokenDetailVo = SteamSessionUtil
                .getAccessTokenByRefreshToken("xxx", "eyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8hirHNxY9FSvLFlatW1OBHMgkGX-qa_G4evH30D8LwuDCfBk6adrK4u0EYRMvO2uc_FswFn1lfu7JeSvS8G3BQ", null);

        System.out.println(tokenDetailVo);

    }

}
