package com.aiyoooo.service.biz.util;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.Collection;
import java.util.List;

public class SteamMobileConf {

    private static final String IDENTITY_SECRET = "Iql95wZlRTr8i2zsolzRmYXwRBI="; // 你的 identity_secret
    private static final String STEAM_ID = "76561199809930905"; // 你的 64 位 SteamID

    public static void main(String[] args) throws Exception {

        String cookie = "sessionid=1ea3aa3477640695260d89e9; recentlyVisitedAppHubs=730; browserid=72726977784684187; 730_17workshopQueueTime=1744252036; app_impressions=730@2_100100_100101_100106|730@2_9_100006_|730@2_9_100000_|730@2_9_100000_|730@2_9_100000_|730@2_9_100006_|730@2_9_screenshots_|730@2_9_100001_|730@2_9_100014_|730@2_9_100002_|730@2_9_100013_; webTradeEligibility=%7B%22allowed%22%3A1%2C%22allowed_at_time%22%3A0%2C%22steamguard_required_days%22%3A15%2C%22new_device_cooldown_days%22%3A0%2C%22time_checked%22%3A1745406381%7D; strResponsiveViewPrefs=touch; strInventoryLastContext=730_2; steamLoginSecure=76561199809930905%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.up7euAVh9Lm54fCTMC6XYohux963EpBDkjLyybifes57k5CTqoBI7S_qI5gygCJk3LB7x2GnUm0o0T2d9BsoDw; steamCountry=JP%7Cc94e2688e638e83288cad24800bad66c";

        // 获取确认列表
        SteamMobileConfirmations.ConfirmationListRes confirmationList = SteamMobileConfirmations.getConfirmationList(STEAM_ID, IDENTITY_SECRET, cookie);
        System.out.println("Confirmations List: " + JSONUtil.toJsonStr(confirmationList));

        //| 字段 | 含义 |
        //| type | 类型 (9 代表 API Key 注册确认) |
        //| type_name | 确认类型名称：注册 API Key |
        //| id | 确认ID (用于后续确认或取消) |
        //| creator_id | 创建者ID (用于标识确认内容的发起者) |
        //| nonce | 随机确认 Token，用于发送确认请求 |
        //| creation_time | 确认请求生成的时间戳 |
        //| cancel | 拒绝按钮名称“Deny" |
        //| accept | 接受按钮名称“Confirm” |
        //| icon | 确认图标 URL |
        //| headline | 标题“Authorize a developer APl Key with access to your account" |
        //| summary | 摘要信息 |
        //| warn | 警告信息 (无) |

        List<SteamMobileConfirmations.ConfirmationItem> list = confirmationList.getConf();

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 示例确认ID 和 Nonce (从 responseList 中解析出来)
        String confirmationId = list.get(0).getId();
        String confirmationNonce = list.get(0).getNonce();

//        // 执行确认
        SteamMobileConfirmations.ConfirmationActionRes actionRes = SteamMobileConfirmations.confirmAction(STEAM_ID, IDENTITY_SECRET, cookie, confirmationId, confirmationNonce, true);
        System.out.println("Confirm Response: " + JSONUtil.toJsonStr(actionRes));

        // 或者执行拒绝
        // String cancelResponse = SteamMobileConfirmations.confirmAction(STEAM_ID, IDENTITY_SECRET, cookie, confirmationId, confirmationNonce, false);
        // System.out.println("Cancel Response: " + cancelResponse);


    }

    // 生成 Device ID
    public static String generateDeviceId(String steamId) throws Exception {
        String input = "android:" + steamId;
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(input.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b & 0xff));
        }
        return sb.toString();
    }

    // 生成 Confirmation Key (HMAC-SHA1)
    public static String generateConfirmationKey(String identitySecret, String tag, long timestamp) throws Exception {
        byte[] secretBytes = Base64.getDecoder().decode(identitySecret);
        byte[] data = new byte[8 + tag.length()];
        long time = timestamp;

        for (int i = 7; i >= 0; i--) {
            data[i] = (byte) (time & 0xff);
            time >>= 8;
        }

        byte[] tagBytes = tag.getBytes("UTF-8");
        System.arraycopy(tagBytes, 0, data, 8, tagBytes.length);

        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(secretBytes, "HmacSHA1");
        mac.init(keySpec);
        byte[] hmac = mac.doFinal(data);

        return Base64.getEncoder().encodeToString(hmac);
    }
}