package com.aiyoooo.service.biz.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 任务管理器 - 负责管理所有任务消费者的生命周期
 * 解决线程泄漏问题，提供优雅的启动和关闭机制
 */
@Component
@Slf4j
public class TaskManager implements ApplicationListener<ContextClosedEvent> {

    @Resource
    private TaskConsumer taskConsumer;

    @Value("${task.consumer-count:2}")
    private Integer consumerCount;

    @Value("${task.shutdown-timeout:30}")
    private Integer shutdownTimeoutSeconds;

    private ExecutorService executorService;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean isShuttingDown = new AtomicBoolean(false);

    @PostConstruct
    public void startTaskConsumers() {
        if (isRunning.compareAndSet(false, true)) {
            log.info("启动任务管理器，消费者数量: {}", consumerCount);
            
            // 创建线程池，使用自定义线程工厂
            ThreadFactory threadFactory = new ThreadFactory() {
                private int counter = 0;
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "TaskConsumer-" + (++counter));
                    thread.setDaemon(false); // 设置为非守护线程
                    return thread;
                }
            };
            
            executorService = Executors.newFixedThreadPool(consumerCount * 4, threadFactory);
            
            // 启动各种类型的消费者
            for (int i = 0; i < consumerCount; i++) {
                submitConsumerTask("CookieSync", () -> taskConsumer.consumeCookieSyncTasks());
                submitConsumerTask("InventorySync", () -> taskConsumer.consumeInventorySyncTasks());
                submitConsumerTask("OfferSync", () -> taskConsumer.consumeOfferSyncTasks());
                submitConsumerTask("OpenBox", () -> taskConsumer.consumeOpenBoxTasks());
                
                // 可以根据需要启用其他消费者
                // submitConsumerTask("BaseInfoSync", () -> taskConsumer.consumeBaseInfoSyncTasks());
                // submitConsumerTask("RefreshToken", () -> taskConsumer.consumeRefreshTokenTask());
                // submitConsumerTask("TradeUrlSync", () -> taskConsumer.consumeTradeUrlSyncTasks());
                // submitConsumerTask("ApiKeySync", () -> taskConsumer.consumeApiKeySyncTasks());
                // submitConsumerTask("WalletSync", () -> taskConsumer.consumeWalletSyncTasks());
            }
            
            log.info("任务管理器启动完成，总线程数: {}", consumerCount * 4);
        }
    }

    private void submitConsumerTask(String taskName, Runnable task) {
        executorService.submit(() -> {
            try {
                log.info("启动{}消费者线程: {}", taskName, Thread.currentThread().getName());
                task.run();
            } catch (Exception e) {
                log.error("{}消费者线程异常退出", taskName, e);
            } finally {
                log.info("{}消费者线程已退出: {}", taskName, Thread.currentThread().getName());
            }
        });
    }

    /**
     * 优雅关闭任务管理器
     */
    public void shutdown() {
        if (isShuttingDown.compareAndSet(false, true)) {
            log.info("开始关闭任务管理器...");

            // 1. 停止所有消费者
            log.info("步骤1: 发送停止信号给所有消费者");
            taskConsumer.stopAllConsumers();

            if (executorService != null && !executorService.isShutdown()) {
                // 2. 停止接收新任务
                log.info("步骤2: 停止线程池接收新任务");
                executorService.shutdown();

                try {
                    // 3. 等待现有任务完成
                    log.info("步骤3: 等待任务完成，最多等待{}秒...", shutdownTimeoutSeconds);
                    if (!executorService.awaitTermination(shutdownTimeoutSeconds, TimeUnit.SECONDS)) {
                        log.warn("任务未能在{}秒内完成，开始强制关闭", shutdownTimeoutSeconds);

                        // 4. 强制中断所有线程
                        log.info("步骤4: 强制中断所有任务线程");
                        taskConsumer.forceStopAllConsumers();
                        executorService.shutdownNow();

                        // 5. 再等待一段时间确保强制关闭完成
                        if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                            log.error("强制关闭任务管理器失败，可能存在无法中断的线程");
                            // 记录当前活跃线程信息用于调试
                            logActiveThreads();
                        } else {
                            log.info("任务管理器强制关闭完成");
                        }
                    } else {
                        log.info("任务管理器优雅关闭完成");
                    }
                } catch (InterruptedException e) {
                    log.error("等待任务管理器关闭时被中断", e);
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            isRunning.set(false);
            log.info("任务管理器已关闭");
        }
    }

    /**
     * 记录活跃线程信息用于调试
     */
    private void logActiveThreads() {
        ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
        ThreadGroup parentGroup;
        while ((parentGroup = rootGroup.getParent()) != null) {
            rootGroup = parentGroup;
        }

        Thread[] threads = new Thread[rootGroup.activeCount()];
        int count = rootGroup.enumerate(threads);

        log.warn("当前活跃线程数: {}", count);
        for (int i = 0; i < count; i++) {
            Thread thread = threads[i];
            if (thread != null && thread.getName().contains("TaskConsumer")) {
                log.warn("发现未关闭的任务消费线程: {} - 状态: {} - 是否守护线程: {}",
                    thread.getName(), thread.getState(), thread.isDaemon());
            }
        }
    }

    /**
     * 监听应用关闭事件
     */
    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("检测到应用关闭事件，开始关闭任务管理器");
        shutdown();
    }

    /**
     * 获取任务管理器状态
     */
    public boolean isRunning() {
        return isRunning.get() && !isShuttingDown.get();
    }

    /**
     * 获取线程池状态信息
     */
    public String getStatus() {
        if (executorService == null) {
            return "未初始化";
        }
        
        if (executorService instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) executorService;
            return String.format("线程池状态 - 核心线程数: %d, 活跃线程数: %d, 任务队列大小: %d, 已完成任务数: %d",
                    tpe.getCorePoolSize(),
                    tpe.getActiveCount(),
                    tpe.getQueue().size(),
                    tpe.getCompletedTaskCount());
        }
        
        return "运行中";
    }

    /**
     * 重启任务管理器（谨慎使用）
     */
    public synchronized void restart() {
        log.info("重启任务管理器...");
        shutdown();
        
        // 等待完全关闭
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 重置状态
        isShuttingDown.set(false);
        startTaskConsumers();
    }
}
