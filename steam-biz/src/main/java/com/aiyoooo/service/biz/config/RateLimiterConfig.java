package com.aiyoooo.service.biz.config;

import com.aiyoooo.service.biz.util.RateLimiterUtil;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 限流器配置类
 */
@Configuration
public class RateLimiterConfig {

    // Steam API 限流器标识
    public static final String STEAM_API_LIMITER = "steam_api";
    public static final String STEAM_INVENTORY_LIMITER = "steam_inventory";
    public static final String STEAM_WALLET_LIMITER = "steam_wallet";
    public static final String STEAM_TRADE_LIMITER = "steam_trade";
    public static final String STEAM_INVENTORY_HISTORY_LIMITER = "steam_inventory_history";

    @PostConstruct
    public void init() {
        // Steam API 限流配置
        RateLimiterUtil.createLimiter(STEAM_API_LIMITER, 1.0); // 每秒1个请求
        RateLimiterUtil.createLimiter(STEAM_INVENTORY_LIMITER, 1.0); // 每秒1个请求
        RateLimiterUtil.createLimiter(STEAM_WALLET_LIMITER, 1.0); // 每秒1个请求
        RateLimiterUtil.createLimiter(STEAM_TRADE_LIMITER, 1.0); // 每秒1个请求
        RateLimiterUtil.createLimiter(STEAM_INVENTORY_HISTORY_LIMITER, 0.5); // 每2秒1个请求
    }
} 