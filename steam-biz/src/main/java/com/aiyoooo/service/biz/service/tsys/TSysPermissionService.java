package com.aiyoooo.service.biz.service.tsys;

import com.aiyoooo.service.dao.entity.tsys.TSysPermission;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 系统权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface TSysPermissionService extends IService<TSysPermission> {

    /**
     * 根据授权码获取权限列表
     * @param authCode 授权码
     * @return 权限列表
     */
    List<TSysPermission> getPermissionsByAuthCode(String authCode);

    /**
     * 检查授权码是否有指定接口的权限
     * @param authCode 授权码
     * @param interfacePath 接口路径
     * @return 是否有权限
     */
    boolean hasPermission(String authCode, String interfacePath);

    /**
     * 为授权码分配权限
     * @param authCode 授权码
     * @param interfacePaths 接口路径列表
     * @return 是否成功
     */
    boolean assignPermissions(String authCode, List<String> interfacePaths);

    /**
     * 移除授权码的权限
     * @param authCode 授权码
     * @param interfacePaths 接口路径列表
     * @return 是否成功
     */
    boolean removePermissions(String authCode, List<String> interfacePaths);

    /**
     * 移除授权码的所有权限
     * @param authCode 授权码
     * @return 是否成功
     */
    boolean removeAllPermissions(String authCode);
} 