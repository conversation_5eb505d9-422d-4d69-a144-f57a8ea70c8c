package com.aiyoooo.service.biz.service.t;

import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.vo.SteamAPIResponseInventoryAsset;
import com.aiyoooo.service.dao.vo.SteamAPIResponseInventoryDescription;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * steam账号资产 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface TSteamAccountAssetService extends IService<TSteamAccountAsset> {


    void doSaveAsset(SteamAPIResponseInventoryAsset asset, TSteamAccount account, SteamAPIResponseInventoryDescription steamAPIResponseInventoryDescription);


    void doSyncAsset(TSteamAccount account);

    /**
     * 拆分后的：同步订单交易出库历史
     */
    void syncTradeOutHistory(TSteamAccount account);


    void getFirstAsset(TSteamAccount account, int count, String startAssetid);
}