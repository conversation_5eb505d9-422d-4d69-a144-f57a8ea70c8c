package com.aiyoooo.service.biz.impl.t;

import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.service.t.TSteamOfferDetailService;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamOffer;
import com.aiyoooo.service.dao.entity.t.TSteamOfferDetail;
import com.aiyoooo.service.dao.mapper.TSteamOfferDetailMapper;
import com.aiyoooo.service.dao.vo.SteamAPIResponseInventoryAsset;
import com.aiyoooo.service.dao.vo.SteamAPIResponseTradeHistory;
import com.aiyoooo.service.dao.vo.SteamAPIResponseTradeHistoryDetail;
import com.aiyoooo.service.dao.vo.SteamTradeDetailVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * steam交易报价明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class TSteamOfferDetailServiceImpl extends ServiceImpl<TSteamOfferDetailMapper, TSteamOfferDetail> implements TSteamOfferDetailService {

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Override
    public List<TSteamOfferDetail> listByOfferId(Long offerId) {
        if (offerId == null) {
            return null;
        }

        LambdaQueryWrapper<TSteamOfferDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSteamOfferDetail::getOfferId, offerId);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doHandleNewAssetid(TSteamOffer data) {

        SteamTradeDetailVo detailVo = JSONUtil.toBean(data.getTradeOfferJson(), SteamTradeDetailVo.class);
        TSteamAccount steamAccount = tSteamAccountService.getById(data.getAccountId());
        String tradeid = detailVo.getResponse().getOffer().getTradeid();
        SteamAPIResponseTradeHistory tradeHistory = SteamUtil.getTradeHistory(steamAccount, detailVo.getResponse().getOffer().getTimeCreated());

        SteamAPIResponseTradeHistoryDetail tradeHistoryDetail = null;
        List<SteamAPIResponseTradeHistoryDetail> trades = tradeHistory.getTrades();
        for (SteamAPIResponseTradeHistoryDetail trade : trades) {
            if (trade.getTradeid().equals(tradeid)) {
                tradeHistoryDetail = trade;
                break;
            }
        }

        if (null == tradeHistoryDetail) {
            return;
        }

        LambdaUpdateWrapper<TSteamOfferDetail> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TSteamOfferDetail::getOfferId, data.getId());
        List<TSteamOfferDetail> steamOfferDetailList =  baseMapper.selectList(wrapper);

        List<SteamAPIResponseInventoryAsset> list = CollectionUtils.isEmpty(tradeHistoryDetail.getAssets_given()) ?
                tradeHistoryDetail.getAssets_received() : tradeHistoryDetail.getAssets_given();
        Map<String, String> collected = list.stream()
                .collect(Collectors.toMap(
                        SteamAPIResponseInventoryAsset::getAssetid,
                        SteamAPIResponseInventoryAsset::getNew_assetid
                ));

        for (TSteamOfferDetail steamOfferDetail : steamOfferDetailList) {
            steamOfferDetail.setNewAssetid(collected.get(steamOfferDetail.getAssetid()));
        }

        this.updateBatchById(steamOfferDetailList);

    }

}