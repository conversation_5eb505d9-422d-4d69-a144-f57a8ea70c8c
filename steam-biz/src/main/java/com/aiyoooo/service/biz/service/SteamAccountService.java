package com.aiyoooo.service.biz.service;

import cn.hutool.json.JSONObject;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetService;
import com.aiyoooo.service.biz.service.t.TSteamAccountInfoService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.task.TaskProducer;
import com.aiyoooo.service.biz.util.SteamSessionUtil;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.biz.util.SysConstants;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.util.RedisUtil;
import com.aiyoooo.service.common.util.SnowflakeIdGenerator;
import com.aiyoooo.service.common.vo.SteamDataRes;
import com.aiyoooo.service.dao.dto.SteamAccountCreateDto;
import com.aiyoooo.service.dao.dto.SteamAccountInvalidSessionDto;
import com.aiyoooo.service.dao.dto.SteamAccountLoginSessionDto;
import com.aiyoooo.service.dao.dto.SteamAccountRefreshCookieFlagDto;
import com.aiyoooo.service.dao.dto.SteamAccountRefreshSteamAccountDto;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamAccountInfo;
import com.aiyoooo.service.dao.enums.EBoolean;
import com.aiyoooo.service.dao.enums.*;
import com.aiyoooo.service.dao.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.time.LocalDateTime;
import java.util.Date;
import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Steam相关服务
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
@Service
public class SteamAccountService {

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountInfoService tSteamAccountInfoService;

    @Resource
    private TSteamAccountAssetService tSteamAccountAssetService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private TaskProducer taskProducer;

    /**
     * 创建steam账户
     */
    public SteamAccountCreateVo create(SteamAccountCreateDto request) {
        //验证是否已存在
        TSteamAccount tSteamAccount = tSteamAccountService.getBySteamAccount(request.getAuthCode(),request.getSteamAccount());
        if (null != tSteamAccount) {
            return new SteamAccountCreateVo(tSteamAccount.getId());
        }

        TSteamAccount data = new TSteamAccount();
        BeanUtils.copyProperties(request, data);

        data.setId(SnowflakeIdGenerator.getNextId());

        data.setAuthCode(request.getAuthCode());
        data.setCookieFlag(ECookieFlag.TODO.getCode());
        data.setStatus(ESteamAccountStatus.STEAM_ACCOUNT_STATUS_0.getCode());
        tSteamAccountService.saveOrUpdate(data);

        //执行登录
        taskProducer.createAndPushTask(TaskTypeEnum.COOKIE_SYNC, data.getId(), null);

        return new SteamAccountCreateVo(data.getId());
    }


    /**
     * 更新cookie标志
     */
    public void refreshCookiesFlag(SteamAccountRefreshCookieFlagDto request) {
        TSteamAccount tSteamAccount = tSteamAccountService.getById(request.getId());
        //请求失效，只能从定时任务处理，这里跳过
        if (ECookieFlag.INVALID.getCode().equals(tSteamAccount.getCookieFlag())) {
            return;
        }
        tSteamAccount.setCookieFlag(ECookieFlag.TODO.getCode());
        tSteamAccountService.updateById(tSteamAccount);
    }

    /**
     * 更新cookie
     */
    public void loginSession(SteamAccountLoginSessionDto request) {
        TSteamAccount tSteamAccount = tSteamAccountService.getById(request.getId());

        tSteamAccountService.firstTokenLogin(tSteamAccount, request.getTokenCode());
    }

    /**
     * 更新cookie
     */
    public void invalidSession(SteamAccountInvalidSessionDto request) {
        TSteamAccount tSteamAccount = tSteamAccountService.getById(request.getId());
        if(EBoolean.YES.getCode().equals(tSteamAccount.getCookieFlag())){
            tSteamAccount.setCookieFlag(EBoolean.NO.getCode());
            tSteamAccount.setTag(SysConstants.ExTag);
            tSteamAccount.setUpdatedAt(LocalDateTime.now());
            tSteamAccountService.updateById(tSteamAccount);
        }
    }


    /**
     * 更新cookie
     */
    @Transactional(rollbackFor = Exception.class)
    public void refreshSteamAccount(SteamAccountRefreshSteamAccountDto request) {
        TSteamAccount tSteamAccount = tSteamAccountService.getById(request.getId());
        if (StringUtils.isNotBlank(request.getSteamPwd())) {
            tSteamAccount.setSteamPwd(request.getSteamPwd());
        }
        if (StringUtils.isNotBlank(request.getStatus())) {
            tSteamAccount.setStatus(request.getStatus());
        }
        if (StringUtils.isNotBlank(request.getQueryLevel())) {
            tSteamAccount.setQueryLevel(request.getQueryLevel());
        }
        if (StringUtils.isNotBlank(request.getSafeLevel())) {
            tSteamAccount.setSafeLevel(request.getSafeLevel());
        }
        if (StringUtils.isNotBlank(request.getMafile())) {
            SteamDataRes steamDataRes = SteamUtil.getSteamInfo(request.getMafile());
            if(!tSteamAccount.getSteamAccount().equals(steamDataRes.getAccountName())){
                throw new BizException("账号" + tSteamAccount.getSteamAccount() + "令牌和账号不对应");
            }

            tSteamAccount.setMafile(request.getMafile());
        }
        if (StringUtils.isNotBlank(request.getMafileDepart())) {
            SteamDataRes steamDataRes = SteamUtil.getSteamInfo(request.getMafileDepart());
            if(!tSteamAccount.getSteamAccount().equals(steamDataRes.getAccountName())){
                throw new BizException("账号" + tSteamAccount.getSteamAccount() + "令牌和账号不对应");
            }
            tSteamAccount.setMafileDepart(request.getMafileDepart());
        }
        if (StringUtils.isNotBlank(request.getTag())) {
            tSteamAccount.setTag(request.getTag());
        }
        if (StringUtils.isNotBlank(request.getUseCategory())) {
            tSteamAccount.setUseCategory(request.getUseCategory());
        }
        if (StringUtils.isNotBlank(request.getUseClass())) {
            tSteamAccount.setUseClass(request.getUseClass());
        }
        tSteamAccount.setUpdatedAt(LocalDateTime.now());
        tSteamAccountService.updateById(tSteamAccount);
    }


    public SteamPlayerVo getPlayer(String steamId) {
        return SteamUtil.getPlayerInfo(steamId);
    }


    public SteamAccountDetailNewVo detailNew(Long id) {
        TSteamAccount data = tSteamAccountService.getById(id);
        if (null == data) {
            throw new BizException("id不存在");
        }

        SteamAccountDetailNewVo res = new SteamAccountDetailNewVo();

        TSteamAccountInfo accountInfo = tSteamAccountInfoService.getById(data.getId());
        if (accountInfo !=null){
            BeanUtils.copyProperties(accountInfo, res);
        }
        res.setSteamAccount(data.getSteamAccount());
        res.setCookieFlag(data.getCookieFlag());

        if(StringUtils.isBlank(res.getSteamId())){
            String steamId = SteamUtil.getSteamIdByCookie(data);
            res.setSteamId(steamId);
        }

        return res;
    }

    public SteamAccountDetailSafeVo detailSafe(Long id) {
        TSteamAccount data = tSteamAccountService.getById(id);
        if (null == data) {
            throw new BizException("id不存在");
        }

        SteamAccountDetailSafeVo res = new SteamAccountDetailSafeVo();
        res.setSteamAccount(data.getSteamAccount());
        res.setSteamPwd(data.getSteamPwd());

        res.setMafile(SteamUtil.getSteamInfoStr(data.getMafile()));
        res.setMafileDepart(SteamUtil.getSteamInfoStr(data.getMafileDepart()));

        return res;
    }

    public JSONObject detailAccountInfo(String accountId) {
        TSteamAccount account = tSteamAccountService.getById(accountId);
        if (null == account) {
            throw new BizException("id不存在");
        }

        LambdaQueryWrapper<TSteamAccountAsset> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSteamAccountAsset::getAccountId, accountId);
        queryWrapper.orderByDesc(TSteamAccountAsset::getAssetid);
        queryWrapper.last("LIMIT 1");
        TSteamAccountAsset asset = tSteamAccountAssetService.getOne(queryWrapper);
        if (null == asset) {
            throw new BizException("账号暂无库存");
        }

        SteamDataRes steamInfo = SteamUtil.getSteamInfo(account.getMafile());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("username", account.getSteamAccount());
        jsonObject.put("password", account.getSteamPwd());
        jsonObject.put("sharedSecret", steamInfo.getSharedSecret());
        return jsonObject;
    }

    public void doSyncSteamAccountCookieFlag2to0() {
        log.info("定时器重置2状态为0");
        LambdaUpdateWrapper<TSteamAccount> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TSteamAccount::getCookieFlag, ECookieFlag.INVALID.getCode());
        TSteamAccount update = new TSteamAccount();
        update.setCookieFlag(ECookieFlag.TODO.getCode());
        tSteamAccountService.update(update, updateWrapper);
        log.info("定时器重置2状态为0完成");

    }
}
