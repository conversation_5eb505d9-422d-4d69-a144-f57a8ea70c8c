package com.aiyoooo.service.biz.task;
import com.aiyoooo.service.biz.service.SteamAccountService;
import com.aiyoooo.service.biz.service.SteamOpenBoxTaskService;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetOutStockService;
import com.aiyoooo.service.biz.service.t.TSteamWalletHistoryService;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetHistoryService;
import com.aiyoooo.service.dao.enums.QueryLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时任务调度
 *
 * <AUTHOR>
 */
@Slf4j
@Component("bizTask")
public class BizScheduledTask {

    @Resource
    private TaskProducer taskProducer;

    @Resource
    private TSteamAccountAssetOutStockService tSteamAccountAssetOutStockService;

    @Resource
    private TSteamWalletHistoryService tSteamWalletHistoryService;

    @Resource
    private TSteamAccountAssetHistoryService tSteamAccountAssetHistoryService;

    @Resource
    private SteamAccountService steamAccountService;

    @Resource
    private SteamOpenBoxTaskService steamOpenBoxTaskService;

    @Scheduled(cron = "0/10 * * * * ?")
    public void doSyncSteamAccountCookie() {
        taskProducer.produceCookieSyncTasks();
    }


    @Scheduled(cron = "0 0/30 * * * ?")
    public void doSyncSteamAccountCookieFlag2to0() {
        steamAccountService.doSyncSteamAccountCookieFlag2to0();
    }

//    @Scheduled(cron = "0 */30 * * * ?")
    public void doRefreshToken() {
        taskProducer.produceRefreshTokenTask();
    }

    @Scheduled(cron = "0 0/10 * * * *")
    public void doSyncSteamAccountBaseInfo() {
        taskProducer.produceBaseInfoSyncTasks();
    }

//    @Scheduled(cron = "0 0/2 * * * ?")
    public void doSyncSteamAccountTradeUrl() {
        taskProducer.produceTradeUrlSyncTasks();
    }

//    @Scheduled(cron = "0 0/1 * * * ?")
    public void doSyncSteamAccountApiKey() {
        taskProducer.produceApiKeySyncTasks();
    }

    // 高频库存同步任务
//    @Scheduled(cron = "0 0/5 * * * ?")
    public void checkInventoryChangesHigh() {
        log.info("执行高频库存同步任务");
        taskProducer.produceInventorySyncTasks(QueryLevelEnum.HIGH);
    }

    // 中频库存同步任务
//    @Scheduled(cron = "0 0 */1 * * ?")
    public void checkInventoryChangesMedium() {
        log.info("执行中频库存同步任务");
        taskProducer.produceInventorySyncTasks(QueryLevelEnum.MEDIUM);
    }

    // 低频库存同步任务
    @Scheduled(cron = "0 0/30 * * * ?")
    public void checkInventoryChangesLow() {
        log.info("执行低频库存同步任务");
        taskProducer.produceInventorySyncTasks(QueryLevelEnum.LOW);
    }

//    @Scheduled(cron = "0 */5 * * * *")
    public void checkWalletHistoryChanges() {
        taskProducer.produceWalletSyncTasks();
    }

    @Scheduled(cron = "0/15 * * * * ?")
    public void doSyncSteamOffers() {
        taskProducer.produceSteamOffers();
    }

    //    @Scheduled(cron = "0 0 0/2 * * ?")
    public void doMigrateAsset() {
        tSteamAccountAssetOutStockService.doMigrateAsset();
    }
    
    /**
     * 每月1号凌晨1点执行历史数据迁移任务
     * 将30天前的钱包和资产历史记录迁移到归档表
     */
//    @Scheduled(cron = "0 0 1 1 * ?")
    public void doMigrateHistoryData() {
        log.info("开始执行历史数据迁移任务...");
        // 迁移钱包历史记录
        tSteamWalletHistoryService.migrateHistoryData(30);
        // 迁移资产历史记录
        tSteamAccountAssetHistoryService.migrateHistoryData(30);
        log.info("历史数据迁移任务执行完成");
    }

    /**
     * 每1秒执行一次开箱任务处理
     */
    @Scheduled(cron = "0/1 * * * * ?")
    public void processOpenBoxTasks() {
        log.debug("开始处理开箱任务...");
        try {
            taskProducer.processOpenCaseTasks();
        } catch (Exception e) {
            log.error("处理开箱任务异常", e);
        }
        log.debug("开箱任务处理完成");
    }
}



