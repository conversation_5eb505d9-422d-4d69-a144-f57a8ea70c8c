package com.aiyoooo.service.biz.util;

import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhoudong
 * @since : 2020/8/28 16:07
 */
@Slf4j
public class HttpUtil {

    // 全局连接池配置
    private static final PoolingHttpClientConnectionManager CONNECTION_MANAGER;
    public static final CloseableHttpClient httpClient;

    private static final String UTF8 = "UTF-8";
    private static final int CONNECT_TIMEOUT = 60000 * 3;
    private static final int CONNECTION_REQUEST_TIMEOUT = 60000 * 3;
    private static final int SOCKET_TIMEOUT = 60000  * 3;
    private static final int MAX_TOTAL_CONNECTION = 200;
    private static final int MAX_PER_ROUTE = 50;
    private static final String JSON_CONTENT_TYPE = "application/json; charset=UTF-8";

    private static final int MAX_RETRY_TIMES = 3;  // 最大重试次数
    private static final long RETRY_INTERVAL = 1000;  // 重试间隔（毫秒）

    static {
        CONNECTION_MANAGER = new PoolingHttpClientConnectionManager();
        CONNECTION_MANAGER.setMaxTotal(MAX_TOTAL_CONNECTION);
        CONNECTION_MANAGER.setDefaultMaxPerRoute(MAX_PER_ROUTE);

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();

        httpClient = HttpClients.custom()
                .setConnectionManager(CONNECTION_MANAGER)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }

    public static String post(String url, Map map) {
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", JSON_CONTENT_TYPE);
        if (map != null) {
            StringEntity entity = new StringEntity(JSONUtil.toJsonStr(map), StandardCharsets.UTF_8);
            post.setEntity(entity);
        }
        return executeHttpRequest(post, null, url);
    }

    public static String post(String url, Map map, HttpClientContext context) {
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", JSON_CONTENT_TYPE);
        if (map != null) {
            StringEntity entity = new StringEntity(JSONUtil.toJsonStr(map), StandardCharsets.UTF_8);
            post.setEntity(entity);
        }
        return executeHttpRequest(post, context, url);
    }

    public static String postByHead(String url, Map map, Map<String, String> headMap) {
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", JSON_CONTENT_TYPE);
        addHeaders(post, headMap);
        if (map != null) {
            StringEntity entity = new StringEntity(JSONUtil.toJsonStr(map), StandardCharsets.UTF_8);
            post.setEntity(entity);
        }
        return executeHttpRequest(post, null, url);
    }

    public static String postByHeadAndBodyStr(String url, String body, Map<String, String> headMap) {
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        addHeaders(post, headMap);
        if (StringUtils.isNotBlank(body)) {
            StringEntity entity = new StringEntity(body, StandardCharsets.UTF_8);
            post.setEntity(entity);
        }
        return executeHttpRequest(post, null, url);
    }

    public static String getMethodByHead(String urll, Map<String, String> headMap) {
        URI uri = buildUri(urll);
        HttpGet httpGet = new HttpGet(uri);
        addHeaders(httpGet, headMap);
        return executeHttpRequest(httpGet, null, urll);
    }

    public static String getMethod(String reqUrl, HttpClientContext context) {
        URI uri = buildUri(reqUrl);
        HttpGet httpGet = new HttpGet(uri);
        return executeHttpRequest(httpGet, context, reqUrl);
    }

    public static String getMethod(String reqUrl) {
        HttpGet httpGet = new HttpGet(reqUrl);
        return executeHttpRequest(httpGet, null, reqUrl);
    }

    public static String postFrom(String url, String parameters, Map<String, String> headMap) {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("User-Agent", "Mozilla/5.0");
        addHeaders(httpPost, headMap);
        List<NameValuePair> nvps = toNameValuePairList(parameters);
        httpPost.setEntity(new UrlEncodedFormEntity(nvps, StandardCharsets.UTF_8));
        return executeHttpRequest(httpPost, null, url);
    }

    /**
     * 内部执行请求并返回结果的通用方法
     */
    private static String executeHttpRequest(HttpUriRequest request, HttpClientContext context, String url) {
        int retryCount = 0;
        while (true) {
            try (CloseableHttpResponse response = (context == null) ? httpClient.execute(request) : httpClient.execute(request, context)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode != HttpStatus.SC_OK) {
                    log.error("连接不成功！状态码 ：{}, url={}", statusCode, url);
                    if ((url.contains("https://steamcommunity.com/") || url.contains("https://api.steampowered.com")) && statusCode == HttpStatus.SC_FORBIDDEN) {
                        return "loginModals";
                    }
                    // 判断是否需要重试
                    if ((url.contains("https://steamcommunity.com/") || url.contains("https://api.steampowered.com"))
                            && shouldRetry(statusCode) && retryCount < MAX_RETRY_TIMES) {
                        retryCount++;
                        log.warn("请求失败，正在进行第{}次重试，url={}", retryCount, url);
                        try {
                            Thread.sleep(RETRY_INTERVAL);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            throw new BizException("重试被中断");
                        }
                        continue;
                    }

                    throw new BizException("请求失败,状态码:" + statusCode);
                }
                HttpEntity entity = response.getEntity();
                return entity != null ? EntityUtils.toString(entity, UTF8) : "";

            } catch (BizException e) {
                throw e;
            } catch (IOException e) {
                // 判断是否需要重试
                if (retryCount < MAX_RETRY_TIMES) {
                    retryCount++;
                    log.warn("请求发生IO异常，正在进行第{}次重试，url={}, error={}", retryCount, url, e.getMessage());
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BizException("重试被中断");
                    }
                    continue;
                }
                log.error("请求出错: url={} 异常信息={}", url, e.getMessage(), e);
                throw new BizException(e.getMessage());
            }
        }
    }

    /**
     * 判断是否需要重试
     *
     * @param statusCode HTTP状态码
     * @return 是否需要重试
     */
    private static boolean shouldRetry(int statusCode) {
        // 以下状态码建议重试
        return statusCode == HttpStatus.SC_INTERNAL_SERVER_ERROR ||  // 500
                statusCode == HttpStatus.SC_BAD_GATEWAY ||           // 502
                statusCode == HttpStatus.SC_SERVICE_UNAVAILABLE ||   // 503
                statusCode == HttpStatus.SC_GATEWAY_TIMEOUT;         // 504
    }

    /**
     * 将参数字符串转为NameValuePair列表
     * 参数格式类似：key1=value1&key2=value2
     */
    private static List<NameValuePair> toNameValuePairList(String parameters) {
        List<NameValuePair> nvps = new ArrayList<>();
        if (StringUtils.isBlank(parameters)) {
            return nvps;
        }
        String[] paramList = parameters.split("&");
        for (String parm : paramList) {
            int index = parm.indexOf("=");
            if (index > 0 && index < parm.length()) {
                String key = parm.substring(0, index);
                String value = parm.substring(index + 1);
                nvps.add(new BasicNameValuePair(key, value));
            }
        }
        return nvps;
    }

    /**
     * 给请求添加头信息
     */
    private static void addHeaders(HttpUriRequest request, Map<String, String> headMap) {
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                request.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 构建URI
     */
    private static URI buildUri(String urll) {
        try {
            URL url = new URL(urll);
            return new URI(url.getProtocol(), null, url.getHost(), url.getPort(), url.getPath(), url.getQuery(), null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 使用代理发送GET请求
     *
     * @param reqUrl    请求URL
     * @param headMap
     * @param proxyHost 代理主机
     * @param proxyPort 代理端口
     * @return 响应内容
     */
    public static String getMethodWithProxy(String reqUrl, Map<String, String> headMap, String proxyHost, int proxyPort) {
        HttpGet httpGet = new HttpGet(reqUrl);
        addHeaders(httpGet, headMap);
        HttpHost proxy = new HttpHost(proxyHost, proxyPort);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .setProxy(proxy)
                .build();
        httpGet.setConfig(requestConfig);
        return executeHttpRequest(httpGet, null, reqUrl);
    }

    /**
     * 使用代理发送带请求头的POST请求
     *
     * @param url       请求URL
     * @param map       请求参数
     * @param headMap   请求头
     * @param proxyHost 代理主机
     * @param proxyPort 代理端口
     * @return 响应内容
     */
    public static String postByHeadWithProxy(String url, Map map, Map<String, String> headMap, String proxyHost, int proxyPort) {
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", JSON_CONTENT_TYPE);
        addHeaders(post, headMap);
        if (map != null) {
            StringEntity entity = new StringEntity(JSONUtil.toJsonStr(map), StandardCharsets.UTF_8);
            post.setEntity(entity);
        }
        HttpHost proxy = new HttpHost(proxyHost, proxyPort);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .setProxy(proxy)
                .build();
        post.setConfig(requestConfig);
        return executeHttpRequest(post, null, url);
    }

}
