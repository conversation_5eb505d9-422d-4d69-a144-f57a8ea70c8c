package com.aiyoooo.service.biz.service.t;

import com.aiyoooo.service.dao.dto.SteamOpenBoxDto;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAssetHistory;
import com.aiyoooo.service.dao.vo.SteamAccountAssetVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
 * <p>
 * steam账号资产历史 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface TSteamAccountAssetHistoryService extends IService<TSteamAccountAssetHistory> {


    /**
     * 拆分后的：同步资产历史（只处理入库）
     */
    void syncAccountAssetHistory(TSteamAccount account);

    /**
     * 迁移历史数据
     * 将指定天数前的资产历史记录迁移到归档表
     *
     * @param days 天数，如30表示迁移30天前的数据
     * @return 迁移的记录数
     */
    int migrateHistoryData(int days);
}