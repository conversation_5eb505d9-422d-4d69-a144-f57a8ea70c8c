package com.aiyoooo.service.biz.task;

import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.SteamAccountAssetHistoryService;
import com.aiyoooo.service.biz.service.SteamOfferService;
import com.aiyoooo.service.biz.service.SteamOpenBoxTaskService;
import com.aiyoooo.service.biz.service.t.*;
import com.aiyoooo.service.common.util.RedisUtil;
import com.aiyoooo.service.dao.dto.SteamOpenBoxDto;
import com.aiyoooo.service.dao.dto.SteamSyncTask;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.enums.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.dao.QueryTimeoutException;

import javax.annotation.Resource;
import java.util.function.Consumer;

@Service
@Slf4j
public class TaskConsumer {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountAssetService tSteamAccountAssetService;

    @Resource
    private TSteamAccountAssetHistoryService tSteamAccountAssetHistoryService;

    @Resource
    private TSteamWalletHistoryService tSteamWalletHistoryService;

    @Resource
    private SteamOfferService steamOfferService;

    @Resource
    private SteamAccountAssetHistoryService steamAccountAssetHistoryService;

    @Resource
    private SteamOpenBoxTaskService steamOpenBoxTaskService;

    private static final int MAX_RETRY_COUNT = 3;

    // 从Set中移除任务标识
    private void removeTaskFromSet(TaskTypeEnum taskType, Long accountId) {
        redisUtil.setRemove(taskType.getSetKey(), accountId.toString());
    }

    public void consumeCookieSyncTasks() {

        consumeTasks(TaskTypeEnum.COOKIE_SYNC, task -> {
            log.info("{} is processing CookieSyncTasks...", Thread.currentThread().getName());
            TSteamAccount account = tSteamAccountService.getById(task.getBizLockId());
            log.info("处理账号登录获取cookie,{}", account.getSteamAccount());
            tSteamAccountService.doSyncSession(account);
        });
    }

    public void consumeRefreshTokenTask() {

        consumeTasks(TaskTypeEnum.REFRESH_TOKEN, task -> {
            TSteamAccount account = tSteamAccountService.getById(task.getBizLockId());
            tSteamAccountService.doRefreshToken(account);
        });
    }


    public void consumeBaseInfoSyncTasks() {
        consumeTasks(TaskTypeEnum.BASE_INFO_SYNC, task -> {
            TSteamAccount account = tSteamAccountService.getById(task.getBizLockId());
//            log.info("处理账号consumeBaseInfoSyncTasks,{}", account.getSteamAccount());
            tSteamAccountService.doSyncSteamAccountBaseInfo(account);
        });
    }

    public void consumeTradeUrlSyncTasks() {
        consumeTasks(TaskTypeEnum.TRADE_URL_SYNC, task -> {
            TSteamAccount account = tSteamAccountService.getById(task.getBizLockId());
//            log.info("处理账号consumeTradeUrlSyncTasks,{}", account.getSteamAccount());
            tSteamAccountService.doSyncTradeUrl(account);
        });
    }

    public void consumeApiKeySyncTasks() {
        consumeTasks(TaskTypeEnum.API_KEY_SYNC, task -> {
            String key = "steam:apikey:create".concat(task.getBizLockId().toString());
            if (!redisUtil.hasKey(key)) {
                TSteamAccount account = tSteamAccountService.getById(task.getBizLockId());
//                log.info("处理账号consumeApiKeySyncTasks,{}", account.getSteamAccount());
                tSteamAccountService.doSyncApikey(account);
                redisUtil.set(key, 1, 1000 * 60 * 10);
            }
        });
    }

    public void consumeInventorySyncTasks() {
        consumeTasks(TaskTypeEnum.INVENTORY_SYNC, task -> {
            TSteamAccount account = tSteamAccountService.getById(task.getBizLockId());
            log.info("处理账号consumeInventorySyncTasks,{}", account.getSteamAccount());
            tSteamAccountAssetService.doSyncAsset(account);
//            try {
//                String key = "steam:inventory:history".concat(account.getId().toString());
//                if (!redisUtil.hasKey(key)) {
//                    tSteamAccountAssetHistoryService.syncAccountAssetHistory(account);
//                    redisUtil.set(key, 1, 1000 * 60 * 10);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            tSteamAccountAssetService.syncTradeOutHistory(account);
        });
    }

    public void consumeWalletSyncTasks() {
        consumeTasks(TaskTypeEnum.WALLET_SYNC, task -> {
            TSteamAccount account = tSteamAccountService.getById(task.getBizLockId());
//            log.info("处理账号consumeWalletSyncTasks,{}", account.getSteamAccount());
            tSteamWalletHistoryService.doSyncWalletHistory(account);
        });
    }

    public void consumeOfferSyncTasks() {
        consumeTasks(TaskTypeEnum.STEAM_OFFER_SYNC, task -> {
            log.info("处理consumeOfferSyncTasks,{}", task.getBizLockId());
            steamOfferService.doCheckTradeStatus(task.getBizLockId());
        });
    }

    public void consumeOpenBoxTasks() {
        consumeTasks(TaskTypeEnum.OPEN_BOX_SYNC, task -> {
            log.info("consumeOpenBoxTasks,{}", task.getBizLockId());
            steamOpenBoxTaskService.processTask(task.getBizLockId());
        });
    }


    // 通用的任务消费方法
    private void consumeTasks(TaskTypeEnum taskType, Consumer<SteamSyncTask> taskHandler) {
        while (true) {
            try {
                String result = redisUtil.pop(taskType.getQueueKey());
                if (StringUtils.isBlank(result)) {
                    continue;
                }
                SteamSyncTask task = JSONUtil.toBean(result, SteamSyncTask.class);
                if (task == null) {
                    continue;
                }

                try {
                    taskHandler.accept(task);
                    // 任务从Set中移除
                    removeTaskFromSet(taskType, task.getBizLockId());
                } catch (Exception e) {
                    log.error("处理{}任务失败: {}", taskType.getDesc(), task.getTaskId(), e);
                    // 任务从Set中移除
                    removeTaskFromSet(taskType, task.getBizLockId());
                    handleFailedTask(task, taskType.getQueueKey());
                }
            } catch (QueryTimeoutException e) {
                sleepFunction(taskType);
            } catch (Exception e) {
                log.error("消费{}任务异常", taskType.getDesc(), e);
            }
        }
    }

    private static void sleepFunction(TaskTypeEnum taskType) {
        try {
            log.debug("{}无数据处理", taskType.getDesc());
            Thread.sleep(1000); // 休眠1秒
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
    }

    private void handleFailedTask(SteamSyncTask task, String queueKey) {
        if (task.getRetryCount() < MAX_RETRY_COUNT) {
            task.setRetryCount(task.getRetryCount() + 1);
            redisUtil.sSetAndTime(task.getTaskType().getSetKey(), 10 * 60, task.getBizLockId().toString());
            redisUtil.push(queueKey, JSONUtil.toJsonStr(task));
        } else {
            log.error("任务最终失败: {}, 账号: {}", task.getTaskId(), task.getBizLockId());
        }
    }
}