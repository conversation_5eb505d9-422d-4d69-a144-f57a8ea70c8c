package com.aiyoooo.service.biz.service.t.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetService;
import com.aiyoooo.service.biz.service.t.TSteamOpenBoxTaskDetailService;
import com.aiyoooo.service.biz.util.HttpUtil;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.vo.SteamDataRes;
import com.aiyoooo.service.dao.dto.CaseOpenApiRequestItem;
import com.aiyoooo.service.dao.dto.OpenCaseCreateDto;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamOpenBoxTaskDetail;
import com.aiyoooo.service.dao.enums.EOpenBoxTaskStatusEnum;
import com.aiyoooo.service.dao.mapper.t.TSteamOpenBoxTaskDetailMapper;
import com.aiyoooo.service.dao.vo.OpenCaseApiVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * steam开箱任务详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Service
@Slf4j
public class TSteamOpenBoxTaskDetailServiceImpl extends ServiceImpl<TSteamOpenBoxTaskDetailMapper, TSteamOpenBoxTaskDetail> implements TSteamOpenBoxTaskDetailService {

    @Value("${open-case-url}")
    private String openCaseUrl;

    @Resource
    private TSteamAccountAssetService tSteamAccountAssetService;

    @Override
    public List<TSteamAccountAsset> processBatchDetail(TSteamAccount account, TSteamOpenBoxTaskDetail batchDetail) {
        log.info("处理批次，任务ID: {}, 批次: {}, 数量: {}",
                batchDetail.getTaskId(), batchDetail.getBatchNo(), batchDetail.getBatchCount());

        // 解析批次中的箱子和钥匙ID
        String[] caseIds = batchDetail.getCaseAssetIds().split(",");
        String[] keyIds = batchDetail.getKeyAssetIds().split(",");

        List<CaseOpenApiRequestItem> itemList = new ArrayList<>();
        for (int i = 0; i < caseIds.length; i++) {
            CaseOpenApiRequestItem item = new CaseOpenApiRequestItem();
            item.setCaseId(Long.parseLong(caseIds[i].trim()));
            item.setKeyId(Long.parseLong(keyIds[i].trim()));
            itemList.add(item);
        }

        if ("test".equals(account.getTag())) {
            Integer batchCount = batchDetail.getBatchCount();
            List<Long> obtainedAssetIds = new ArrayList<>();
            if (batchCount >= 3) {
                obtainedAssetIds.add(42333026368L);
                obtainedAssetIds.add(42333026570L);
                obtainedAssetIds.add(42333026809L);
            }else {
                obtainedAssetIds.add(42333026368L);
            }

            List<TSteamAccountAsset> assetList = getAssetListByIds(obtainedAssetIds);

            batchDetail.setFinishTime(LocalDateTime.now());
            batchDetail.setResultAssetIds(StringUtils.join(obtainedAssetIds, ","));
            batchDetail.setResultAssetInfo(JSONUtil.toJsonStr(assetList));
            batchDetail.setStatus(EOpenBoxTaskStatusEnum.COMPLETED.getCode());
            updateById(batchDetail);

            //测试返回
            return assetList;
        }

        SteamDataRes steamInfo = SteamUtil.getSteamInfo(account.getMafile());
        Map<String, Object> map = new HashMap<>();
        map.put("username", account.getSteamAccount());
        map.put("password", account.getSteamPwd());
        map.put("sharedSecret", steamInfo.getSharedSecret());
        map.put("caseRequests", itemList);

        log.info("开箱请求参数: {}", JSONUtil.toJsonStr(map));
        String res = HttpUtil.post(openCaseUrl, map);
        JSON json = new JSONObject(res);
        OpenCaseApiVo caseApiVo = json.toBean(OpenCaseApiVo.class);

        if (!caseApiVo.getSuccess()) {
            throw new BizException(caseApiVo.getErrorMessage());
        }

        List<Long> obtainedAssetIds = caseApiVo.getObtainedAssetIds();
        List<TSteamAccountAsset> assetList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            tSteamAccountAssetService.getFirstAsset(account, 15, "");
            assetList = getAssetListByIds(obtainedAssetIds);
            if (assetList.size() != obtainedAssetIds.size()) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            } else {
                break;
            }
        }

        batchDetail.setFinishTime(LocalDateTime.now());
        batchDetail.setResultAssetIds(StringUtils.join(obtainedAssetIds, ","));
        batchDetail.setResultAssetInfo(JSONUtil.toJsonStr(assetList));
        batchDetail.setStatus(EOpenBoxTaskStatusEnum.COMPLETED.getCode());
        updateById(batchDetail);

        return assetList;
    }

    public List<TSteamAccountAsset> getAssetListByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TSteamAccountAsset> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TSteamAccountAsset::getAssetid, ids);
        return tSteamAccountAssetService.list(queryWrapper);

    }
}
