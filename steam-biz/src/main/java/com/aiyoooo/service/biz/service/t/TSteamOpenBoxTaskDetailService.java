package com.aiyoooo.service.biz.service.t;

import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamOpenBoxTaskDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * steam开箱任务详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
public interface TSteamOpenBoxTaskDetailService extends IService<TSteamOpenBoxTaskDetail> {

    /**
     * 处理单个批次详情
     */
    List<TSteamAccountAsset> processBatchDetail(TSteamAccount account, TSteamOpenBoxTaskDetail detail);
}
