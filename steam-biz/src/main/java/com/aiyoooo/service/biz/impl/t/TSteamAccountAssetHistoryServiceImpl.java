package com.aiyoooo.service.biz.impl.t;

import cn.hutool.core.lang.Pair;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetHistoryService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAssetHistory;
import com.aiyoooo.service.dao.enums.EAccountAssetFlowDirectionEnum;
import com.aiyoooo.service.dao.mapper.TSteamAccountAssetHistoryMapper;
import com.aiyoooo.service.dao.mapper.TSteamAccountAssetMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * steam账号资产历史 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class TSteamAccountAssetHistoryServiceImpl extends ServiceImpl<TSteamAccountAssetHistoryMapper, TSteamAccountAssetHistory> implements TSteamAccountAssetHistoryService {

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountAssetMapper tSteamAccountAssetMapper;


    public static String inputStreamToString(String filePath) {
        StringBuilder stringBuilder = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                if (StringUtils.isNotBlank(line))
                    stringBuilder.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return stringBuilder.toString();
    }

    public static void main(String[] args) {
        String string = inputStreamToString("/Users/<USER>/Downloads/js.txt");

        JSONObject history = JSONUtil.parseObj(string);

        List<TSteamAccountAssetHistory> historyList = new ArrayList<>();

        Pair<Boolean, String> booleanStringPair = buildAssetHistory(1L, history, historyList, null);
    }

    @Override
    public void syncAccountAssetHistory(TSteamAccount account) {
        String cursorTime = "0";
        String cursorS = "0";
        String lastHistoryId = null;

        //已存在的
        String existId = null;
        if (StringUtils.isNotBlank(account.getAssetLastHistoryJson())) {
            JSONObject jsonObject = JSONUtil.parseObj(account.getAssetLastHistoryJson());
            if (jsonObject.containsKey("assetHistoryId")) {
                existId = jsonObject.getStr("assetHistoryId");
            }
        }

        while (true) {
            JSONObject history = SteamUtil.getInventoryHistoryByUserId(account, cursorTime, cursorS);
            List<TSteamAccountAssetHistory> historyList = new ArrayList<>();

            Pair<Boolean, String> booleanStringPair = buildAssetHistory(account.getId(),history, historyList, existId);

            if (lastHistoryId == null && booleanStringPair.getValue() != null) {
                lastHistoryId = booleanStringPair.getValue();
            }

            baseMapper.upsertBatch(historyList);

            List<TSteamAccountAsset> accountAssetList = new ArrayList<>();
            for (TSteamAccountAssetHistory assetHistory : historyList) {
                TSteamAccountAsset accountAsset = new TSteamAccountAsset();
                accountAsset.setAssetid(assetHistory.getAssetid());
                accountAsset.setAccountId(account.getId());
                accountAsset.setStockInDatetime(assetHistory.getTradeDate());
                if (StringUtils.isNotBlank(assetHistory.getSteamId())) {
                    accountAsset.setSourceSteamId(assetHistory.getSteamId());
                }
                accountAssetList.add(accountAsset);
            }

            if (!accountAssetList.isEmpty()) {
                tSteamAccountAssetMapper.batchUpdateByAssetId(accountAssetList);
            }

            if (booleanStringPair.getKey()) {
                break;
            }
            if (history.getInt("num") < 50) {
                break;
            }

            cursorS = history.getJSONObject("cursor").getStr("s");
            cursorTime = history.getJSONObject("cursor").getStr("time");
        }

        if (StringUtils.isNotBlank(lastHistoryId)) {
            TSteamAccount updateAccount = new TSteamAccount();
            updateAccount.setId(account.getId());
            if (StringUtils.isBlank(account.getAssetLastHistoryJson())) {
                Map<String,String> jsonObject = new HashMap<>();
                jsonObject.put("assetHistoryId", lastHistoryId);
                updateAccount.setAssetLastHistoryJson(JSONUtil.toJsonStr(jsonObject));
            } else {
                JSONObject jsonObject = JSONUtil.parseObj(account.getAssetLastHistoryJson());
                jsonObject.put("assetHistoryId", lastHistoryId);
                updateAccount.setAssetLastHistoryJson(JSONUtil.toJsonStr(jsonObject));
            }
            tSteamAccountService.updateById(updateAccount);
            account.setAssetLastHistoryJson(updateAccount.toString());
        }
    }

    private static Pair<Boolean, String> buildAssetHistory(Long accountId, JSONObject history, List<TSteamAccountAssetHistory> historyList, String existId) {
        String lastHistoryId = null;
        Document doc = Jsoup.parse(history.getStr("html"));
        Elements tradeRows = doc.select(".tradehistoryrow");

        for (Element row : tradeRows) {
            // 日期与时间
            String dateText = row.select(".tradehistory_date").first().ownText().trim();
            String timeText = row.select(".tradehistory_timestamp").text().trim();
            // 交易描述
            String description = row.select(".tradehistory_event_description").text().trim();
            // 交易类型
            String tradeType = description.contains("任务奖励") ? "任务奖励" : null;
            // 交易对方 Steam ID
            Element link = row.select(".tradehistory_event_description a").first();
            String steamId = null;
            if (link != null) {
                String href = link.attr("href");
                steamId = href.substring(href.lastIndexOf("/") + 1);
                tradeType = "交易";
            }
            if (description.contains("交易") && StringUtils.isBlank(tradeType)) {
                tradeType = "交易";
            }
            // 物品流向
            String flowDirection = row.select(".tradehistory_items_plusminus").text().trim().equals("+") ? EAccountAssetFlowDirectionEnum.IN.getCode() : EAccountAssetFlowDirectionEnum.OUT.getCode();

            // 交易物品
            Elements items = row.select(".history_item");
            for (Element item : items) {
                String historyItemId = item.attr("id");
                if (lastHistoryId == null) {
                    lastHistoryId = historyItemId;
                }

                if (existId != null && existId.equals(historyItemId)) {
                    return new Pair<>(true, lastHistoryId);
                }

                String assetHref = item.attr("href");
                if (StringUtils.isBlank(assetHref)
                        && EAccountAssetFlowDirectionEnum.OUT.getCode().equals(flowDirection)) {
                    continue;
                }
                String appid = item.attr("data-appid");
                if (StringUtils.isNotBlank(appid)&& !appid.equals("730")) {
                    continue;
                }
                String classid = item.attr("data-classid");
                String instanceid = item.attr("data-instanceid");
                String name = item.select(".history_item_name").text().trim();
                String assetid = null;
                if (StringUtils.isNotBlank(assetHref)) {
                    assetid = assetHref.split("#730_2_")[1];
                }

                TSteamAccountAssetHistory assetHistory = new TSteamAccountAssetHistory();
                assetHistory.setHistoryItemId(historyItemId);
                assetHistory.setTradeDate(convertToDateTime(dateText, timeText));
                assetHistory.setTradeType(tradeType);
                assetHistory.setDescription(description);
                assetHistory.setName(name);
                assetHistory.setAssetid(assetid);
                assetHistory.setClassid(classid);
                assetHistory.setInstanceid(instanceid);
                assetHistory.setSteamId(steamId);
                assetHistory.setFlowDirection(EAccountAssetFlowDirectionEnum.IN.getCode());
                assetHistory.setCreateDatetime(new Date());
                assetHistory.setAccountId(accountId);
                historyList.add(assetHistory);
            }
        }
        return new Pair<>(false, lastHistoryId);
    }

    // 转换中文日期与时间为标准 datetime 格式
    private static Date convertToDateTime(String dateText, String timeText) {
        dateText = dateText.replace("年", "-").replace("月", "-").replace("日", "").replaceAll(" ", "");
        String fullText = dateText + " " + timeText.replaceAll(" ", "");
        SimpleDateFormat sdfInput = new SimpleDateFormat("yyyy-MM-dd ah:mm", Locale.CHINA);
        try {
            return sdfInput.parse(fullText);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 迁移历史数据
     * 将指定天数前的资产历史记录迁移到归档表
     *
     * @param days 天数，如30表示迁移30天前的数据
     * @return 迁移的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int migrateHistoryData(int days) {
        log.info("开始迁移{}天前的资产历史记录", days);
        
        try {
            // 执行迁移操作
            int migratedCount = baseMapper.migrateToArchive(days);
            
            if (migratedCount > 0) {
                log.info("成功迁移{}条资产历史记录到归档表", migratedCount);
                
                // 从原表中删除已迁移的数据
                Date cutoffDate = new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L);
                LambdaQueryWrapper<TSteamAccountAssetHistory> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.lt(TSteamAccountAssetHistory::getCreateDatetime, cutoffDate);
                int deletedCount = baseMapper.delete(deleteWrapper);
                
                log.info("成功从原表删除{}条已迁移的数据", deletedCount);
                return deletedCount;
            } else {
                log.info("没有找到需要迁移的资产历史记录");
                return 0;
            }
        } catch (Exception e) {
            log.error("迁移资产历史记录失败", e);
            throw new RuntimeException("迁移资产历史记录失败: " + e.getMessage(), e);
        }
    }
}