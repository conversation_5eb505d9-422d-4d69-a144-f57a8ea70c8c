package com.aiyoooo.service.biz.aspect;

import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.common.annotation.RequirePermission;
import com.aiyoooo.service.dao.dto.OpenBaseReq;
import com.aiyoooo.service.dao.mapper.TSysPermissionMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.Method;

/**
 * 权限控制切面
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Aspect
@Component
public class PermissionAspect {

    @Resource
    private TSysPermissionMapper permissionMapper;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Around("@annotation(com.aiyoooo.service.common.annotation.RequirePermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.warn("无法获取请求上下文，跳过权限验证");
                return joinPoint.proceed();
            }

            HttpServletRequest request = attributes.getRequest();

            String authCode = null;
            // 设置请求参数
            Object[] args = joinPoint.getArgs();
            if (args != null) {
                for (Object arg : args) {
                    if (JSONUtil.toJsonStr(arg).contains("authCode")) {
                        authCode =  ((OpenBaseReq) arg).getAuthCode();
                        break;
                    }
                }
            }

            if (StringUtils.isBlank(authCode)) {
                log.warn("请求中未找到授权码，接口: {}", request.getRequestURI());
                throw new RuntimeException("缺少授权码");
            }

            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            RequirePermission annotation = method.getAnnotation(RequirePermission.class);

            // 获取接口路径
            String interfacePath = request.getRequestURI();

            // 如果注解指定了权限编码，则使用权限编码作为接口路径
            if (StringUtils.isNotBlank(annotation.value())) {
                interfacePath = annotation.value();
            }

            // 检查权限
            int permissionCount = permissionMapper.checkPermission(authCode, interfacePath);
            
            if (permissionCount == 0) {
                String errorMsg = String.format("授权码 [%s] 无权限访问接口 [%s]", 
                    authCode, interfacePath);
                log.warn(errorMsg);
                
                if (annotation.required()) {
                    throw new RuntimeException("权限不足: " + errorMsg);
                } else {
                    log.info("权限验证失败但允许继续访问: {}", errorMsg);
                }
            } else {
                log.debug("权限验证通过: 授权码 [{}] 访问接口 [{}]", authCode, interfacePath);
            }

            return joinPoint.proceed();
            
        } catch (Exception e) {
            log.error("权限验证过程中发生异常", e);
            throw e;
        }
    }

    /**
     * 从请求中获取授权码
     * 优先级: Header > Parameter
     */
    private String getAuthCodeFromRequest(HttpServletRequest request) throws IOException {
        // 从参数中获取
        String  authCode = request.getParameter("authCode");
        if (StringUtils.isNotBlank(authCode)) {
            return authCode;
        }

        // 从Header中获取 ,为了测试用
         authCode = request.getHeader("Auth-Code");
        if (StringUtils.isNotBlank(authCode)) {
            return authCode;
        }

        BufferedReader reader = request.getReader();
        StringBuilder sb = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
        }
        String body = sb.toString();

        return null;
    }
} 