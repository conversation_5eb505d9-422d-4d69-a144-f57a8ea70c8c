package com.aiyoooo.service.biz.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetHistoryService;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.util.HttpUtil;
import com.aiyoooo.service.common.util.RedisUtil;
import com.aiyoooo.service.dao.dto.SteamOpenBoxDto;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * steam账号资产历史 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class SteamAccountAssetHistoryService {

    @Resource
    private TSteamAccountAssetHistoryService tSteamAccountAssetHistoryService;

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountAssetService tSteamAccountAssetService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 拆分后的：同步资产历史（只处理入库）
     */
    public void syncAccountAssetHistory(TSteamAccount account) {

    }

    /**
     * 开箱结果通知
     *
     * @param request
     * @return
     */
    public Boolean openBoxNotify(SteamOpenBoxDto request) {

        TSteamAccount account = tSteamAccountService.getById(request.getAccountId());

        String key = "steam:box:" + account.getId() + ":" + request.getTaskId();

        String maxId = redisUtil.getString(key);

        LambdaQueryWrapper<TSteamAccountAsset> assetWrapper = new LambdaQueryWrapper<>();
        assetWrapper.eq(TSteamAccountAsset::getAccountId, account.getId());
        assetWrapper.gt(TSteamAccountAsset::getAssetid, maxId);
        assetWrapper.orderByDesc(TSteamAccountAsset::getAssetid);

        List<TSteamAccountAsset> assetList = tSteamAccountAssetService.list(assetWrapper);
        if (assetList.isEmpty() || assetList.size() < request.getCount()) {
            tSteamAccountAssetService.getFirstAsset(account, 75, "");
            return false;
        }

        if (assetList.size() > request.getCount()) {
            assetList = assetList.subList(0, request.getCount());
        }

        JSONConfig config = new JSONConfig();
        config.setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

        Map<String, Object> map = new HashMap<>();
        map.put("code", 0);
        JSONArray assetArray = new JSONArray();
        for (TSteamAccountAsset accountAsset : assetList) {
            JSONObject asset = JSONUtil.parseObj(accountAsset, config);
            asset.put("description", JSONUtil.parseObj(accountAsset.getDescription()));
            assetArray.add(asset);
        }
        map.put("data", assetArray);
        try {
            log.info("openbox-notify-params,{},{},{}",request.getTaskId(), request.getCallbackUrl(), JSONUtil.toJsonStr(map));
            String res = HttpUtil.post(request.getCallbackUrl(), map);
            log.info(res);
        }catch (Exception e) {
            log.error("openbox-notify-error{}",request.getTaskId(), e);
        }

        redisUtil.del(key);

        return true;
    }
}