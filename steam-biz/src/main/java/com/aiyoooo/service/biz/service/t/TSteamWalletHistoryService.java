package com.aiyoooo.service.biz.service.t;

import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamWalletHistory;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * steam钱包历史 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface TSteamWalletHistoryService extends IService<TSteamWalletHistory> {

    void doSyncWalletHistory(TSteamAccount account);

    /**
     * 迁移历史数据
     * 将指定天数前的钱包历史记录迁移到归档表
     *
     * @param days 天数，如30表示迁移30天前的数据
     * @return 迁移的记录数
     */
    int migrateHistoryData(int days);
}