package com.aiyoooo.service.biz.aspect;

import com.aiyoooo.service.biz.service.SysOperationLogService;
import com.aiyoooo.service.common.annotation.OperationLog;
import com.aiyoooo.service.dao.entity.t.TSysOperationLog;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

/**
 * 操作日志切面处理类
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Aspect
@Component
public class OperationLogAspect {

    @Resource
    private SysOperationLogService operationLogService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 定义切点 - 带有OperationLog注解的方法
    @Pointcut("@annotation(com.aiyoooo.service.common.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    /**
     * 环绕通知，处理带有OperationLog注解的方法
     */
    @Around("operationLogPointcut()")
    public Object aroundAnnotatedMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        return processOperationLog(joinPoint);
    }


    /**
     * 处理操作日志逻辑
     */
    private Object processOperationLog(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        TSysOperationLog operationLog = new TSysOperationLog();
        
        try {
            // 设置请求方法、请求参数等基本信息
            setRequestInfo(joinPoint, operationLog);
            
            // 提取授权码 - 提前提取，以便在出错时也能记录
            extractAuthCode(joinPoint, operationLog);
            
            // 获取注解信息
            setAnnotationInfo(joinPoint, operationLog);
            
            // 执行原方法
            result = joinPoint.proceed();
            
            // 设置响应结果
            if ((getOperationLogAnnotation(joinPoint).saveResponseData())) {
                setResponseResult(result, operationLog);
            }
            
            operationLog.setStatus("0"); // 成功
        } catch (Throwable e) {
            operationLog.setStatus("1"); // 失败
            operationLog.setErrorMsg(e.getMessage());
            throw e;
        } finally {
            // 计算执行时间
            long costTime = System.currentTimeMillis() - startTime;
            operationLog.setCostTime(costTime);
            operationLog.setOperationTime(LocalDateTime.now());
            // 使用异步方法保存日志
            operationLogService.asyncSaveLog(operationLog);
        }
        
        return result;
    }

//    /**
//     * 处理异常，主要用于捕获切点之外的异常
//     */
//    @AfterThrowing(value = "operationLogPointcut()", throwing = "e")
//    public void afterThrowing(JoinPoint joinPoint, Throwable e) {
//        TSysOperationLog operationLog = new TSysOperationLog();
//        try {
//            // 设置请求信息
//            setRequestInfo(joinPoint, operationLog);
//
//            // 提取授权码
//            extractAuthCode(joinPoint, operationLog);
//
//            // 尝试获取注解信息
//            try {
//                setAnnotationInfo(joinPoint, operationLog);
//            } catch (Exception ex) {
//                // 如果获取注解信息失败，设置默认值
//                Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
//                operationLog.setModule("API接口");
//                operationLog.setOperationType("调用");
//                operationLog.setDescription(method.getName());
//            }
//
//            // 设置异常信息
//            operationLog.setStatus("1");
//            operationLog.setErrorMsg(e.getMessage());
//            operationLog.setOperationTime(LocalDateTime.now());
//
//            // 异步保存日志
//            operationLogService.asyncSaveLog(operationLog);
//        } catch (Exception ex) {
//            log.error("记录操作日志异常", ex);
//        }
//    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(JoinPoint joinPoint, TSysOperationLog operationLog) {
        try {
            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                operationLog.setRequestMethod(request.getMethod());
                operationLog.setRequestUrl(request.getRequestURI());
                operationLog.setRequestIp(getIpAddress(request));
            }
            
            // 设置请求方法
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = signature.getName();
            operationLog.setMethod(className + "." + methodName + "()");
            
            // 设置请求参数
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                try {
                    String params = objectMapper.writeValueAsString(args);
                    // 限制日志长度，避免过大
                    operationLog.setRequestParam(params.length() > 2000 ? params.substring(0, 2000) : params);
                } catch (JsonProcessingException e) {
                    operationLog.setRequestParam("参数序列化异常: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("设置请求信息异常", e);
        }
    }

    /**
     * 设置注解信息
     */
    private void setAnnotationInfo(JoinPoint joinPoint, TSysOperationLog operationLog) {
        OperationLog annotation = getOperationLogAnnotation(joinPoint);
        if (annotation != null) {
            operationLog.setModule(annotation.module());
            operationLog.setOperationType(annotation.operationType());
            operationLog.setDescription(annotation.description());
        }
    }

    /**
     * 设置响应结果
     */
    private void setResponseResult(Object result, TSysOperationLog operationLog) {
        try {
            if (result != null) {
                String responseStr = objectMapper.writeValueAsString(result);
                // 限制日志长度，避免过大
                operationLog.setResponseResult(responseStr.length() > 2000 ? responseStr.substring(0, 2000) : responseStr);
            }
        } catch (JsonProcessingException e) {
            log.error("设置响应结果异常", e);
            operationLog.setResponseResult("响应结果序列化异常: " + e.getMessage());
        }
    }

    /**
     * 提取授权码
     */
    private void extractAuthCode(JoinPoint joinPoint, TSysOperationLog operationLog) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                for (Object arg : args) {
                    if (arg != null) {
                        // 首先尝试直接获取authCode字段（最常见的情况）
                        try {
                            Method getAuthCode = arg.getClass().getMethod("getAuthCode");
                            if (getAuthCode != null) {
                                Object authCode = getAuthCode.invoke(arg);
                                if (authCode != null) {
                                    operationLog.setAuthCode(authCode.toString());
                                    log.debug("成功从请求参数中提取authCode: {}", authCode);
                                    return; // 找到authCode后立即返回
                                }
                            }
                        } catch (NoSuchMethodException e) {
                            // 该对象没有authCode属性，继续检查其他可能的方法
                            try {
                                // 尝试获取其他可能的授权码字段名
                                String[] possibleNames = {"getAuthorization", "getToken", "getUserId", "getAppId"};
                                for (String methodName : possibleNames) {
                                    try {
                                        Method method = arg.getClass().getMethod(methodName);
                                        Object value = method.invoke(arg);
                                        if (value != null) {
                                            operationLog.setAuthCode(value.toString());
                                            log.debug("从备选字段{}中提取授权信息: {}", methodName, value);
                                            return;
                                        }
                                    } catch (NoSuchMethodException ex) {
                                        // 忽略，继续尝试下一个可能的方法
                                    }
                                }
                            } catch (Exception ex) {
                                // 忽略，继续检查其他参数
                            }
                        }
                    }
                }
            }
            
            // 从请求上下文中尝试获取
            try {
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (attributes != null) {
                    HttpServletRequest request = attributes.getRequest();
                    
                    // 尝试从请求头中获取
                    String authHeader = request.getHeader("X-Auth-Code");
                    if (authHeader != null && !authHeader.isEmpty()) {
                        operationLog.setAuthCode(authHeader);
                        log.debug("从请求头X-Auth-Code中提取授权码: {}", authHeader);
                        return;
                    }
                    
                    // 尝试从请求参数中获取
                    String authParam = request.getParameter("authCode");
                    if (authParam != null && !authParam.isEmpty()) {
                        operationLog.setAuthCode(authParam);
                        log.debug("从请求参数authCode中提取授权码: {}", authParam);
                        return;
                    }
                }
            } catch (Exception e) {
                log.warn("尝试从请求上下文中提取授权码失败", e);
            }
            
            if (operationLog.getAuthCode() == null || operationLog.getAuthCode().isEmpty()) {
                log.warn("未能提取到有效的授权码，接口路径: {}", operationLog.getRequestUrl());
            }
        } catch (Exception e) {
            log.error("提取授权码异常", e);
        }
    }

    /**
     * 获取操作日志注解
     */
    private OperationLog getOperationLogAnnotation(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getAnnotation(OperationLog.class);
    }

    /**
     * 检查方法是否调用了verifySignature
     */
    private boolean checkIfUsesVerification(JoinPoint joinPoint) {
        // 这个方法在实际应用中需要更复杂的实现
        // 简化实现：检查方法的参数名称或类型
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg != null) {
                    // 检查是否有authCode和sign属性
                    try {
                        Method getAuthCode = arg.getClass().getMethod("getAuthCode");
                        Method getSign = arg.getClass().getMethod("getSign");
                        if (getAuthCode != null && getSign != null) {
                            return true;
                        }
                    } catch (NoSuchMethodException e) {
                        // 忽略
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获取IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}