package com.aiyoooo.service.biz.service.t;

import com.aiyoooo.service.dao.entity.t.TSteamOffer;
import com.aiyoooo.service.dao.entity.t.TSteamOfferDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * steam交易报价明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface TSteamOfferDetailService extends IService<TSteamOfferDetail> {

    /**
     * 根据交易报价ID查询明细
     * @param offerId 交易报价ID
     * @return 明细列表
     */
    List<TSteamOfferDetail> listByOfferId(Long offerId);


    void doHandleNewAssetid(TSteamOffer data);
}