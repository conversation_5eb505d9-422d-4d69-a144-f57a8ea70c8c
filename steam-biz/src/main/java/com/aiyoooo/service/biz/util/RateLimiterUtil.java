package com.aiyoooo.service.biz.util;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 限流工具类
 */
@Slf4j
@Component
public class RateLimiterUtil {

    private static final Map<String, RateLimiter> LIMITER_MAP = new ConcurrentHashMap<>();

    /**
     * 创建限流器
     *
     * @param key 限流器标识
     * @param qps 每秒允许的请求数
     */
    public static void createLimiter(String key, double qps) {
        LIMITER_MAP.putIfAbsent(key, RateLimiter.create(qps));
    }

    /**
     * 尝试获取令牌
     *
     * @param key 限流器标识
     * @return 是否获取成功
     */
    public static boolean tryAcquire(String key) {
        RateLimiter limiter = LIMITER_MAP.get(key);
        if (limiter == null) {
            log.warn("限流器不存在: {}", key);
            return true;
        }
        return limiter.tryAcquire();
    }

    /**
     * 尝试获取令牌，如果失败则抛出异常
     *
     * @param key 限流器标识
     * @throws RuntimeException 获取令牌失败时抛出
     */
    public static void acquire(String key) {
        if (!tryAcquire(key)) {
            log.warn("限流器触发: {}", key);
            throw new RuntimeException("Rate limit exceeded for: " + key);
        }
    }

    /**
     * 获取限流器
     *
     * @param key 限流器标识
     * @return RateLimiter实例
     */
    public static RateLimiter getLimiter(String key) {
        return LIMITER_MAP.get(key);
    }

    /**
     * 移除限流器
     *
     * @param key 限流器标识
     */
    public static void removeLimiter(String key) {
        LIMITER_MAP.remove(key);
    }

    /**
     * 清空所有限流器
     */
    public static void clearAllLimiters() {
        LIMITER_MAP.clear();
    }
} 