package com.aiyoooo.service.biz.service.t;

import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * steam账号 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
public interface TSteamAccountService extends IService<TSteamAccount> {

    /**
     * 根据steam账号获取账号信息
     *
     * @param authCode
     * @param steamAccount Steam账号
     * @return 账号对象
     */
    TSteamAccount getBySteamAccount(String authCode, String steamAccount);

    void doSyncSession(TSteamAccount tSteamAccount);

    TSteamAccount firstTokenLogin(TSteamAccount tSteamAccount, String tokenCode);

    void doSyncTradeUrl(TSteamAccount account);


    void doSyncSteamAccountBaseInfo(TSteamAccount account);


    void doSyncApikey(TSteamAccount account);

    void doRefreshToken(TSteamAccount tSteamAccount);
}
