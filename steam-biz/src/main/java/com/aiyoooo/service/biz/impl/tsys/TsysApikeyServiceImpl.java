package com.aiyoooo.service.biz.impl.tsys;

import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.util.SignatureTool;
import com.aiyoooo.service.dao.entity.tsys.SysApikey;
import com.aiyoooo.service.dao.enums.EBoolean;
import com.aiyoooo.service.dao.mapper.tsys.TsysApikeyMapper;
import com.aiyoooo.service.biz.service.tsys.TsysApikeyService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * apikey 配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
public class TsysApikeyServiceImpl extends ServiceImpl<TsysApikeyMapper, SysApikey> implements TsysApikeyService {

    @Override
    public SysApikey getByApiKey(String authCode) {
        LambdaQueryWrapper<SysApikey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApikey::getAuthCode, authCode);
        queryWrapper.eq(SysApikey::getStatus, EBoolean.YES.getCode());
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public SysApikey verifySignature(Object obj, String signed, String authCode) {
        SysApikey result = getByApiKey(authCode);
        if (result == null) {
            throw new BizException("授权不合法");
        }
        SignatureTool.verifySignature(obj, signed, result.getPublicKey());
        return result;
    }

    @Override
    public String generateMd5Signature(Object obj, String authCode) {
        LambdaQueryWrapper<SysApikey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysApikey::getAuthCode, authCode);
        SysApikey result = this.baseMapper.selectOne(queryWrapper);
        if (result == null) {
            throw new BizException("授权不合法");
        }

        return SignatureTool.generateMd5Signature(obj, result.getSecretKey());
    }
}
