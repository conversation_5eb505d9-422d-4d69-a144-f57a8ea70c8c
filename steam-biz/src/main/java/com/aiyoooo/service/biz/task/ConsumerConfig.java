package com.aiyoooo.service.biz.task;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
@EnableAsync
public class ConsumerConfig {

    @Resource
    private TaskConsumer taskConsumer;

    @Value("${task.consumer-count}")
    private Integer count;

    // 创建一个固定大小的线程池

    @PostConstruct
    public void init() {

        ExecutorService executorService = Executors.newFixedThreadPool(count * 5);

        for (int i = 0; i < count; i++) {
            executorService.submit(() -> taskConsumer.consumeCookieSyncTasks());

//            executorService.submit(() -> taskConsumer.consumeBaseInfoSyncTasks());
//            executorService.submit(() -> taskConsumer.consumeRefreshTokenTask());

//            executorService.submit(() -> taskConsumer.consumeTradeUrlSyncTasks());
//            executorService.submit(() -> taskConsumer.consumeApiKeySyncTasks());
            executorService.submit(() -> taskConsumer.consumeInventorySyncTasks());
//            executorService.submit(() -> taskConsumer.consumeWalletSyncTasks());
            executorService.submit(() -> taskConsumer.consumeOfferSyncTasks());
            executorService.submit(() -> taskConsumer.consumeOpenBoxTasks());
        }
    }
}