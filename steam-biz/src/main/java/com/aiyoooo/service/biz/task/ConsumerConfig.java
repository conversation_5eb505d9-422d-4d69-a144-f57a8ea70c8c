package com.aiyoooo.service.biz.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.Resource;

/**
 * 任务消费者配置类
 * 现在使用 TaskManager 来管理任务消费者的生命周期
 *
 * @deprecated 建议直接使用 TaskManager，此类保留用于兼容性
 */
@Configuration
@EnableAsync
@Slf4j
public class ConsumerConfig {

    @Resource
    private TaskManager taskManager;

    // TaskManager 会自动通过 @PostConstruct 启动
    // 不需要在这里手动初始化
}