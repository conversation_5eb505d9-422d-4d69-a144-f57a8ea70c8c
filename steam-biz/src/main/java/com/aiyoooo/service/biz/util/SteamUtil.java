package com.aiyoooo.service.biz.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.config.RateLimiterConfig;
import com.aiyoooo.service.biz.event.SteamCookieInvalidEvent;
import com.aiyoooo.service.common.define.HttpStatusEnum;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.util.DateUtil;
import com.aiyoooo.service.common.util.MyHttpUtil;
import com.aiyoooo.service.common.util.SteamMafileUtil;
import com.aiyoooo.service.common.vo.SteamDataRes;
import com.aiyoooo.service.dao.dto.SteamOfferOrderAssetDto;
import com.aiyoooo.service.dao.dto.SteamOfferOrderPartyDto;
import com.aiyoooo.service.dao.dto.SteamOfferOrderTradeDto;
import com.aiyoooo.service.dao.dto.SteamTradeofferVo;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamWalletHistory;
import com.aiyoooo.service.dao.enums.EBoolean;
import com.aiyoooo.service.dao.enums.ECookieFlag;
import com.aiyoooo.service.dao.vo.*;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import okhttp3.*;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

/**
 * Steam服务工具类
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Slf4j
@Component
public class SteamUtil extends SteamMafileUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        SteamUtil.applicationContext = applicationContext;
    }

    /**
     * 检查Steam响应是否表明Cookie已失效
     *
     * @param html      响应HTML内容
     * @param accountId accountId
     */
    private static void checkCookieValid(String html, Long accountId) {
        if (StringUtils.hasText(html) && (!html.contains("loginModals") && !html.contains("<title>登录</title>") && !html.contains("<title>Sign In</title>"))) {
            return;
        }
        // 发布事件
        if (applicationContext != null) {
            applicationContext.publishEvent(new SteamCookieInvalidEvent(SteamUtil.class, accountId));
        }

        throw new RuntimeException("Steam cookie已失效，需要重新登录" + accountId);
    }

    /**
     * 检查Steam响应是否表明Cookie已失效
     *
     * @param accountId accountId
     */
    public static void sendCookieValid(Long accountId) {
        // 发布事件
        if (applicationContext != null) {
            applicationContext.publishEvent(new SteamCookieInvalidEvent(SteamUtil.class, accountId));
        }
    }

    /**
     * 获取Steam余额
     *
     * @param account 认证Cookie
     * @return 账户余额
     */
    public static SteamBalanceVo getAccountBalance(TSteamAccount account) {
        String url = "https://store.steampowered.com/account/";

        String body = HttpUtil.getMethodByHead(url, getSimpleHeaders(account.getLastCookie()));

        // 检查cookie是否失效
        checkCookieValid(body, account.getId());

        SteamBalanceVo vo = new SteamBalanceVo();
        if (StrUtil.isEmpty(body)) {
            return vo;
        }
        Document doc = Jsoup.parse(body);
        Elements eles = doc.select("div.youraccount_steamid");
        if (!eles.isEmpty()) {
            Element ele = eles.get(0);
            String steamIdFromDoc = ele.text();
            steamIdFromDoc = steamIdFromDoc.replace("Steam ID：", "");
            vo.setSteamId(steamIdFromDoc);
        }
        // 获取账户余额
        eles = doc.select("div.accountBalance .accountData");
        if (!eles.isEmpty()) {
            Element ele = eles.get(0);

            String[] arrs = null;
            if (ele.text().contains("$")) {
                arrs = ele.text().split("\\$");
                if (StringUtils.hasText(arrs[0])) {
                    vo.setCurrency(arrs[0]);
                    vo.setBalance(BigDecimal.valueOf(Double.parseDouble(arrs[1])));
                }
            } else if (ele.text().contains("¥ ")) {
                arrs = ele.text().split("¥ ");
                if (StringUtils.hasText(arrs[0])) {
                    vo.setCurrency(arrs[0]);
                    vo.setBalance(BigDecimal.valueOf(Double.parseDouble(arrs[1])));
                }
            }
        }
        // 获取地区信息
        eles = doc.select("div.country_settings .account_data_field");
        if (!eles.isEmpty()) {
            Element ele = eles.get(0);
            String place = ele.text();
            vo.setPlace(place);
        }
        // 获取邮件信息
        eles = doc.select("div.account_setting_block .account_setting_sub_block");
        if (!eles.isEmpty()) {
            for (Element ele : eles) {
                String label = ele.select(".account_manage_label").text().trim();
                if (label.startsWith("电子邮件地址：")) {
                    String email = ele.select(".account_data_field").text().trim();
                    vo.setEmail(email.split(" ")[0]);
                    continue;
                }
                Elements sts = ele.select(".account_security_block .account_data_field");
                if (sts.isEmpty()) {
                    continue;
                }
                String protectType = sts.get(0).text().trim();
                if (protectType.equals("受 Steam 令牌电子邮件保护中")) {
                    vo.setProtectType(1);
                } else if (protectType.equals("受 Steam 令牌手机验证器保护中")) {
                    vo.setProtectType(2);
                } else {
                    vo.setProtectType(0);
                }
            }
        }
        return vo;
    }


    private static Map<String, String> getSimpleHeaders(String cookie) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie","timezoneOffset=28800,0; " +cookie);
        headers.put("Accept-Language", "zh-CN");
        headers.put("user-agent","Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36");
        return headers;
    }

    /**
     * 获取玩家信息
     */
    public static SteamPlayerVo getPlayerInfo(String steamId) {
        if (StringUtils.hasText(steamId)) {
            String url = "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/?key=73FD53EDDCFB865759D3AE1E79DD9C0B&steamids=" + steamId;
            // 发起请求
            String jsonResponse = HttpUtil.getMethod(url);
            // 使用Gson解析
            Gson gson = new Gson();
            JsonObject jsonObject = gson.fromJson(jsonResponse, JsonObject.class);
            JsonObject playerObject = jsonObject
                    .getAsJsonObject("response")
                    .getAsJsonArray("players")
                    .get(0)
                    .getAsJsonObject();

            // 转换为Player对象
            return gson.fromJson(playerObject, SteamPlayerVo.class);
        }
        return null;
    }

    /**
     * 账号等级
     * {
     * "response": {
     * "badges": [...],
     * "player_xp": 4530,
     * "player_level": 23,
     * "player_xp_needed_to_level_up": 270,
     * "player_xp_needed_current_level": 4500
     * }
     * }
     *
     * @param steamId
     * @return
     */
    public static JSONObject getBadges(String steamId) {
        if (StringUtils.hasText(steamId)) {
            String url = "https://api.steampowered.com/IPlayerService/GetBadges/v1/?key=73FD53EDDCFB865759D3AE1E79DD9C0B&steamid=" + steamId;
            String jsonResponse = HttpUtil.getMethod(url);
            return JSONUtil.parseObj(jsonResponse);
        }
        return null;
    }

    /**
     * 账号封禁信息
     * {
     * "players": [
     * {
     * "SteamId": "7656119xxxxxxxxxx",
     * "CommunityBanned": false,
     * "VACBanned": true,
     * "NumberOfVACBans": 1,   存在一次
     * "DaysSinceLastBan": 23,  最近23天
     * "NumberOfGameBans": 0,
     * "EconomyBan": "none"
     * }
     * ]
     * }
     *
     * @param steamId
     * @return
     */
    public static JSONObject getPlayerBans(String steamId) {
        if (StringUtils.hasText(steamId)) {
            String url = "https://api.steampowered.com/ISteamUser/GetPlayerBans/v1/?key=73FD53EDDCFB865759D3AE1E79DD9C0B&steamids=" + steamId;
            // 发起请求
            String jsonResponse = HttpUtil.getMethod(url);
            return JSONUtil.parseObj(jsonResponse).getJSONArray("players").getJSONObject(0);
        }

        return null;
    }

    public static String getApiKey(TSteamAccount steamAccount) {
        if (StringUtils.hasText(steamAccount.getLastCookie())) {
            String url = "https://steamcommunity.com/dev/apikey";

            String html = HttpUtil.getMethodByHead(url, getSimpleHeaders(steamAccount.getLastCookie()));

            checkCookieValid(html, steamAccount.getId());

            Pattern keyPattern = Pattern.compile("(Key|密钥):\\s*([A-Fa-f0-9]+)");
            Matcher keyMatcher = keyPattern.matcher(html);
            if (keyMatcher.find()) {
                return keyMatcher.group(2);
            }
            if (html.contains("注册 Steam Web API 密钥")) {
                return createApikey(steamAccount);
            }
        }
        return null;
    }

    private static String createApikey(TSteamAccount steamAccount) {
        SteamDataRes mafileJson = SteamMafileUtil.getSteamInfo(steamAccount.getMafile());

        String res = HttpUtil.postFrom("https://steamcommunity.com/dev/requestkey", "domain=dev.com&request_id=0&sessionid=" + steamAccount.getLastSessionid()
                + "&agreeToTerms=true", getSimpleHeaders(steamAccount.getLastCookie()));

        checkCookieValid(res, steamAccount.getId());

        if (!res.contains("success")) {
            return null;
        }

        String requestId = JSONUtil.parseObj(res).getStr("request_id");


        SteamMobileConfirmations.ConfirmationListRes listConfRes = null;
        for (int i = 0; i < 10; i++) {
            listConfRes = SteamMobileConfirmations.getConfirmationList(getSteamIdByCookie(steamAccount), mafileJson.getIdentitySecret(), steamAccount.getLastCookie());
            System.out.println("Confirmations List: " + JSONUtil.toJsonStr(listConfRes));
            if (CollectionUtils.isNotEmpty(listConfRes.getConf())) {
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        if (listConfRes.getConf().isEmpty()) {
            return null;
        }

        SteamMobileConfirmations.ConfirmationItem confirmationItem = null;
        for (int i = 0; i < listConfRes.getConf().size(); i++) {
            confirmationItem = listConfRes.getConf().get(i);
            if (confirmationItem.getType() == 9) {
                break;
            }
            confirmationItem = null;
        }
        if (confirmationItem == null) {
            return null;
        }
        String confirmationId = confirmationItem.getId();
        String confirmationNonce = confirmationItem.getNonce();

        for (int i = 0; i < 10; i++) {
            // 执行确认
            SteamMobileConfirmations.ConfirmationActionRes actionRes = SteamMobileConfirmations.confirmAction(getSteamIdByCookie(steamAccount), mafileJson.getIdentitySecret(), steamAccount.getLastCookie(), confirmationId, confirmationNonce, true);
            log.info("Confirmation Action: " + JSONUtil.toJsonStr(actionRes));
            if (actionRes.getSuccess()) {
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        for (int i = 0; i < 10; i++) {
            res = HttpUtil.postFrom("https://steamcommunity.com/dev/requestkey", "domain=dev.com&request_id=" + requestId + "&sessionid=" + steamAccount.getLastSessionid()
                    + "&agreeToTerms=true", new HashMap<String, String>() {{
                put("cookie", steamAccount.getLastCookie());
                put("Accept-Language", "zh-CN");
            }});
            if (res.contains("api_key")) {
                return JSONUtil.parseObj(res).getStr("api_key");
            }
            try {
                Thread.sleep(1500);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    /**
     * 根据steamId,cookie 拉取库存信息
     */
    public static SteamAPIResponseInventory getInventoryByUserId(TSteamAccount steamAccount, String appId, String count,
                                                                 String startAssetid,
                                                                 String language) {
        if(org.apache.commons.lang3.StringUtils.isBlank(language)){
            language = "schinese";
        }
        if(!ECookieFlag.VALID.getCode().equals(steamAccount.getCookieFlag())){
            return null;
        }

        StringBuilder url = new StringBuilder();
        url.append("https://steamcommunity.com/inventory/")
                .append(getSteamIdByCookie(steamAccount))
                .append("/").append(appId)
                .append("/2?l=").append(language)
                .append("&count=").append(count)
                .append("&start_assetid=").append(startAssetid);

        try{
            String jsonResponse = HttpUtil.getMethodByHead(url.toString(), new HashMap<String, String>() {{
                put("cookie", steamAccount.getLastCookie());
            }});

            checkCookieValid(jsonResponse, steamAccount.getId());

            // 使用Gson解析
            Gson gson = new GsonBuilder()
                    .setFieldNamingStrategy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                    .create();
            // 解析 JSON 为顶级对象
            return gson.fromJson(jsonResponse, SteamAPIResponseInventory.class);
        } catch (BizException e){
            if(e.getMessage().contains(HttpStatusEnum.TOO_MANY_REQUESTS.getCode().toString())){
                log.error("库存请求太多,cookie失效处理");
                SteamUtil.sendCookieValid(steamAccount.getId());
            }
            throw e;
        } catch (Exception e){
            throw e;
        }
    }

    public static String getTradeUrl(TSteamAccount account) {
        String tradeUrl = null;

        String url = "https://steamcommunity.com/profiles/" + getSteamIdByCookie(account) + "/tradeoffers/privacy";
        String string = HttpUtil.getMethodByHead(url, new HashMap<String, String>() {{
            put("cookie", account.getLastCookie());
        }});

        // 检查cookie是否失效
        checkCookieValid(string, account.getId());

        Document doc = Jsoup.parse(string);
        // 根据id查找<input>标签
        Element inputElement = doc.getElementById("trade_offer_access_url");

        if (inputElement != null) {
            tradeUrl = inputElement.attr("value").trim();
        } else {
            System.out.println("Element not found!");
        }
        return tradeUrl;
    }

    public static String getSteamIdByCookie(TSteamAccount steamAccount) {
        if (org.apache.commons.lang3.StringUtils.isBlank(steamAccount.getLastCookie())) {
            return null;
        }
        Pattern pattern = Pattern.compile("steamLoginSecure=(\\d+)");
        Matcher matcher = pattern.matcher(steamAccount.getLastCookie());
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }


    public static List<TSteamWalletHistory> getWalletHistory(TSteamAccount account) {
        if(!ECookieFlag.VALID.getCode().equals(account.getCookieFlag())){
            return null;
        }

        String INITIAL_URL = "https://store.steampowered.com/account/history/";
        String LOAD_MORE_URL = "https://store.steampowered.com/account/AjaxLoadMoreHistory/";

        List<Element> allRows = new ArrayList<>();

        // 第一次请求，拿到第一页html
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", "timezoneOffset=28800,0; " + account.getLastCookie());
        headers.put("X-Requested-With", "XMLHttpRequest");
        headers.put("Accept-Language", "zh-CN");

        String firstPageHtml = HttpUtil.getMethodByHead(INITIAL_URL, headers);
        checkCookieValid(firstPageHtml, account.getId());

        Document doc = Jsoup.parse(firstPageHtml);

        // 取第一页数据
        Elements rows = doc.select("table.wallet_history_table tbody tr.wallet_table_row");
        allRows.addAll(rows);

        // 取第一页的 cursor
        String gHistoryCursor = getCursorFromHtml(firstPageHtml);

        // 循环load更多
        while (gHistoryCursor != null && !gHistoryCursor.isEmpty()) {
            String jsonResponse = HttpUtil.postFrom(LOAD_MORE_URL, "cursor=" + gHistoryCursor + "&sessionid=" + account.getLastSessionid(), headers);

            JSONObject jsonObject = JSONUtil.parseObj(jsonResponse);

            if (jsonObject.containsKey("html")) {
                String html = (String) jsonObject.get("html");
                Document moreDoc = Jsoup.parse(html);
                Elements moreRows = moreDoc.select("tr.wallet_table_row");
                allRows.addAll(moreRows);
            }
            if (jsonObject.containsKey("cursor") && jsonObject.get("cursor") != null) {
                gHistoryCursor = jsonObject.get("cursor").toString();
            } else {
                break;
            }
        }
        List<TSteamWalletHistory> historyList = new ArrayList<>();
        // 到这里allRows就是所有记录
        System.out.println("总记录数：" + allRows.size());
        for (Element row : allRows) {
            String date = row.selectFirst("td.wht_date").text().replaceAll(" ", "");
            String item = row.selectFirst("td.wht_items").text();
            String type = row.selectFirst("td.wht_type").text();
            String total = row.selectFirst("td.wht_total").text();
            String change = row.selectFirst("td.wht_wallet_change").text();
            String balance = row.selectFirst("td.wht_wallet_balance").text();
            String onclickAttr = row.attr("onclick");
            String transactionLink = null;
            if (StringUtils.hasText(onclickAttr) && onclickAttr.contains("'")) {
                transactionLink = onclickAttr.split("'")[1];
            }
            String transactionId = null;
            if (transactionLink != null && transactionLink.contains("transid=")) {
                String[] parts = transactionLink.split("transid=");
                String idPart = parts[1];
                if (idPart.contains("&")) { // 有可能后面还有别的参数，比如 appid
                    idPart = idPart.split("&")[0];
                }
                transactionId = idPart;
            }

            if (transactionId == null) {
                String string = date + item + type + total + change + balance;
                transactionId = SteamMobileConfirmations.md5(string);
            }
            TSteamWalletHistory history = new TSteamWalletHistory();
            history.setAccountId(account.getId());
            history.setTransactionId(transactionId);
            history.setTransactionDate(DateUtil.strToDate(date, DateUtil.YYYY_MM_DD_HH_ZW));
            history.setItemDescription(item);
            history.setTransactionType(type.split(" ")[0]);
            history.setPaymentMethod(type.split(" ")[1]);
            history.setTotalAmount(total);
            history.setWalletChange(change);
            history.setWalletBalance(balance);
            history.setTransactionLink(transactionLink);
            history.setCreateDatetime(new Date());
            historyList.add(history);
        }
        return historyList;
    }

    private static String getCursorFromHtml(String html) {
        // 简单提取 g_historyCursor (可以用正则匹配)
        int idx = html.indexOf("g_historyCursor = '");
        if (idx == -1) return null;
        int start = idx + "g_historyCursor = '".length();
        int end = html.indexOf("'", start);
        return html.substring(start, end);
    }

    public LoginSteamDataVo getSessionInfo(String steamAccount, String steamPwd, String mafile) {
        return new LoginSteamDataVo();
    }

    public static JSONObject getInventoryHistoryByUserId(TSteamAccount steamAccount, String cursorTime, String cursorS) {
        if(!ECookieFlag.VALID.getCode().equals(steamAccount.getCookieFlag())){
            return null;
        }

        // 使用限流工具
        RateLimiterUtil.acquire(RateLimiterConfig.STEAM_INVENTORY_HISTORY_LIMITER);

        String url = "https://steamcommunity.com/profiles/" + getSteamIdByCookie(steamAccount) + "/inventoryhistory/?ajax=1&cursor%5Btime%5D=" + cursorTime
                + "&cursor%5Btime_frac%5D=0&cursor%5Bs%5D=" + cursorS
                + "&sessionid=" + steamAccount.getLastSessionid() + "&app%5B%5D=730";
//        String jsonResponse = MyHttpUtil.getMethedGetResponse(url, "timezoneOffset=28800,0; " + steamAccount.getLastCookie());

        String jsonResponse = HttpUtil.getMethodByHead(url, getSimpleHeaders(steamAccount.getLastCookie()));

        log.error("请求对象");
        checkCookieValid(jsonResponse, steamAccount.getId());

        return JSONUtil.parseObj(jsonResponse);
    }

    public static BigDecimal getAssetExteriorValue(String steamId, String assetId, String d) {
        String inspectUrl = "steam://rungame/730/*****************/%2Bcsgo_econ_action_preview%20S" + steamId + "A" + assetId + d;
        String apiUrl = "https://api.csgofloat.com/?url=" + inspectUrl;

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)  // 設置連接超時時間，這裡是30秒
                .readTimeout(60, TimeUnit.SECONDS)     // 設置讀取超時時間，這裡是60秒
                .writeTimeout(30, TimeUnit.SECONDS)    // 設置寫入超時時間，這裡是30秒
                .build();

        Request request = new Request.Builder().get().url(apiUrl)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Referer", "https://csfloat.com")
                .addHeader("Origin", "https://csfloat.com")
                .addHeader("user-agent",
                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36")
                .build();

        Call call = okHttpClient.newCall(request);
        try {
            Response response = call.execute();
            String jsonStr = response.body().string();
            JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
            return jsonObject.getJSONObject("iteminfo").getBigDecimal("floatvalue");
        } catch (Exception e) {
            log.error("获取磨损度异常SteamId:{},assetId:{},d:{}", steamId, assetId, d, e);
        }
        return null;
    }

    public static SteamAPIResponseTradeHistory getTradeHistory(TSteamAccount sendAccount, Long start_after_time) {
        if(!ECookieFlag.VALID.getCode().equals(sendAccount.getCookieFlag())){
            return null;
        }

      StringBuffer urlStr = new StringBuffer()
              .append( "https://api.steampowered.com/IEconService/GetTradeHistory/v1/?access_token=")
              .append(sendAccount.getAccessToken())
              .append("&max_trades=100")
              .append("&start_after_time=").append(start_after_time)
              .append("&navigating_back=true")
              .append("&get_descriptions=true")
              .append("&language=schinese");

        String jsonResponse = HttpUtil.getMethodByHead(urlStr.toString(), new HashMap<String, String>() {{
            put("Accept-Language", "zh-CN");
        }});

        checkCookieValid(jsonResponse, sendAccount.getId());

        return  JSONUtil.parseObj(jsonResponse).getJSONObject("response").toBean(SteamAPIResponseTradeHistory.class);
    }

    public static SteamAPIResponseTradeHistory getTradeHistoryByTradeid(TSteamAccount sendAccount, Long start_after_tradeid) {
        if(!ECookieFlag.VALID.getCode().equals(sendAccount.getCookieFlag())){
            return null;
        }

        String url = "https://api.steampowered.com/IEconService/GetTradeHistory/v1/?access_token=" +
                sendAccount.getAccessToken() +
                "&max_trades=100" +
                "&start_after_tradeid=" + start_after_tradeid +
                "&navigating_back=true" + "&get_descriptions=true";

        String jsonResponse = HttpUtil.getMethodByHead(url, new HashMap<String, String>() {{
            put("Accept-Language", "zh-CN");
        }});

        checkCookieValid(jsonResponse, sendAccount.getId());

        return  JSONUtil.parseObj(jsonResponse).getJSONObject("response").toBean(SteamAPIResponseTradeHistory.class);
    }


    public static SteamAPIResponseGetTradeOffers getTradeOffers(TSteamAccount account) {

        String url = "https://api.steampowered.com/IEconService/GetTradeOffers/v1/?access_token=" +
                account.getAccessToken() +
                //发起的
                "&get_sent_offers=true" +
                //接受的
                "&get_received_offers=true" +
                "&get_descriptions=true" +
                "&historical_only=false";

        String jsonResponse = HttpUtil.getMethodByHead(url, new HashMap<String, String>() {{
            put("Accept-Language", "zh-CN");
        }});

        checkCookieValid(jsonResponse, account.getId());

//        log.info("{}获取offers:{}", account.getId(), jsonResponse);
        return JSONUtil.toBean(jsonResponse, SteamAPIResponseGetTradeOffers.class);
    }


    public static OfferOrderResultRes doSendOfferOrder(TSteamAccount fromSteamAccount, String refNo, String tradeUrl,
                                                       List<String> fromAssetIdList,
                                                       List<String> toAssetIdList) {
        String tradeofferid = null;
        String errMsg = null;

        // 创建 OkHttpClient 实例，并设置一些默认配置（可选）
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();

        //构造交易主体
        String partner = getSteamIdByTradeUrl(tradeUrl) + "";
        SteamOfferOrderTradeDto tradeDto = getSteamOfferOrderTradeDto(fromAssetIdList, toAssetIdList);
        // 构建 FormDataBody
        FormBody formBody = new FormBody.Builder()
                .add("sessionid", fromSteamAccount.getLastSessionid())
                .add("serverid", "1")
                .add("partner", partner)
                .add("tradeoffermessage", "")
                .add("json_tradeoffer", JSONUtil.toJsonStr(tradeDto)
//                        "{\"newversion\":true,\"version\":2,\"me\":{\"assets\":[],\"currency\":[],\"ready\":false},\"them\":{\"assets\":[{\"appid\":730,\"contextid\":\"2\",\"amount\":1,\"assetid\":\"***********\"}],\"currency\":[],\"ready\":false}}"
                )
                .add("captcha", "")
                .add("trade_offer_create_params", "{\"trade_offer_access_token\":\"" + getTokenFromUrl(tradeUrl) + "\"}")
                .build();

        // 创建请求对象，并添加 Cookies 到请求头中
        Request request = new Request.Builder()
                .url("https://steamcommunity.com/tradeoffer/new/send") // 替换为你的目标 URL
                .post(formBody)
                .addHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8")
                .addHeader("referer", tradeUrl)
                .addHeader("content-length", "502")
                .addHeader("Cookie", fromSteamAccount.getLastCookie()) // 替换为你的实际 Cookies

                .build();

        // 同步调用（在实际应用中，更推荐使用异步调用）
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 请求成功，处理响应
                Gson gson = new Gson();
                SteamTradeofferVo tradeofferVo = gson.fromJson(response.body().string(), SteamTradeofferVo.class);
                tradeofferid = tradeofferVo.getTradeofferid();

            } else {
                Gson gson = new Gson();
                SteamTradeofferVo tradeofferVo = gson.fromJson(response.body().string(), SteamTradeofferVo.class);
                //Response{protocol=http/1.1, code=401, message=Unauthorized, url=https://steamcommunity.com/tradeoffer/new/send}
                // 请求失败，处理错误响应
                log.error("报价No: " + refNo);
                log.error("请求失败: " + response.code());
                log.error("失败原因: " + tradeofferVo.getStrError());
                errMsg = tradeofferVo.getStrError();

                //cookie失效同步
                sendCookieValid(fromSteamAccount.getId());
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("请求过程中发生异常: " + e.getMessage());
            errMsg = "交易发起失败，请检查cookie是否过期";
        }

        return new OfferOrderResultRes(tradeofferid, errMsg);
    }


    public static SteamTradeDetailVo getOfferTradeId(TSteamAccount fromSteamAccount, String tradeOfferId) {
        String accessToken = fromSteamAccount.getAccessToken();

        StringBuffer sbUrl = new StringBuffer()
                .append("https://api.steampowered.com/IEconService/GetTradeOffer/v1/?access_token=")
                .append(accessToken)
                .append("&tradeofferid=")
                .append(tradeOfferId);
        String response = HttpUtil.getMethod(sbUrl.toString());

        try {
            return JSONUtil.toBean(response,SteamTradeDetailVo.class);
        } catch (Exception e) {
            log.error("查询交易[{}]返回信息:[{}]", tradeOfferId, response);
            log.error("错误信息:{}", e.getMessage());

            if(e.getMessage().contains(HttpStatusEnum.FORBIDDEN.getCode().toString())){
                SteamUtil.sendCookieValid(fromSteamAccount.getId());
            }

            return null;
        }
    }

    /**
     * 取消报价
     * @param fromSteamAccount
     * @param tradeOfferId
     */
    public static void cancel(TSteamAccount fromSteamAccount, String tradeOfferId) {
        // 创建 OkHttpClient 实例，并设置一些默认配置（可选）
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();

        // 构建 FormDataBody
        FormBody formBody = new FormBody.Builder()
                .add("sessionid", fromSteamAccount.getLastSessionid())
                .build();

        // 创建请求对象，并添加 Cookies 到请求头中
        Request request = new Request.Builder()
                .url("https://steamcommunity.com/tradeoffer/" + tradeOfferId + "/cancel") // 替换为你的目标 URL
                .post(formBody)
                .addHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8")
//                .addHeader("referer", "https://steamcommunity.com/profiles/*****************/tradeoffers/sent/")
                .addHeader("content-length", "34")
                .addHeader("Cookie", fromSteamAccount.getLastCookie()) // 替换为你的实际 Cookies
                .build();

        // 同步调用（在实际应用中，更推荐使用异步调用）
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 请求成功，处理响应
                String responseData = response.body().string();
                log.info("处理成功，响应数据: " + responseData);
            } else {
                // 请求失败，处理错误响应
                log.info("取消交易id[{}],请求不成功[{}]", tradeOfferId, response.code());
            }
        } catch (Exception e) {
            log.error("请求过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 拒绝报价
     * @param fromSteamAccount
     * @param tradeOfferId
     */
    public static void decline(TSteamAccount fromSteamAccount, String tradeOfferId) {
        // 创建 OkHttpClient 实例，并设置一些默认配置（可选）
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();

        // 构建 FormDataBody
        FormBody formBody = new FormBody.Builder()
                .add("sessionid", fromSteamAccount.getLastSessionid())
                .build();

        // 创建请求对象，并添加 Cookies 到请求头中
        Request request = new Request.Builder()
                .url("https://steamcommunity.com/tradeoffer/" + tradeOfferId + "/decline") // 替换为你的目标 URL
                .post(formBody)
                .addHeader("content-type", "application/x-www-form-urlencoded; charset=UTF-8")
//                .addHeader("referer", "https://steamcommunity.com/profiles/*****************/tradeoffers/sent/")
                .addHeader("content-length", "34")
                .addHeader("Cookie", fromSteamAccount.getLastCookie()) // 替换为你的实际 Cookies
                .build();

        // 同步调用（在实际应用中，更推荐使用异步调用）
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 请求成功，处理响应
                String responseData = response.body().string();
                log.info("处理成功，响应数据: " + responseData);
            } else {
                // 请求失败，处理错误响应
                log.info("拒绝交易id[{}],请求不成功[{}]", tradeOfferId, response.code());
            }
        } catch (Exception e) {
            log.error("请求过程中发生异常: " + e.getMessage());
        }
    }

    private static SteamOfferOrderTradeDto getSteamOfferOrderTradeDto(List<String> fromAssetIdList, List<String> toAssetIdList) {
        SteamOfferOrderTradeDto tradeOrder = new SteamOfferOrderTradeDto();
        tradeOrder.setNewversion(true);
        tradeOrder.setVersion(2);
        SteamOfferOrderPartyDto me = new SteamOfferOrderPartyDto();

        List<SteamOfferOrderAssetDto> assetDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fromAssetIdList)) {
            for (String assetid : fromAssetIdList) {
                SteamOfferOrderAssetDto assetDto = new SteamOfferOrderAssetDto(730, "2", 1, assetid);
                assetDtoList.add(assetDto);
            }
        }
        me.setAssets(assetDtoList);
        me.setCurrency(new ArrayList<>());
        me.setReady(true);

        tradeOrder.setMe(me);

        SteamOfferOrderPartyDto them = new SteamOfferOrderPartyDto();

        List<SteamOfferOrderAssetDto> themAssetDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(toAssetIdList)) {
            for (String assetid : toAssetIdList) {
                themAssetDtoList.add(new SteamOfferOrderAssetDto(730, "2", 1, assetid));
            }
        }
        them.setAssets(themAssetDtoList);
        them.setCurrency(new ArrayList<>());
        them.setReady(false);

        tradeOrder.setThem(them);

        return tradeOrder;
    }

    private static String getTokenFromUrl(String urlString) {
        if (urlString == null || !urlString.contains("token=")) {
            return null; // 如果 URL 为空或者不包含 token，直接返回 null
        }

        // 找到 token= 的起始位置
        int tokenStartIndex = urlString.indexOf("token=") + "token=".length();

        // 查找 token 的结束位置（下一个 & 或者字符串的结束）
        int tokenEndIndex = urlString.indexOf("&", tokenStartIndex);

        // 如果 & 未找到，说明 token 到字符串末尾
        if (tokenEndIndex == -1) {
            return urlString.substring(tokenStartIndex);
        }

        // 提取 token 的值
        return urlString.substring(tokenStartIndex, tokenEndIndex);
    }

    public static Long getSteamIdByTradeUrl(String tradeUrl) {
        // 正则表达式匹配 partner 参数
        String regex = "partner=(\\d+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(tradeUrl);

        if (matcher.find()) {
            // 获取 AccountID 并转换为 SteamID64
            long accountId = Long.parseLong(matcher.group(1));
            return accountId + 76561197960265728L;
        }

        // 如果匹配不到，返回 null
        return null;
    }


    /**
     * 确认某笔自己发起的 trade offer
     *
     * @param fromSteamAccount fromSteamAccount        登录账号的 Steam
     * @param tradeOfferId   你业务中保存的 TradeOfferId（字符串形式）
     * @return 手机确认接口返回的 JSON 字符串
     */
    public static Boolean confirmTradeOffer(TSteamAccount fromSteamAccount,  String tradeOfferId ) {
        if (org.apache.commons.lang3.StringUtils.isBlank(fromSteamAccount.getMafile())) {
            return false;
        }
        SteamDataRes steamInfo = getSteamInfo(fromSteamAccount.getMafile());
        String steamId = getSteamIdByCookie(fromSteamAccount);
        String identitySecret = null;
        if (steamInfo != null) {
            identitySecret = steamInfo.getIdentitySecret();
        }
        String cookie = fromSteamAccount.getLastCookie();
        // 1. 先拿到待确认列表
        SteamMobileConfirmations.ConfirmationListRes confirmationList = SteamMobileConfirmations.getConfirmationList(steamId, identitySecret, cookie);

        if (!confirmationList.isSuccess()){
            return false;
        }

        //关闭交易保护
        String s = HttpUtil.postByHeadAndBodyStr("https://steamcommunity.com//trade/new/acknowledge", "sessionid=" + fromSteamAccount.getLastSessionid() + "&message=1", getSimpleHeaders(fromSteamAccount.getLastCookie()));
        log.info("关闭交易保护:{}", s);

        for (SteamMobileConfirmations.ConfirmationItem conf : confirmationList.getConf()) {
            // 2. 只关心交易邀请 (type == 2)
            if (conf.getType() != 2) continue;

            // 3. 拿详情，解析 HTML（注意接口返回的字段名可能是 "html"）
            SteamMobileConfirmations.ConfirmationDetailsRes details = SteamMobileConfirmations.getConfirmationDetails(
                    steamId, identitySecret, cookie, conf.getId()
            );
            String foundOfferId = parseTradeOfferIdFromHtml(details.getHtml());
            if (tradeOfferId.equals(foundOfferId)) {
                // 4. 找到明细，发起 allow
                SteamMobileConfirmations.ConfirmationActionRes actionRes = SteamMobileConfirmations.confirmAction(
                        steamId,
                        identitySecret,
                        cookie,
                        conf.getId(),
                        conf.getNonce(),
                        true   // true = 通过，false = 拒绝
                );
                return actionRes.getSuccess();
            }
        }
        return false;
    }

    /**
     * 从 /mobileconf/details 返回的 HTML 片段中抽出 tradeoffer_xxxxxxx
     */
    private static String parseTradeOfferIdFromHtml(String html) {
        Matcher m = Pattern.compile("tradeofferid_(\\d+)").matcher(html);
        if (m.find()) {
            return m.group(1);
        }
        throw new IllegalStateException("解析 tradeOfferId 失败，html = " + html);
    }


    public static void main(String[] args) {
        TSteamAccount account = new TSteamAccount();
        account.setLastCookie("steamLoginSecure=*****************%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Mtq9uNRIBfoMNd37FHAlojkKcoElQT4iCOMEJqJsiyJ_sgCEMVFKIbRFAxgWf7Qqk9BPXfdOv86LOH8ykfp4CQ;sessionid=5389ca25aebff981755d1d5a");

        account.setLastSessionid("5389ca25aebff981755d1d5a");

        System.out.println("sessionid=" + account.getLastSessionid() + "&message=1");
        String s = HttpUtil.postByHeadAndBodyStr("https://steamcommunity.com//trade/new/acknowledge", "sessionid=" + account.getLastSessionid() + "&message=1", getSimpleHeaders(account.getLastCookie()));

        System.out.println(s);


        ////        SteamAPIResponseTradeHistory tradeHistory = getTradeHistory(account, 0L);
////
////        System.out.println(tradeHistory);
//
//
//        System.out.println(getInventoryByUserId(account, "730", "200", "", ""));

//        account.setAccessToken("eyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PboOb7FyMWLU2wpXZdG-a6g9jwhGaYUKhDs3KEnngW7-WvQFbW7R2Icpbm_nUxyzg0uczheigtdZiD2N9JPZAQ");
////        account.setLastCookie("steamLoginSecure=*****************%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ustwhHy0aLOoN0G650SMPkreb2_Er3LbAMAk6YsyGGobXQU8E5N1mDB8KuYUiF0mv2ReB98L5KcuPDj7izEJAg;sessionid=eb0084c07273301bc3bc1246");
////        account.setLastSessionid("eb0084c07273301bc3bc1246");
//
////        System.out.println(getInventoryHistoryByUserId(account, "0", "0"));
//
////                getTradeHistory(account,null);
//
////        String methodByHead = HttpUtil.getMethodByHead("https://steamcommunity.com/profiles/*****************/inventoryhistory/", new HashMap() {{
////            put("cookie", "sessionid=1ea3aa3477640695260d89e9; timezoneOffset=28800,0; browserid=*****************; 730_17workshopQueueTime=**********; webTradeEligibility=%7B%22allowed%22%3A1%2C%22allowed_at_time%22%3A0%2C%22steamguard_required_days%22%3A15%2C%22new_device_cooldown_days%22%3A0%2C%22time_checked%22%3A1745406381%7D; strResponsiveViewPrefs=touch; recentlyVisitedAppHubs=730%2C211; app_impressions=730@2_100100_100101_100106|730@2_9_100006_|730@2_9_100000_|730@2_9_100000_|730@2_9_100000_|730@2_9_100006_|730@2_9_screenshots_|730@2_9_100001_|730@2_9_100014_|730@2_9_100002_|730@2_9_100013_|211@2_9_100000_; strInventoryLastContext=730_2; steamCountry=HK%7Cdca35472b386bcc32bdfef93d8282070; steamLoginSecure=*****************%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WOxJGvQJ3PuPPwLh_YRxkT-TvK72t0-W5qVciivNvxie1O4WdUzZNNH84YBVtRvW2E3BPe_LLwhn3RyN8Q4yBQ");
////        }});
////
////        System.out.println(methodByHead);
//
//        String methodByHead = HttpUtil.getMethodByHead("https://steamcommunity.com/profiles/76561199771079160/inventoryhistory/?ajax=1&cursor%5Btime%5D=0&cursor%5Btime_frac%5D=0&cursor%5Bs%5D=0&sessionid=6d3b32498485d74afeef717c&app%5B%5D=730", new HashMap() {{
//            put("cookie", "steamLoginSecure=76561199771079160%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QfOINJTy6OLyXIzMg4MOzGOL2Xw7TNyjVbrYZ0E3rezIAPSm6g0qXJPmjlVQvvaK63Q3rEN8H8YCYZPKobnFDA;sessionid=6d3b32498485d74afeef717c");
//        }});

//        System.out.println(methodByHead);
    }
}

