package com.aiyoooo.service.biz.util;

import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.util.MyHttpUtil;
import com.aiyoooo.service.dao.vo.LoginSteamDataDetailVo;
import com.aiyoooo.service.dao.vo.LoginSteamDataVo;
import com.aiyoooo.service.dao.vo.LoginSteamSessionIdVo;
import com.aiyoooo.service.dao.vo.LoginSteamTokenDetailVo;
import com.aiyoooo.service.dao.vo.LoginSteamTokenVo;
import com.google.gson.Gson;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Steam Session服务工具类
 */
@Slf4j
public class SteamSessionUtil {

    //aiyo_prod_branch1=*************,aiyo_prod_branch2=************,aiyo_dev=***********,office=*************,aiyo_prod_main=*************
//    private static final String[] ipList = {"*************", "**************", "*************","*************"};

//    private static final String[] ipList = {"************", "*************", "*************","*************", "***********"};

    private static final String[] ipList = {"localhost"};

    public static String getSessionUrl(String ip) {
        Random random = new Random();
        int randomNumber = random.nextInt(ipList.length); // 生成 0-3 之间的随机数

        if (StringUtils.isBlank(ip)) {
            ip = ipList[randomNumber];
        }

        return "http://" + ip + ":3000/login-steam";
    }

    public static String getRefreshUrl(String ip) {
        Random random = new Random();
        int randomNumber = random.nextInt(ipList.length); // 生成 0-3 之间的随机数

        if (StringUtils.isBlank(ip)) {
            ip = ipList[randomNumber];
        }

        return "http://" + ip + ":3000/refresh-token";
    }

    /**
     * 根据账号，密码和共享密钥获取cookie
     */
    public static LoginSteamDataDetailVo getLoginSteam(String steamAccount, String steamPwd, String shareSecret, String ip) {
        String sessionUrl = getSessionUrl(ip);

        Map<String, Object> params = new HashMap<>();
        params.put("username", steamAccount);
        params.put("password", steamPwd);

        String sessionResult = MyHttpUtil.postJson(sessionUrl, params);
        Gson gson = new Gson();
        LoginSteamSessionIdVo loginSteamSessionIdVo = gson.fromJson(sessionResult, LoginSteamSessionIdVo.class);

        if (loginSteamSessionIdVo.getCode() != 200) {
            log.error("{}获取sessionId请求失败:{}", steamAccount, JSONUtil.toJsonStr(loginSteamSessionIdVo));

            if(SysConstants.RateLimitExceeded.equals(loginSteamSessionIdVo.getErrMsg())
                    || SysConstants.AccountLoginDeniedThrottle.equals(loginSteamSessionIdVo.getErrMsg())){

                throw new BizException(steamAccount + "获取sessionid失败,触发steam限流，访问url="+ sessionUrl);
            } else {
                throw new BizException(steamAccount + "获取sessionId请求失败");
            }
        }

        params.put("sessionId", loginSteamSessionIdVo.getData().getSessionId());
        try {
            params.put("code", SteamSuardUtil.generateSteamGuardCode(shareSecret));
        } catch (Exception e) {
            throw new BizException(steamAccount + "生成令牌码错误");
        }
        String result = MyHttpUtil.postJson(sessionUrl, params);
        LoginSteamDataVo loginSteamDataVo = gson.fromJson(result, LoginSteamDataVo.class);
        if (loginSteamDataVo.getCode() != 200) {
            log.error("{}获取cookie请求失败:{}", steamAccount, JSONUtil.toJsonStr(loginSteamDataVo));

            if(SysConstants.RateLimitExceeded.equals(loginSteamDataVo.getErrMsg())
                    || SysConstants.AccountLoginDeniedThrottle.equals(loginSteamDataVo.getErrMsg())){
                throw new BizException(steamAccount + "获取cookie失败,触发steam限流，访问url="+ sessionUrl);
            } else{
                throw new BizException("获取cookie请求失败:" + JSONUtil.toJsonStr(loginSteamDataVo));
            }
        }

        LoginSteamDataDetailVo loginSteamDataDetailVo = loginSteamDataVo.getData();
        loginSteamDataDetailVo.setSessionId(loginSteamDataVo.getData().getWebCookies().get(1).replace("sessionid=", ""));

        return loginSteamDataDetailVo;
    }

    /**
     * 根据账号，密码和共享密钥获取cookie
     */
    public static LoginSteamDataDetailVo getLoginSteamByTokenCode(String steamAccount, String steamPwd, String tokenCode, String ip) {
        String sessionUrl = getSessionUrl(ip);

        Map<String, Object> params = new HashMap<>();
        params.put("username", steamAccount);
        params.put("password", steamPwd);

        String sessionResult = MyHttpUtil.postJson(sessionUrl, params);
        Gson gson = new Gson();
        LoginSteamSessionIdVo loginSteamSessionIdVo = gson.fromJson(sessionResult, LoginSteamSessionIdVo.class);

        if (loginSteamSessionIdVo.getCode() != 200) {
            log.error("{}获取sessionId请求失败:{}", steamAccount, JSONUtil.toJsonStr(loginSteamSessionIdVo));
            throw new BizException(steamAccount + "获取sessionId请求失败");
        }

        params.put("sessionId", loginSteamSessionIdVo.getData().getSessionId());
        params.put("code", tokenCode);
        String result = MyHttpUtil.postJson(sessionUrl, params);
        LoginSteamDataVo loginSteamDataVo = gson.fromJson(result, LoginSteamDataVo.class);
        if (loginSteamDataVo.getCode() != 200) {
            log.error("{}获取cookie请求失败:{}", steamAccount, result);
            throw new BizException("令牌码错误或请求异常，请稍后再试");
        }

        LoginSteamDataDetailVo loginSteamDataDetailVo = loginSteamDataVo.getData();
        loginSteamDataDetailVo.setSessionId(loginSteamDataVo.getData().getWebCookies().get(1).replace("sessionid=", ""));

        return loginSteamDataDetailVo;
    }

    public static LoginSteamTokenDetailVo getAccessTokenByRefreshToken(String steamAccount, String refreshToken, String ip) {
        Map<String, Object> params = new HashMap<>();
        params.put("refreshToken", refreshToken);

        String tokenResult = MyHttpUtil.postJson(getRefreshUrl(ip), params);
        Gson gson = new Gson();
        LoginSteamTokenVo result = gson.fromJson(tokenResult, LoginSteamTokenVo.class);
        if (result.getCode().intValue() != 200) {
            log.error(steamAccount + "刷新token请求失败:{}", JSONUtil.toJsonStr(result));
            throw new BizException("刷新token请求失败:" + JSONUtil.toJsonStr(result));
        }

        return result.getData();
    }

    public static void main(String[] args) {
//        for (int i = 0; i < 1; i++) {
//        String steamAccount = "cswjm59161";
//        String steamPwd = "WFx9Zny2aQ6K";
//        String shareSecret = "btcZFaeUd2gemS2MFDCkEIDXf+Y=";
//        System.out.println(getLoginSteam(steamAccount, steamPwd, shareSecret));
//        }

//        for (int i = 0; i < 10; i++) {
//            String refreshToken = "eyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mCtgot48vx57cn-uuFWH8e6KdAXGsVZ7XQE0jnPdZQ07KTP8XAWksSkCoTU2gVzcQfLtqDg5Gf_8Yhpcpf7HBA";
//            System.out.println(getAccessTokenByRefreshToken(steamAccount, refreshToken));
//        }
    }
}