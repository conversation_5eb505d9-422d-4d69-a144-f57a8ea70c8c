package com.aiyoooo.service.biz.service;

import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.t.TSteamAccountInfoService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.service.t.TSteamOfferDetailService;
import com.aiyoooo.service.biz.service.t.TSteamOfferService;
import com.aiyoooo.service.biz.service.tsys.TsysApikeyService;
import com.aiyoooo.service.biz.util.HttpUtil;
import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.biz.util.SysConstants;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.util.DateUtil;
import com.aiyoooo.service.common.util.SnowflakeIdGenerator;
import com.aiyoooo.service.dao.dto.BaseDetailDto;
import com.aiyoooo.service.dao.dto.SteamOfferBuyReq;
import com.aiyoooo.service.dao.dto.SteamOfferSellReq;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountInfo;
import com.aiyoooo.service.dao.entity.t.TSteamOffer;
import com.aiyoooo.service.dao.entity.t.TSteamOfferDetail;
import com.aiyoooo.service.dao.entity.tsys.SysApikey;
import com.aiyoooo.service.dao.enums.EBoolean;
import com.aiyoooo.service.dao.enums.ESteamOfferStatus;
import com.aiyoooo.service.dao.enums.ESteamOfferTradeState;
import com.aiyoooo.service.dao.enums.ESteamOfferTypeEnum;
import com.aiyoooo.service.dao.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.manager.util.SessionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Steam交易报价服务
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class SteamOfferService {

    @Resource
    private TSteamOfferService tSteamOfferService;

    @Resource
    private TSteamOfferDetailService tSteamOfferDetailService;

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountInfoService tSteamAccountInfoService;

    @Resource
    private TsysApikeyService tsysApikeyService;

    @Transactional(rollbackFor = Exception.class)
    public BaseCreateVo sell(SteamOfferSellReq req) {

        TSteamOffer data = new TSteamOffer();
        data.setId(SnowflakeIdGenerator.getNextId());
        data.setType(ESteamOfferTypeEnum.PLATFORM_SELL.getCode());
        data.setCallbackUrl(req.getCallbackUrl());
        data.setAccountId(req.getAccountId());
        data.setTradeUrl(req.getTradeUrl());
        data.setQuantity(req.getAssetidList().size());
        data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_0.getCode());
        data.setCreateDatetime(new Date());
        tSteamOfferService.save(data);

        //新增明细订单
        List<TSteamOfferDetail> detailList = new ArrayList<>();

        for (String assetid : req.getAssetidList()) {
            TSteamOfferDetail detail = new TSteamOfferDetail();
            detail.setOfferId(data.getId());
            detail.setAssetid(assetid);
            detail.setAmount(1);
            detailList.add(detail);
        }

        tSteamOfferDetailService.saveBatch(detailList);

        return new BaseCreateVo(data.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseCreateVo buy(SteamOfferBuyReq request) {
        TSteamAccountInfo accountInfo = tSteamAccountInfoService.getById(request.getAccountId());
        if (accountInfo == null) {
            throw new BizException("account not exist");
        }

        TSteamOffer data = new TSteamOffer();
        data.setId(SnowflakeIdGenerator.getNextId());
        data.setType(ESteamOfferTypeEnum.PLATFORM_BUY.getCode());
        data.setAccountId(request.getAccountId());
        data.setTradeUrl(accountInfo.getTradeUrl());
        data.setTradeOfferId(request.getTradeOfferId());
        data.setQuantity(request.getAssetidList().size());
        data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_2.getCode());
        data.setCallbackUrl(request.getCallbackUrl());
        data.setCreateDatetime(new Date());
        tSteamOfferService.save(data);

        //新增明细订单
        List<TSteamOfferDetail> detailList = new ArrayList<>();
        for (String assetid : request.getAssetidList()) {
            TSteamOfferDetail detail = new TSteamOfferDetail();
            detail.setOfferId(data.getId());
            detail.setAssetid(assetid);
            detail.setAmount(1);
            detailList.add(detail);
        }

        tSteamOfferDetailService.saveBatch(detailList);
        return new BaseCreateVo(data.getId());
    }


    public static void main(String[] args) {

        List<String> assetidList = new ArrayList<>();
        assetidList.add("***********");

        TSteamAccount fromSteamAccount = new TSteamAccount();
        fromSteamAccount.setLastSessionid("db9d39a46220ea607654b55b");
        fromSteamAccount.setLastCookie("steamLoginSecure=*****************%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jDFuHGU9N9aAvmbagqBpUkF6miudPG-n5Nb_ljSKgxWP1SUAbzJVKvLGzgzmZlseX4QvoPQZ25wZOc7Cs5w0Cw;sessionid=db9d39a46220ea607654b55b");

        OfferOrderResultRes tradeResult = SteamUtil.doSendOfferOrder(fromSteamAccount, "", "https://steamcommunity.com/tradeoffer/new/?partner=**********&token=JwvOHREi"
                , assetidList, null);

    }


    @Transactional(rollbackFor = Exception.class)
    public void doCheckTradeStatus(Long id) {
        TSteamOffer data = getOfferForUpdate(id);

        TSteamAccount fromSteamAccount = tSteamAccountService.getById(data.getAccountId());

        //超时取消处理
        doCheckTimeoutCancel(data, fromSteamAccount);

        boolean rows = false;
        if (ESteamOfferStatus.STEAM_OFFER_STATUS_0.getCode().equals(data.getStatus())) {
            List<TSteamOfferDetail> steamOfferDetails = tSteamOfferDetailService.listByOfferId(data.getId());

            List<String> assetidList = steamOfferDetails.stream().map(TSteamOfferDetail::getAssetid).collect(Collectors.toList());

            OfferOrderResultRes tradeResult = SteamUtil.doSendOfferOrder(fromSteamAccount, data.getId().toString(), data.getTradeUrl().trim(), assetidList, null);

            if (StringUtils.isBlank(tradeResult.getTradeofferid())) {
                data.setRemark(tradeResult.getErrMsg());

                //交易链接错误
                if (SysConstants.TRADE_URL_ERROR.equals(tradeResult.getErrMsg())) {
                    data.setCancelReason("买家交易链接失效,系统自动取消");
                    data.setFinishDatetime(new Date());
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_6.getCode());

                } else if (SysConstants.TRADE_URL_ERROR_26.equals(tradeResult.getErrMsg())) {
                    data.setCancelReason("賬號异常,系统自动取消");
                    data.setFinishDatetime(new Date());
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_6.getCode());

                } else if (SysConstants.CD_ERROR.equals(tradeResult.getErrMsg())) {
                    data.setCancelReason("发货账号异常,系统自动取消");
                    data.setFinishDatetime(new Date());
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_6.getCode());

                } else if (null != tradeResult.getErrMsg() && tradeResult.getErrMsg().contains(SysConstants.TOO_MANY_TRADE)) {
                    data.setCancelReason("买方太多订单未处理,系统自动取消");
                    data.setFinishDatetime(new Date());
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_4.getCode());

                } else if (null != tradeResult.getErrMsg() && tradeResult.getErrMsg().contains(SysConstants.NOT_AVAILABLE_URL)
                        && tradeResult.getErrMsg().lastIndexOf(SysConstants.NOT_AVAILABLE_URL_END) > 0) {
                    data.setCancelReason("买家交易链接失效,系统自动取消");
                    data.setFinishDatetime(new Date());
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_4.getCode());
                }
            } else {
                data.setTradeOfferId(tradeResult.getTradeofferid());
                data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_1.getCode());
            }
            rows = tSteamOfferService.updateById(data);
//            rows = steamOfferMapper.updateByPrimaryKeySelective(data);
        } else if (ESteamOfferStatus.STEAM_OFFER_STATUS_1.getCode().equals(data.getStatus())
                || ESteamOfferStatus.STEAM_OFFER_STATUS_2.getCode().equals(data.getStatus())) {

            SteamTradeDetailVo result = SteamUtil
                    .getOfferTradeId(fromSteamAccount, data.getTradeOfferId());

            if (null != result && null != result.getResponse() && null != result.getResponse().getOffer()) {
                if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_1.getCode() == result.getResponse().getOffer()
                        .getTradeOfferState()) {
                    //交易无效，暂不处理，可能是交易链接无效，待验证
                } else if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_2.getCode() == result.getResponse().getOffer()
                        .getTradeOfferState()) {
                    //待买家确认
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_2.getCode());

                } else if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_5.getCode() == result.getResponse().getOffer().getTradeOfferState()
                        || ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_6.getCode() == result.getResponse().getOffer().getTradeOfferState()
                        || ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_7.getCode() == result.getResponse().getOffer().getTradeOfferState()
                        || ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_8.getCode() == result.getResponse().getOffer().getTradeOfferState()
                        || ESteamOfferTradeState.STEAM_OFFER_ORDER_STATE_10.getCode() == result.getResponse().getOffer()
                        .getTradeOfferState()) {
                    data.setFinishDatetime(new Date());

                    if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_5.getCode() == result.getResponse().getOffer()
                            .getTradeOfferState()) {

                        data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_4.getCode());
                        data.setCancelReason("买家超时未接受报价，系统取消订单");
                    } else if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_6.getCode() == result.getResponse().getOffer()
                            .getTradeOfferState() || ESteamOfferTradeState.STEAM_OFFER_ORDER_STATE_10.getCode() == result.getResponse()
                            .getOffer()
                            .getTradeOfferState()) {

                        data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_6.getCode());
                        data.setCancelReason("卖家自主取消订单");
                    } else if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_7.getCode() == result.getResponse().getOffer()
                            .getTradeOfferState()) {

                        data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_5.getCode());
                        data.setCancelReason("买家自主取消订单");
                    } else if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_8.getCode() == result.getResponse().getOffer()
                            .getTradeOfferState()) {
                        data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_7.getCode());
                        data.setCancelReason("物品已不在卖家库存中,系统自动取消");

                    }
                } else if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_3.getCode() == result.getResponse().getOffer()
                        .getTradeOfferState()) {
                    //交易成功
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_3.getCode());
                    data.setFinishDatetime(new Date());

                } else if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_4.getCode() == result.getResponse().getOffer()
                        .getTradeOfferState()) {
                    //发起还价不处理，认为买家责任
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_5.getCode());
                    data.setCancelReason("买家发起还价,系统取消");
                } else if (ESteamOfferTradeState.STEAM_OFFER_TRADE_STATE_9.getCode() == result.getResponse().getOffer()
                        .getTradeOfferState()) {

                    SysApikey apiKey = tsysApikeyService.getByApiKey(fromSteamAccount.getAuthCode());

                    if (apiKey != null && EBoolean.YES.getCode().equals(apiKey.getOfferAutoSwitch())) {
                        //开始确认
                        SteamUtil.confirmTradeOffer(fromSteamAccount, data.getTradeOfferId());
                    }

//                    //待手机确认，卖家未手机确认，不用处理
//                    if (data.getType().equals(ESteamOfferTypeEnum.PLATFORM_SELL.getCode())) {
//
//                    }

                } else if (ESteamOfferTradeState.STEAM_OFFER_ORDER_STATE_11.getCode() == result.getResponse().getOffer()
                        .getTradeOfferState()) {
                    //交易暂挂
                    data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_9.getCode());
                    data.setFinishDatetime(new Date());
                }

                data.setTradeOfferJson(JSONUtil.toJsonStr(result));

                rows = tSteamOfferService.updateById(data);
            }
        }

        if (!rows) {
            return;
        }

        if (ESteamOfferStatus.STEAM_OFFER_STATUS_3.getCode().equals(data.getStatus())) {
            tSteamOfferDetailService.doHandleNewAssetid(data);
        }

        if (StringUtils.isBlank(data.getCallbackUrl())){
            return;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("offerId", data.getId());
        map.put("orderNo", data.getOrderNo());
        map.put("status", data.getStatus());
        map.put("sign", tsysApikeyService.generateMd5Signature(map, fromSteamAccount.getAuthCode()));

        try {
            String res = HttpUtil.post(data.getCallbackUrl(), map);
            log.info("通知渠道:{}", res);
        } catch (Exception e) {
            log.info("通知渠道异常", e);
        }
    }

    private void doCheckTimeoutCancel(TSteamOffer data, TSteamAccount steamAccount) {

//        if(ESteamOfferStatus.STEAM_OFFER_STATUS_0.getCode().equals(data.getStatus())){
//
//            Date date = new Date();
//            Date afterDate = DateUtil.getRelativeDateOfSecond(data.getCreateDatetime(), 60 * 30);
//            //当前时间如果大于创建时间+30分钟,且卖家未发货，则取消发货
//            if(date.compareTo(afterDate) > 0){
//                data.setStatus(ESteamOfferStatus.STEAM_OFFER_STATUS_4.getCode());
//                data.setCancelReason("订单超时发货，系统取消");
//            }
//
//            tSteamOfferService.updateById(data);
//        }else

        if(ESteamOfferStatus.STEAM_OFFER_STATUS_1.getCode().equals(data.getStatus())){

            Date date = new Date();
            Date afterDate = DateUtil.getRelativeDateOfSecond(data.getCreateDatetime(), 60 * 30);
            //当前时间如果大于创建时间+30分钟,且卖家未发货，则取消发货
            if(date.compareTo(afterDate) > 0){
                SteamUtil.cancel(steamAccount, data.getTradeOfferId());
            }

        } else if(ESteamOfferStatus.STEAM_OFFER_STATUS_2.getCode().equals(data.getStatus())){
            Date date = new Date();
            Date afterDate = DateUtil.getRelativeDateOfSecond(data.getCreateDatetime(), 60 * 60 * 12);
            //当前时间如果大于创建时间+12小时
            if(date.compareTo(afterDate) > 0){
                SteamUtil.cancel(steamAccount, data.getTradeOfferId());
            }
        }
    }


    /**
     * 分页查询交易报价
     *
     * @param page    分页参数
     * @param wrapper wrapper
     * @return 分页结果
     */
    public Page<TSteamOffer> pageOffer(Page<TSteamOffer> page, LambdaQueryWrapper<TSteamOffer> wrapper) {
        return tSteamOfferService.page(page, wrapper);
    }


    /**
     * 获取交易报价详情
     *
     * @param id 交易ID
     * @return 交易报价详情
     */
    public TSteamOffer getOfferForUpdate(Long id) {
        QueryWrapper<TSteamOffer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).last("FOR UPDATE");
        return tSteamOfferService.getOne(queryWrapper);
    }

    /**
     * 根据Steam订单号获取交易报价
     *
     * @param tradeOfferId Steam订单号
     * @return 交易报价
     */
    public TSteamOffer getByTradeOfferId(String tradeOfferId) {
        if (tradeOfferId == null || tradeOfferId.isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<TSteamOffer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSteamOffer::getTradeOfferId, tradeOfferId);
        return tSteamOfferService.getOne(wrapper);
    }


    public SteamOfferVo detail(BaseDetailDto req) {
        TSteamOffer offer =null;
        if (req.getId() != null){
             offer = tSteamOfferService.getById(req.getId());
        }else if (StringUtils.isNoneBlank(req.getOrderNo())){
            LambdaQueryWrapper<TSteamOffer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TSteamOffer::getOrderNo,req.getOrderNo());
            offer = tSteamOfferService.getOne(queryWrapper);
        }

        if (offer == null) {
            throw new BizException("offer not exist");
        }
        SteamOfferVo res = new SteamOfferVo();
        BeanUtils.copyProperties(offer, res);

        List<SteamOfferDetailVo> details = new ArrayList<>();
        tSteamOfferDetailService.listByOfferId(offer.getId()).forEach(detail -> {
            SteamOfferDetailVo detailVo = new SteamOfferDetailVo();
            BeanUtils.copyProperties(detail, detailVo);
            details.add(detailVo);
        });

        res.setDetails(details);
        return res;
    }
}