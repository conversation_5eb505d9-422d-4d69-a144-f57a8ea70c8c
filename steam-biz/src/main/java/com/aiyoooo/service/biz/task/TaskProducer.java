// SteamTaskProducer.java
package com.aiyoooo.service.biz.task;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.SteamOfferService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.service.t.TSteamOpenBoxTaskService;
import com.aiyoooo.service.common.util.RedisUtil;
import com.aiyoooo.service.common.util.SnowflakeIdGenerator;
import com.aiyoooo.service.dao.dto.SteamOpenBoxDto;
import com.aiyoooo.service.dao.dto.SteamSyncTask;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamOffer;
import com.aiyoooo.service.dao.entity.t.TSteamOpenBoxTask;
import com.aiyoooo.service.dao.enums.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;

@Slf4j
@Service
public class TaskProducer {

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private SteamOfferService steamOfferService;

    @Resource
    private TSteamOpenBoxTaskService tSteamOpenBoxTaskService;

    @Resource
    private RedisUtil redisUtil;


    private static final int BATCH_SIZE = 100;
    // 任务去重的过期时间（24小时）
    private static final long TASK_EXPIRE_TIME = 10 * 60;


    public void initAccount(TSteamAccount account) {
        for (TaskTypeEnum taskType : TaskTypeEnum.values()) {
            if (!taskType.getModel().equals("account")){
                continue;
            }
            createAndPushTask(taskType, account.getId(), null);
        }
    }

    // 检查任务是否存在并添加标记
    private boolean checkAndAddTask(TaskTypeEnum taskType, Long accountId) {
        String taskKey = accountId.toString();
        Boolean isMember = redisUtil.sHasKey(taskType.getSetKey(), taskKey);
        if (Boolean.TRUE.equals(isMember)) {
            return true;
        }
        // 如果任务不存在，添加到Set中并设置过期时间
        redisUtil.sSetAndTime(taskType.getSetKey(), TASK_EXPIRE_TIME, taskKey);
        return false;
    }

    // 创建任务并添加到队列
    public void createAndPushTask(TaskTypeEnum taskType, Long bizLockId, JSONObject params) {
        if (checkAndAddTask(taskType, bizLockId)) {
            return;
        }
        SteamSyncTask task = SteamSyncTask.builder()
                .taskId(UUID.randomUUID().toString())
                .bizLockId(bizLockId)
                .taskType(taskType)
                .retryCount(0)
                .createTime(System.currentTimeMillis())
                .params(params)
                .build();
        redisUtil.push(taskType.getQueueKey(), JSONUtil.toJsonStr(task));
    }

    public void produceCookieSyncTasks() {
        try {
            produceTasks(TaskTypeEnum.COOKIE_SYNC, wrapper -> wrapper
                    .eq(TSteamAccount::getCookieFlag, ECookieFlag.TODO.getCode())
                    .eq(TSteamAccount::getStatus, EAccountStatusEnum.BINDING.getCode())
                    .and(w -> w
                            .isNotNull(TSteamAccount::getMafile)
                            .or()
                            .isNotNull(TSteamAccount::getMafileDepart)
//                            .or()
//                            .isNotNull(TSteamAccount::getRefreshToken)
                    )
            );

        } catch (Exception e) {
            log.error("生产{}任务失败", TaskTypeEnum.COOKIE_SYNC.getDesc(), e);
        }
    }

    public void produceRefreshTokenTask() {
        try {
            produceTasks(TaskTypeEnum.REFRESH_TOKEN, wrapper -> wrapper
                    .eq(TSteamAccount::getCookieFlag, ECookieFlag.VALID.getCode())
                    .eq(TSteamAccount::getStatus, EAccountStatusEnum.BINDING.getCode())
                            .and(w ->
                                    w.isNotNull(TSteamAccount::getRefreshToken)
                                            .ne(TSteamAccount::getRefreshToken, "-1")
                            )
            );

        } catch (Exception e) {
            log.error("生产{}任务失败", TaskTypeEnum.REFRESH_TOKEN.getDesc(), e);
        }
    }

    public void produceBaseInfoSyncTasks() {
        try {
            produceTasks(TaskTypeEnum.BASE_INFO_SYNC, wrapper -> wrapper
                    .eq(TSteamAccount::getStatus, EAccountStatusEnum.BINDING.getCode())
                    .eq(TSteamAccount::getCookieFlag, ECookieFlag.VALID.getCode()));
        } catch (Exception e) {
            log.error("生产{}任务失败", TaskTypeEnum.BASE_INFO_SYNC.getDesc(), e);
        }
    }

    public void produceTradeUrlSyncTasks() {
        try {
            produceTasks(TaskTypeEnum.TRADE_URL_SYNC, wrapper -> {
                wrapper.eq(TSteamAccount::getStatus, EAccountStatusEnum.BINDING.getCode())
                        .eq(TSteamAccount::getCookieFlag, ECookieFlag.VALID.getCode())
                        .exists("SELECT 1 FROM t_steam_account_info info WHERE info.account_id = t_steam_account.id AND (info.trade_url IS NULL OR info.trade_url = '')");
            });
        } catch (Exception e) {
            log.error("生产{}任务失败", TaskTypeEnum.TRADE_URL_SYNC.getDesc(), e);
        }
    }

    public void produceApiKeySyncTasks() {
        try {
            produceTasks(TaskTypeEnum.API_KEY_SYNC, wrapper -> wrapper
                    .eq(TSteamAccount::getStatus, EAccountStatusEnum.BINDING.getCode())
                    .eq(TSteamAccount::getCookieFlag, ECookieFlag.VALID.getCode())
                    .exists("SELECT 1 FROM t_steam_account_info info WHERE info.account_id = t_steam_account.id AND (info.apikey IS NULL OR info.apikey = '')"));

        } catch (Exception e) {
            log.error("生产{}任务失败", TaskTypeEnum.API_KEY_SYNC.getDesc(), e);
        }
    }

    public void produceInventorySyncTasks(QueryLevelEnum queryLevel) {
        log.info("开始生产{}任务 - {}", TaskTypeEnum.INVENTORY_SYNC.getDesc(), queryLevel.getDesc());
        try {
            produceTasks(TaskTypeEnum.INVENTORY_SYNC, wrapper -> wrapper
                    .eq(TSteamAccount::getStatus, EAccountStatusEnum.BINDING.getCode())
                    .eq(TSteamAccount::getCookieFlag, ECookieFlag.VALID.getCode())
                    .eq(TSteamAccount::getQueryLevel, queryLevel.getCode()));
        } catch (Exception e) {
            log.error("生产{}任务失败 - {}", TaskTypeEnum.INVENTORY_SYNC.getDesc(), queryLevel.getDesc(), e);
        }
        log.info("生产{}任务结束 - {}", TaskTypeEnum.INVENTORY_SYNC.getDesc(), queryLevel.getDesc());
    }

    public void produceWalletSyncTasks() {
        try {
            produceTasks(TaskTypeEnum.WALLET_SYNC, wrapper -> wrapper
                    .eq(TSteamAccount::getStatus, EAccountStatusEnum.BINDING.getCode())
                    .eq(TSteamAccount::getCookieFlag, ECookieFlag.VALID.getCode()));
        } catch (Exception e) {
            log.error("生产{}任务失败", TaskTypeEnum.WALLET_SYNC.getDesc(), e);
        }
    }

    // 通用的任务生产方法
    private void produceTasks(TaskTypeEnum taskType, Consumer<LambdaQueryWrapper<TSteamAccount>> wrapperConfig) {
        log.info("开始生产{}任务", taskType.getDesc());

        int currentPage = 1;
        boolean hasNext = true;

        while (hasNext) {
            Page<TSteamAccount> page = new Page<>(currentPage, BATCH_SIZE);
            LambdaQueryWrapper<TSteamAccount> wrapper = new LambdaQueryWrapper<>();
            wrapperConfig.accept(wrapper);

            IPage<TSteamAccount> result = tSteamAccountService.page(page, wrapper);
            List<TSteamAccount> accounts = result.getRecords();

            if (accounts.isEmpty()) {
                break;
            }

            for (TSteamAccount account : accounts) {
                createAndPushTask(taskType, account.getId(), null);
            }

            hasNext = result.getCurrent() < result.getPages();
            currentPage++;
        }
        log.info("生产{}任务结束", taskType.getDesc());
    }

    public void produceSteamOffers() {
        log.info("开始生产{}任务", TaskTypeEnum.STEAM_OFFER_SYNC.getDesc());

        int currentPage = 1;
        boolean hasNext = true;

        while (hasNext) {
            Page<TSteamOffer> page = new Page<>(currentPage, 100);

            LambdaQueryWrapper<TSteamOffer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(TSteamOffer::getStatus, ESteamOfferStatus.STEAM_OFFER_STATUS_0.getCode(),
                    ESteamOfferStatus.STEAM_OFFER_STATUS_1.getCode()
                    , ESteamOfferStatus.STEAM_OFFER_STATUS_2.getCode()).orderByAsc(TSteamOffer::getId);

            Page<TSteamOffer> result = steamOfferService.pageOffer(page, queryWrapper);
            List<TSteamOffer> steamOfferList = result.getRecords();

            if (steamOfferList.isEmpty()) {
                break;
            }
            for (TSteamOffer data : steamOfferList) {
                createAndPushTask(TaskTypeEnum.STEAM_OFFER_SYNC, data.getId(), null);
            }
            hasNext = result.getCurrent() < result.getPages();
            currentPage++;
        }
    }


    public void processOpenCaseTasks() {
        log.info("开始生产{}任务", TaskTypeEnum.OPEN_BOX_SYNC.getDesc());

        int currentPage = 1;
        boolean hasNext = true;

        while (hasNext) {
            Page<TSteamOpenBoxTask> page = new Page<>(currentPage, 100);
            LambdaQueryWrapper<TSteamOpenBoxTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(TSteamOpenBoxTask::getStatus, EOpenBoxTaskStatusEnum.PENDING.getCode(),
                    EOpenBoxTaskStatusEnum.PROCESSING.getCode())
                    .orderByAsc(TSteamOpenBoxTask::getCreateTime);

            Page<TSteamOpenBoxTask> result = tSteamOpenBoxTaskService.page(page, queryWrapper);
            List<TSteamOpenBoxTask> records = result.getRecords();
            if (records.isEmpty()) {
                break;
            }

            records.forEach(task -> {
                createAndPushTask(TaskTypeEnum.OPEN_BOX_SYNC, task.getId(), null);
            });

            hasNext = result.getCurrent() < result.getPages();
            currentPage++;
        }

    }
}