package com.aiyoooo.service.biz.impl.t;

import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAssetOutStock;
import com.aiyoooo.service.dao.enums.ESteamAssetStatusEnum;
import com.aiyoooo.service.dao.mapper.TSteamAccountAssetMapper;
import com.aiyoooo.service.dao.mapper.t.TSteamAccountAssetOutStockMapper;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetOutStockService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Slf4j
@Service
public class TSteamAccountAssetOutStockServiceImpl extends ServiceImpl<TSteamAccountAssetOutStockMapper, TSteamAccountAssetOutStock> implements TSteamAccountAssetOutStockService {

    @Resource
    private TSteamAccountAssetMapper steamAccountAssetMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doMigrateAsset() {
        log.info("开始迁移已出库的Steam账号资产数据");
        
        try {
            int migratedCount = baseMapper.migrateFromAsset();
            
            if (migratedCount > 0) {
                log.info("成功迁移{}条已出库资产数据到出库表", migratedCount);
                
                // 从原表中删除已迁移的数据
                LambdaQueryWrapper<TSteamAccountAsset> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(TSteamAccountAsset::getInventoryStatus, ESteamAssetStatusEnum.OUT_OF_STOCK.getCode());
                int deletedCount = steamAccountAssetMapper.delete(deleteWrapper);
                
                log.info("成功从原表删除{}条已迁移的数据", deletedCount);
            } else {
                log.info("没有找到需要迁移的已出库资产数据");
            }
        } catch (Exception e) {
            log.error("迁移已出库资产数据失败", e);
            throw new RuntimeException("迁移已出库资产数据失败: " + e.getMessage(), e);
        }
    }
}
