package com.aiyoooo.service.biz.util;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/5/26 3:39 下午
 */
public class SysConstants {

    public static final String TRADE_URL_ERROR = "There was an error sending your trade offer.  Please try again later. (15)";

    public static final String TRADE_URL_ERROR_26 = "There was an error sending your trade offer.  Please try again later. (26)";

    public static final String CD_ERROR = "There was an error sending your trade offer.  Please try again later.<br><br>You must have had Steam Guard enabled for at least 15 days before you can participate in a trade.";

    public static final String TOO_MANY_TRADE = "You have sent too many trade offers, or have too many outstanding trade offers with";

    public static final String NOT_AVAILABLE_URL = "is not available to trade. More information will be shown to";

    public static final String NOT_AVAILABLE_URL_END = "if they invite you to trade.";

    public static final String RateLimitExceeded = "ERROR: This login attempt has failed! RateLimitExceeded";

    public static final String AccountLoginDeniedThrottle = "ERROR: This login attempt has failed! AccountLoginDeniedThrottle";

    public static final String ExTag = "ING";
}




