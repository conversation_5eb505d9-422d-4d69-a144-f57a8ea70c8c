package com.aiyoooo.service.biz.listener;

import com.aiyoooo.service.biz.event.SteamCookieInvalidEvent;
import com.aiyoooo.service.biz.service.SteamAccountService;
import com.aiyoooo.service.dao.dto.SteamAccountRefreshCookieFlagDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SteamCookieInvalidListener {

    @Resource
    private SteamAccountService steamAccountService;

    @EventListener
    public void onSteamCookieInvalid(SteamCookieInvalidEvent event) {
        log.warn("监听到Steam cookie失效事件，accountId={}", event.getAccountId());
        SteamAccountRefreshCookieFlagDto dto = new SteamAccountRefreshCookieFlagDto();
        dto.setId(event.getAccountId());
        steamAccountService.refreshCookiesFlag(dto);
    }
}