package com.aiyoooo.service.biz.impl.t;

import com.aiyoooo.service.biz.service.t.TSysOperationLogService;
import com.aiyoooo.service.dao.entity.t.TSysOperationLog;
import com.aiyoooo.service.dao.mapper.tsys.TSysOperationLogMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <p>
 * 系统操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class TSysOperationLogServiceImpl extends ServiceImpl<TSysOperationLogMapper, TSysOperationLog> implements TSysOperationLogService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLog(TSysOperationLog operationLog) {
        try {
//            log.info("开始同步保存日志: {}", operationLog.getAuthCode());
            return doSaveLog(operationLog);
        } catch (Exception e) {
            log.error("同步保存日志异常", e);
        }
        return false;
    }
    
    /**
     * 实际保存日志的逻辑
     */
    private boolean doSaveLog(TSysOperationLog operationLog) {
        try {
            // 确保authCode不为空
            if (!StringUtils.hasText(operationLog.getAuthCode())) {
                operationLog.setAuthCode("system");
                log.warn("操作日志的authCode为空，已设置为默认值'system'");
            }
            
            // 确保时间不为空
            if (operationLog.getOperationTime() == null) {
                operationLog.setOperationTime(LocalDateTime.now());
            }
            
            // 记录保存操作日志的重要信息
            String logMessage = String.format(
                "保存操作日志: authCode=[%s], module=[%s], operation=[%s], url=[%s], status=[%s]",
                operationLog.getAuthCode(),
                operationLog.getModule(),
                operationLog.getOperationType(),
                operationLog.getRequestUrl(),
                operationLog.getStatus()
            );
            
            //log.info(logMessage);
            
            // 保存日志
            boolean success = save(operationLog);
            
            // 记录保存结果
            if (success) {
//                log.info("操作日志保存成功, ID: {}", operationLog.getId());
            } else {
                log.warn("操作日志保存失败: {}", logMessage);
            }
            
            return success;
        } catch (Exception e) {
            log.error("保存操作日志异常: authCode={}, url={}", 
                    operationLog.getAuthCode(), operationLog.getRequestUrl(), e);
            return false;
        }
    }

} 