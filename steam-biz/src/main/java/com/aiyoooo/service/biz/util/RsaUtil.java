package com.aiyoooo.service.biz.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.io.pem.PemObjectGenerator;
import org.bouncycastle.util.io.pem.PemWriter;
import java.io.ByteArrayInputStream;
import java.io.FileWriter;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA加密工具类
 * <AUTHOR>
 * @since   2024/12/14
 */
public class RsaUtil {
    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 生成公钥和私钥
     * @throws Exception    异常
     */
    public static void generateKeyPair() throws Exception {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA", "BC");
        keyPairGenerator.initialize(2048);
        KeyPair keyPair = keyPairGenerator.genKeyPair();
        PrivateKey privateKey = keyPair.getPrivate();
        PublicKey publicKey = keyPair.getPublic();

        try (PemWriter pw = new PemWriter(new FileWriter("private_key.pem"))) {
            pw.writeObject((PemObjectGenerator) privateKey);
        }

        try (PemWriter pw = new PemWriter(new FileWriter("public_key.pem"))) {
            pw.writeObject((PemObjectGenerator) publicKey);
        }

        X509EncodedKeySpec spec = new X509EncodedKeySpec(publicKey.getEncoded());
        X509Certificate certificate = generateCertificate(spec);
        byte[] encodedCert = certificate.getEncoded();
        try (PemWriter pw = new PemWriter(new FileWriter("public_key_x509.pem"))) {
            pw.writeObject((PemObjectGenerator) certificate);
        }
        System.out.println("Public Key (X509)：" + Base64.getEncoder().encodeToString(encodedCert));
    }

    /**
     * 生成证书
     * @param spec
     * @return
     * @throws Exception
     */
    private static X509Certificate generateCertificate(X509EncodedKeySpec spec) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA", "BC");
        X509Certificate certificate = (X509Certificate) CertificateFactory.getInstance("X.509", "BC")
                .generateCertificate(new ByteArrayInputStream(spec.getEncoded()));
        certificate.checkValidity();
        certificate.verify(keyFactory.generatePublic(spec));
        return certificate;
    }
}
