package com.aiyoooo.service.biz.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.time.Instant;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.Data;

public class SteamMobileConfirmations {

    private static final String BASE_URL = "https://steamcommunity.com/mobileconf";


    public static void main(String[] args) {
//        String str ="{\"success\":false,\"message\":\"Invalid authenticator\",\"detail\":\"It looks like your Steam Guard Mobile Authenticator is providing incorrect Steam Guard codes. This could be caused by an inaccurate clock or bad timezone settings on your device. If your time settings are correct, it could be that a different device has been set up to provide the Steam Guard codes for your account, which means the authenticator on this device is no longer valid.\"}";
//        Gson gson = new Gson();
//        ConfirmationActionRes actionRes = gson.fromJson(str, ConfirmationActionRes.class);
//        System.out.println(actionRes);
//
//        String string ="{\"success\":true,\"conf\":[{\"type\":9,\"type_name\":\"Register API Key\",\"id\":\"***********\",\"creator_id\":\"2898227242301963026\",\"nonce\":\"13896353897881849070\",\"creation_time\":**********,\"cancel\":\"Deny\",\"accept\":\"Confirm\",\"icon\":\"https:\\/\\/community.steamstatic.com\\/public\\/images\\/mobile\\/Icon_addpartner.png\",\"multi\":false,\"headline\":\"Authorize a developer API Key with access to your account\",\"summary\":[\"\"],\"warn\":null}]}";
//        System.out.println(gson.fromJson(string, ConfirmationListRes.class));

        ConfirmationDetailsRes details = getConfirmationDetails("*****************", "d2fh+qZhJUaTz2fWd5aGL8RYPLw=",
                "steamLoginSecure=*****************%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6TqfSZPGBTL8qqwTCmOEq59siZ3IbDkAjgvGcEr3LF2UdZw56CgiyvyHLMh92hT82QCVadWx2slqwZnVwUWZCQ;sessionid=c20cfb7f65d8b13da9aba709",
                "***********");

        Matcher m = Pattern.compile("tradeofferid_(\\d+)").matcher(details.getHtml());
        if (m.find()) {
            System.out.println(m.group(1));
        }

        System.out.println(details);
    }

    public static ConfirmationListRes getConfirmationList(String steamId, String identitySecret, String cookie) {
        long timestamp = java.time.Instant.now().getEpochSecond();
        String tag = "conf";

        String deviceId = generateDeviceId(steamId);
        String confirmationKey = generateConfirmationKey(identitySecret, tag, timestamp);

        Map<String, String> headers = getDefaultHeaders(cookie);

        String url = BASE_URL + "/getlist?p=" + deviceId + "&a=" + steamId + "&k=" + confirmationKey + "&t=" + timestamp + "&m=android&tag=" + tag;
        String res = HttpUtil.getMethodByHead(url, headers);

        Gson gson = new Gson();
        return gson.fromJson(res, ConfirmationListRes.class);
    }

    public static ConfirmationActionRes confirmAction(String steamId, String identitySecret, String cookie, String confirmationId, String confirmationKeyVal, boolean allow)  {
        long timestamp = java.time.Instant.now().getEpochSecond();
        String tag = allow ? "allow" : "cancel";

        String deviceId = generateDeviceId(steamId);
        String confirmationKey = generateConfirmationKey(identitySecret, tag, timestamp);

        Map<String, String> headers = getDefaultHeaders(cookie);

        String url = BASE_URL + "/ajaxop?p=" + deviceId +
                "&a=" + steamId +
                "&k=" + confirmationKey +
                "&t=" + timestamp +
                "&m=android" +
                "&tag=" + tag +
                "&op=" + tag +
                "&cid=" + confirmationId +
                "&ck=" + confirmationKeyVal;

        String res = HttpUtil.getMethodByHead(url, headers);
        Gson gson = new Gson();
        return gson.fromJson(res, ConfirmationActionRes.class);
    }

    /**
     * 获取指定 confirmation 的详细信息（包含 HTML）。
     *
     * @param steamId        用户的 SteamID
     * @param identitySecret 用户的 identity_secret（Base64 编码）
     * @param cookie         完整的 Cookie 字符串
     * @param confirmationId 要查询的 confirmation ID
     * @return 接口返回的 JSON 字符串，其中包含 "html" 字段
     */
    public static ConfirmationDetailsRes getConfirmationDetails(String steamId,
                                               String identitySecret,
                                               String cookie,
                                               String confirmationId) {
        // 当前时间戳（秒）
        long timestamp = Instant.now().getEpochSecond();
        // tag 必须是 "details" + confirmationId
        String tag = "details" + confirmationId;

        // 设备 ID 和确认密钥
        String deviceId = generateDeviceId(steamId);
        String confirmationKey = generateConfirmationKey(identitySecret, tag, timestamp);

        // 默认请求头
        Map<String, String> headers = getDefaultHeaders(cookie);

        // 构建 URL
        String url = BASE_URL + "/details/" + confirmationId
                + "?p=" + deviceId
                + "&a=" + steamId
                + "&k=" + confirmationKey
                + "&t=" + timestamp
                + "&m=android"
                + "&tag=" + tag;

        String res = HttpUtil.getMethodByHead(url, headers);
        Gson gson = new Gson();
        return gson.fromJson(res, ConfirmationDetailsRes.class);
    }

    private static Map<String, String> getDefaultHeaders(String cookie) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", cookie);
        headers.put("X-Requested-With", "com.valvesoftware.android.steam.community");
        return headers;
    }

    public static String generateDeviceId(String steamId) {
        String base = "android:" + steamId;
        return "android-" + md5(base).substring(0, 16);
    }

    public static String generateConfirmationKey(String identitySecret, String tag, long timestamp) {
        byte[] identitySecretBytes = Base64.getDecoder().decode(identitySecret);
        byte[] data = new byte[8 + tag.length()];
        ByteBuffer buffer = ByteBuffer.allocate(8).putLong(timestamp);
        System.arraycopy(buffer.array(), 0, data, 0, 8);
        System.arraycopy(tag.getBytes(StandardCharsets.UTF_8), 0, data, 8, tag.length());

        try {
            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKeySpec keySpec = new SecretKeySpec(identitySecretBytes, "HmacSHA1");
            mac.init(keySpec);
            byte[] hash = mac.doFinal(data);

            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    // ======== Response Entities ========
    @Data
    public static class ConfirmationListRes {
        private boolean success;
        private boolean needauth;
        private List<ConfirmationItem> conf;
    }

    @Data
    public static class ConfirmationItem {
        private int type;
        @SerializedName("type_name")
        private String typeName;
        private String id;
        @SerializedName("creator_id")
        private String creatorId;
        private String nonce;
        @SerializedName("creation_time")
        private long creationTime;
        private String cancel;
        private String accept;
        private String icon;
        private boolean multi;
        private String headline;
        private List<String> summary;
        private List<String> warn;

    }

    @Data
    public static class ConfirmationDetailsRes {
        private String html;
    }

    @Data
    public static class ConfirmationActionRes {

        private Boolean success;

        private String message;

        private String detail;
    }
}