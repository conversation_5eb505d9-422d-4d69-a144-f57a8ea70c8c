package com.aiyoooo.service.biz.impl.t;

import com.aiyoooo.service.biz.service.t.TSteamOfferService;
import com.aiyoooo.service.dao.entity.t.TSteamOffer;
import com.aiyoooo.service.dao.mapper.TSteamOfferMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * steam交易报价 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Service
public class TSteamOfferServiceImpl extends ServiceImpl<TSteamOfferMapper, TSteamOffer> implements TSteamOfferService {


} 