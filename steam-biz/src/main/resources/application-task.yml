# 任务系统配置示例
task:
  # 消费者数量 - 每种任务类型会启动这么多个消费者线程
  consumer-count: 2
  
  # 关闭超时时间（秒）- 应用关闭时等待任务完成的最大时间
  shutdown-timeout: 30
  
  # Redis 队列配置
  redis:
    # 任务队列前缀
    queue-prefix: "steam:task:queue:"
    # 任务去重集合前缀  
    set-prefix: "steam:task:set:"
    # 任务过期时间（秒）
    task-expire-time: 600
    
  # 任务重试配置
  retry:
    # 最大重试次数
    max-count: 3
    # 重试间隔（毫秒）
    interval: 5000
    
  # 监控配置
  monitor:
    # 是否启用监控接口
    enabled: true
    # 监控接口路径前缀
    path-prefix: "/api/task/monitor"
