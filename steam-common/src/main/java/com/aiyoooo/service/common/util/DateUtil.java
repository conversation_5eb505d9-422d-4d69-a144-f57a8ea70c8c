package com.aiyoooo.service.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtil  {

    public static String YYYY = "yyyy";
    public static String YYYY_MM = "yyyy-MM";
    public static String YYYY_MM_DD = "yyyy-MM-dd";
    public static String YYYYMMDD = "yyyyMMdd";
    public static String YYYY_MM_DD_PATH = "yyyy/MM/dd";
    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String HH_MM_SS = "HH:mm:ss";
    public static String YYYY_MM_DD_HH = "yyyy-MM-dd HH";
    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static String YYYY_MM_DD_HH_MM_SS_MILLIS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static String YYYY_MM_DD_HH_ZW = "yyyy年MM月dd日";

    public static String HHmm = "HH:mm";

    private static String[] parsePatterns = {
            "yyyy-MM-dd",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy-MM",
            "yyyy/MM/dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm",
            "yyyy/MM",
            "yyyy.MM.dd",
            "yyyy.MM.dd HH:mm:ss",
            "yyyy.MM.dd HH:mm",
            "yyyy.MM"
    };

    /**
     * 获取当前时间戳（单位秒）
     */
    public static Long getTimestamp() {
        return System.currentTimeMillis() / 1000;
    }

    public static Long getTimestamp(Date date) {
        Long timestamp = null;

        if (null != date) {
            timestamp = date.getTime() / 1000;
        }

        return timestamp;
    }

    /**
     * 获取当前时间戳（单位毫秒）
     */
    public static Long getMillisTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    /**
     * 获取当前日期, 默认格式为yyyyMMdd
     *
     * @return String
     */
    public static String getDate(String format) {
        if (StringUtils.isBlank(format)) {
            format = YYYYMMDD;
        }

        return dateTimeNow(format);
    }

    /**
     * 获取昨天开始时间
     *
     * @create: 2014-10-14 下午4:24:57 miyb
     * @history:
     */
    public static Date getYesterdayStart() {
        Calendar currentDate = new GregorianCalendar();
        currentDate.add(Calendar.DAY_OF_MONTH, -1); // 设置为前一天
        currentDate.set(Calendar.HOUR_OF_DAY, 0);
        currentDate.set(Calendar.MINUTE, 0);
        currentDate.set(Calendar.SECOND, 0);
        currentDate.set(Calendar.MILLISECOND, 000);

        return (Date) currentDate.getTime().clone();
    }

    /**
     * 获取当前日期, 默认格式为 yyyy/MM/dd
     *
     * @return String
     */
    public static String getDatePath() {
        return dateTimeNow(YYYY_MM_DD_PATH);
    }

    /**
     * 根据时间戳获取年月日时分秒
     *
     * @param timestamp 秒级时间戳
     * @return yyyy-MM-dd HH:mm:ss
     */
    public static String getAllTime(Long timestamp) {
        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, new Date(timestamp * 1000));
    }

    public static String getDateTime(Long timestamp) {
        return parseDateToStr(YYYY_MM_DD, new Date(timestamp * 1000));
    }

    /**
     * 根据时间戳获取年月日时分秒毫秒
     *
     * @param timestamp 毫秒级时间戳
     * @return yyyy-MM-dd HH:mm:ss.SSS
     */
    public static String getAllTimeOfMillis(Long timestamp) {
        return parseDateToStr(YYYY_MM_DD_HH_MM_SS_MILLIS, new Date(timestamp));
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String ts, final String format) {
        if (StringUtils.isBlank(ts)) {
            return null;
        }

        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将秒数格式化成时间字符串（HH:mm:ss）
     *
     * @return java.lang.String
     */
    public static String parseTime(Integer seconds) {
        return DateFormatUtils.format(seconds * 1000 - TimeZone.getDefault().getRawOffset(), "HH:mm:ss");
    }

    /**
     * 时分秒对比 s1晚于s2返回true
     */
    public static boolean compTime(String s1, String s2) {
        try {
            if (s1.indexOf(":") < 0 || s1.indexOf(":") < 0) {
                System.out.println("格式不正确");
            } else {
                String[] array1 = s1.split(":");
                int total1 = Integer.valueOf(array1[0]) * 3600 + Integer.valueOf(array1[1]) * 60 + Integer.valueOf(array1[2]);
                String[] array2 = s2.split(":");
                int total2 = Integer.valueOf(array2[0]) * 3600 + Integer.valueOf(array2[1]) * 60 + Integer.valueOf(array2[2]);
                return total1 - total2 > 0 ? true : false;
            }
        } catch (NumberFormatException e) {
            return true;
        }
        return false;
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    public static long getDateByDay(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天

        return diff / nd;
    }


    /**
     * 获取当前月的第一天 最早时间
     *
     * <AUTHOR>
     * @date 2017-3-25 - 上午10:57:27
     */
    public static Date getCurrMonthFirstDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, 1);// 设置为1号,当前日期既为本月第一天
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /**
     * 获取当前月的最后一天 最晚时间
     *
     * <AUTHOR>
     * @date 2017-3-25 - 上午11:00:34
     */
    public static Date getCurrMonthLastDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 23);
        // 将分钟至0
        c.set(Calendar.MINUTE, 59);
        // 将秒至0
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }


    /**
     * 获取当天的最早时间
     *
     * <AUTHOR>
     * @date 2017-3-25 - 上午11:35:28
     */
    public static Date getCurrDayFirstTime() {
        Calendar c = Calendar.getInstance();
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /**
     * 获取当天的最晚时间
     *
     * <AUTHOR>
     * @date 2017-3-25 - 上午11:35:28
     */
    public static Date getCurrDayLastTime() {
        Calendar c = Calendar.getInstance();
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 23);
        // 将分钟至0
        c.set(Calendar.MINUTE, 59);
        // 将秒至0
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    /**
     * 获取昨天的最早时间
     *
     * <AUTHOR>
     * @date 2017-3-25 - 上午11:35:28
     */
    public static Date getYesterDayFirstTime() {
        Calendar c = Calendar.getInstance();
        //减去一天
        c.add(Calendar.DAY_OF_MONTH, -1);
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    public static Date getYesterDayFirstTime(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //减去一天
        c.add(Calendar.DAY_OF_MONTH, -1);
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /**
     * 获取昨天的最晚时间
     *
     * <AUTHOR>
     * @date 2017-3-25 - 上午11:35:28
     */
    public static Date getYesterDayLastTime() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, -1);
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 23);
        // 将分钟至0
        c.set(Calendar.MINUTE, 59);
        // 将秒至0
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    public static Long getUnixTimestap() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        return calendar.getTimeInMillis() / 1000;
    }

    public static long secondBetween(Date beginDate, Date endDate) {
        long times = endDate.getTime() - beginDate.getTime();
        return times / 1000L;
    }

    public static Integer millisByMins(Integer mins) {
        return mins * 60 * 1000;
    }

    /**
     * 创建时间文字化
     */
    public static String createTimetoString(Date createtime) {
        Date now = new Date();
        long i = secondBetween(createtime, now);
        if (i < 0) {
            return "MINUS TIME ERROR";
        } else if (i < 60 && i >= 0) {
            return i + "秒前";
        } else if (i < 3600 && i >= 60) {
            return i / 60 + "分前";
        } else if (i < 86400 && i >= 3600) {
            return i / 3600 + "小时前";
        } else if (i >= 86400) {
            return DateFormatUtils.format(createtime, YYYY_MM_DD);
        }
        return "ERROR";
    }



    /**
     * 统计两个时间差，返回的是s
     *
     * @create: 2015年11月16日 上午11:20:51 myb858
     * @history:
     */
    public static long secondsBetween(Date beginDate, Date endDate) {
        long times = endDate.getTime() - beginDate.getTime();
        return (long) (times / 1000);
    }

    public static Date getDateSetSecond(Date date, int second) {
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(date);
            calendar.set(Calendar.SECOND, second);
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTime();
        } catch (Exception e) {
            return date;
        }
    }

    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }


    public static Date getDateSetMinute(Date date, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(date);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, second);
            return calendar.getTime();
        } catch (Exception e) {
            return date;
        }
    }

    public static Date getDateSetHour(Date date, int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            calendar.set(Calendar.MINUTE, minute);
            calendar.set(Calendar.SECOND, second);
            return calendar.getTime();
        } catch (Exception e) {
            return date;
        }
    }

    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }


    public static int getRelativeWeekDay(Date date) {
        int dayWeek = 0;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (calendar.get(Calendar.DAY_OF_WEEK) == 1) {
            dayWeek = 6;
        } else {
            dayWeek = calendar.get(Calendar.DAY_OF_WEEK) - 2;
        }

        return dayWeek;
    }


    public static Date getPreMonthFirstDay() {
        // 获取前月的第一天
        Calendar cal = Calendar.getInstance(); // 获取当前日期
        cal.add(Calendar.MONTH, -1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    public static Date getPreMonthLastDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 0); // 设置为1号,当前日期既为本月第一天
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }


    public static Date getYearStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(calendar.MONTH, 0);
        calendar.set(calendar.DAY_OF_MONTH, 1);
        calendar.set(calendar.HOUR_OF_DAY, 0);
        calendar.set(calendar.MINUTE, 0);
        calendar.set(calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static Date getPreYearStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(calendar.MONTH, -12);
        return calendar.getTime();
    }

    public static Date getNextYearStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(calendar.MONTH, 12);
        return calendar.getTime();
    }

    public static Date getDate(Date date, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(calendar.MONTH, month); // 把日期往后增加一个月.整数往后推,负数往前移动
        return calendar.getTime(); // 这个时间就是日期往后推一天的结果
    }

    public static Date getMMMddyyyyDate(String dateString) {
        // 转换 am/pm 为 AM/PM
        dateString = dateString.replace("am", "AM").replace("pm", "PM");

        // 定义日期解析格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM d, yyyy h:mma", Locale.ENGLISH);

        try {
            LocalDateTime localDateTime = LocalDateTime.parse(dateString, formatter);
            // 指定时区
            ZoneId zoneId = ZoneId.systemDefault();

            // 将 LocalDateTime 转换为 ZonedDateTime
            ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

            // 将 ZonedDateTime 转换为 Date
            return Date.from(zonedDateTime.toInstant());

        } catch (DateTimeParseException e) {
            try {
                formatter = DateTimeFormatter.ofPattern("d MMM, yyyy h:mma", Locale.ENGLISH);

                LocalDateTime localDateTime = LocalDateTime.parse(dateString, formatter);
                // 指定时区
                ZoneId zoneId = ZoneId.systemDefault();

                // 将 LocalDateTime 转换为 ZonedDateTime
                ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

                // 将 ZonedDateTime 转换为 Date
                return Date.from(zonedDateTime.toInstant());
            } catch (Exception exception) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy MM dd a hh:mm");
                try {
                    // 解析日期字符串为 Date 对象
                    return sdf.parse(dateString);
                } catch (ParseException ee) {
                    System.out.println(dateString + "日期无法转义" + exception.getMessage());
                }
            }
        }

        return null;
    }

    public static Date convertToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static int getCurrentZoneUTCSecond() {

        // 获取当前服务器的时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 获取当前时间的时区信息
        ZonedDateTime zonedDateTime = ZonedDateTime.now(zoneId);

        // 获取当前时区的偏移量
        ZoneOffset offsetTime = zonedDateTime.getOffset();

        return offsetTime.getTotalSeconds();
    }

    public static LocalDate getCurrentDayOfWeek(DayOfWeek targetDay) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 获取当前日期
        LocalDate today = now.toLocalDate();

        LocalDate thisDay = today.with(targetDay);
        if (today.isBefore(thisDay)) {
            // 如果今天还没到周二，则取上周二
            thisDay = thisDay.minusWeeks(1);
        }

        return thisDay;
    }

    public static LocalDate getDayOfWeek(LocalDate currenctDate, DayOfWeek targetDay) {

        LocalDate thisDay = currenctDate.with(targetDay);
        if (currenctDate.isBefore(thisDay)) {
            // 如果今天还没到周二，则取上周二
            thisDay = thisDay.minusWeeks(1);
        }

        return thisDay;
    }

    // 计算两个Date日期之间的天数差
    public static long getDaysBetween(Date startDate, Date endDate) {
        // 获取时间戳（毫秒级）
        long diffInMillies = endDate.getTime() - startDate.getTime();

        // 将毫秒差转换为天数
        return TimeUnit.MILLISECONDS.toDays(diffInMillies);
    }

    public static String getYesterday() {
        LocalDate yesterday = LocalDate.now().minusDays(1);

        return yesterday.toString();
    }


    public static Date strToDate(String str, String pattern) {
        Date date = null;
        SimpleDateFormat formate = new SimpleDateFormat(pattern);

        try {
            date = formate.parse(str);
        } catch (Exception var5) {
        }

        return date;
    }

    public static Date getRelativeDateOfSecond(Date startDate, int second) {
        Calendar calendar = Calendar.getInstance();

        try {
            calendar.setTime(startDate);
            calendar.add(13, second);
            return calendar.getTime();
        } catch (Exception var4) {
            return startDate;
        }
    }
}