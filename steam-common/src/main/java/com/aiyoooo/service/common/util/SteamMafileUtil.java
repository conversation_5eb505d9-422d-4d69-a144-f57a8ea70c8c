package com.aiyoooo.service.common.util;

import com.aiyoooo.service.common.vo.SteamDataRes;
import com.google.gson.Gson;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> xieyj
 * @since : 2025/2/24 14:10
 */
@Slf4j
public class SteamMafileUtil {

//    public static void main(String[] args) {
//        String url = "https://aiyoooo.s3.us-east-1.amazonaws.com/939780643469557761";
//        System.out.println(getSteamInfo(url).getSession().getSteamID());
//        System.out.println(getSteamInfo(url).getRevocationCode());
//    }

    public static SteamDataRes getSteamInfo(String fileUrl) {
        Gson gson = new Gson();
        if (!fileUrl.contains("http")) {
            return gson.fromJson(fileUrl, SteamDataRes.class);
        }

        try {
            String json = getContentFromUrl(fileUrl);
            return gson.fromJson(json, SteamDataRes.class);
        } catch (IOException e) {
            log.error("解析steamId出错[{}]", e.getMessage());
        }

        return null;
    }

    public static String getSteamInfoStr(String fileUrl) {
        if(StringUtils.isBlank(fileUrl)){
            return null;
        }

        if (!fileUrl.contains("http")) {
            return fileUrl;
        }

        try {
            return getContentFromUrl(fileUrl);
        } catch (IOException e) {
            log.error("解析steamId出错[{}]", e.getMessage());
        }

        return null;
    }

    private static String getContentFromUrl(String urlString) throws IOException {
        StringBuilder content = new StringBuilder();
        HttpURLConnection connection = null;

        try {
            URL url = new URL(urlString);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            int status = connection.getResponseCode();
            if (status != HttpURLConnection.HTTP_OK) {
                throw new IOException("HTTP request failed with status: " + status);
            }

            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return content.toString();
    }
}


