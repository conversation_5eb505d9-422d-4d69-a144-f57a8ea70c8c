package com.aiyoooo.service.common.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.common.bean.HttpQueryBean;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 网络通讯工具封装
 *
 * <AUTHOR>
 * @since 2024/12/10
 */
public class MyHttpUtil {

    public static final Logger logger = LoggerFactory.getLogger(MyHttpUtil.class);
    private static final int HTTP_TIMEOUT = 20000;
    private static final String JSON_CONTENT_TYPE = "application/json; charset=utf-8";
    private static final String FORM_CONTENT_TYPE = "application/x-www-form-urlencoded; charset=utf-8";
    private static final String TEXT_CONTENT_TYPE = "text/html; charset=utf-8";

    /**
     * HTTP POST请求，获取Form数据
     *
     * @param queryBean 请求参数对象
     * @return 请求结果
     */
    public static String postForm(HttpQueryBean queryBean) {
        if (Objects.isNull(queryBean.getHeaders())) {
            queryBean.setHeaders(new HashMap<>());
        }
        queryBean.getHeaders().put("Content-Type", FORM_CONTENT_TYPE);
        return getPostForm(queryBean);
    }

    /**
     * HTTP GET请求，获取Form数据
     *
     * @param queryBean 请求参数对象
     * @return 请求结果
     */
    public static String getForm(HttpQueryBean queryBean) {
        if (Objects.isNull(queryBean.getHeaders())) {
            queryBean.setHeaders(new HashMap<>());
        }
        queryBean.getHeaders().put("Content-Type", FORM_CONTENT_TYPE);
        return getQueryString(queryBean);
    }

    /**
     * HTTP GET请求，获取Form数据
     *
     * @param url 请求地址
     * @param headers 头信息
     * @return 请求结果
     */
    public static String getForm(String url, Map<String, String> headers) {
        HttpQueryBean queryBean = new HttpQueryBean();
        queryBean.setUrl(url);
        queryBean.setHeaders(headers);
        queryBean.setTimeout(HTTP_TIMEOUT);
        return getForm(queryBean);
    }

    /**
     * HTTP GET请求，获取Form数据
     *
     * @param url 请求地址
     * @return 请求结果
     */
    public static String getForm(String url) {
        HttpQueryBean queryBean = new HttpQueryBean();
        queryBean.setUrl(url);
        queryBean.setTimeout(HTTP_TIMEOUT);
        return getForm(queryBean);
    }

    /**
     * HTTP GET请求，获取HTML数据
     *
     * @param queryBean 请求参数对象
     * @return 请求结果
     */
    public static String getHtml(HttpQueryBean queryBean) {
        if (Objects.isNull(queryBean.getHeaders())) {
            queryBean.setHeaders(new HashMap<>());
        }
//        queryBean.getHeaders().put("Content-Type", TEXT_CONTENT_TYPE);
        return getQueryString(queryBean);
    }

    /**
     * HTTP GET请求，获取HTML数据
     *
     * @param url 请求地址
     * @param headers 头信息
     * @return 请求结果
     */
    public static String getHtml(String url, Map<String, String> headers) {
        HttpQueryBean queryBean = new HttpQueryBean();
        queryBean.setUrl(url);
        queryBean.setTimeout(HTTP_TIMEOUT);
        queryBean.setHeaders(headers);
        return getHtml(queryBean);
    }

    /**
     * 提交JSON请求
     *
     * @param queryBean 请求参数对象
     * @return 请求结果
     */
    public static String postJson(HttpQueryBean queryBean) {
        if (Objects.isNull(queryBean.getHeaders())) {
            queryBean.setHeaders(new HashMap<>());
        }
        queryBean.getHeaders().put("Content-Type", JSON_CONTENT_TYPE);
        return getPostJson(queryBean);
    }

    /**
     * HTTP POST JSON请求
     *
     * @param headers 请求头信息
     * @param url 请求地址
     * @param body 请求参数对象
     * @return 请求结果
     */
    public static String postJson(Map<String, String> headers, String url, Map<String, Object> body) {
        HttpQueryBean queryBean = new HttpQueryBean();
        queryBean.setHeaders(headers);
        queryBean.setUrl(url);
        queryBean.setBodyParams(body);
        queryBean.setTimeout(HTTP_TIMEOUT);
        return postJson(queryBean);
    }

    /**
     * HTTP POST JSON请求
     *
     * @param url 请求地址
     * @param body 请求参数对象
     * @return 请求结果
     */
    public static String postJson(String url, Map<String, Object> body) {
        HttpQueryBean queryBean = new HttpQueryBean();
        queryBean.setBodyParams(body);
        queryBean.setUrl(url);
        queryBean.setTimeout(HTTP_TIMEOUT);
        return postJson(queryBean);
    }

    /**
     * HTTP GET请求，获取JSON返回值
     *
     * @param queryBean 请求参数对象
     * @return 请求结果
     */
    public static String getJson(HttpQueryBean queryBean) {
        if (Objects.isNull(queryBean.getHeaders())) {
            queryBean.setHeaders(new HashMap<>());
        }
        queryBean.getHeaders().put("Content-Type", JSON_CONTENT_TYPE);
        return getQueryString(queryBean);
    }

    /**
     * HTTP GET请求，获取JSON返回值
     *
     * @param url 请求地址
     * @return 请求结果
     */
    public static String getJson(String url) {
        HttpQueryBean queryBean = new HttpQueryBean();
        queryBean.setUrl(url);
        queryBean.setTimeout(HTTP_TIMEOUT);
        return getJson(queryBean);
    }

    /**
     * HTTP GET请求，获取JSON返回值
     *
     * @param url 请求地址
     * @param headers 请求头信息
     * @return 请求结果
     */
    public static String getJson(String url, Map<String, String> headers) {
        HttpQueryBean queryBean = new HttpQueryBean();
        queryBean.setUrl(url);
        queryBean.setHeaders(headers);
        queryBean.setTimeout(HTTP_TIMEOUT);
        return getJson(queryBean);
    }

    /**
     * HTTP DELETE请求，获取JSON返回值
     *
     * @param queryBean 请求参数对象
     * @return 请求结果
     */
    public static String deleteJson(HttpQueryBean queryBean) {
        if (Objects.isNull(queryBean.getHeaders())) {
            queryBean.setHeaders(new HashMap<>());
        }
        queryBean.getHeaders().put("Content-Type", JSON_CONTENT_TYPE);
        return getDeleteJson(queryBean);
    }

    /**
     * HTTP POST JSON请求
     *
     * @param queryBean 请求参数
     * @return 查询结果
     */
    public static String getPostJson(HttpQueryBean queryBean) {
        String url = getQueryParamString(queryBean.getUrl(), queryBean.getQueryParams());
        HttpRequest request = HttpRequest.post(url);
        if (Objects.nonNull(queryBean.getHeaders())) {
            request.addHeaders(queryBean.getHeaders());
        }
        if (Objects.nonNull(queryBean.getBodyParams())) {
            request.body(JSONUtil.toJsonStr(queryBean.getBodyParams()));
        }
        if (Objects.nonNull(queryBean.getBody())) {
            request.body(queryBean.getBody());
        }
        if (Objects.nonNull(queryBean.getTimeout())) {
            request.setConnectionTimeout(queryBean.getTimeout());
        }
        try (HttpResponse response = request.execute()) {
            return response.body();
        } catch (Exception e) {
            logger.error("POST请求异常，错误信息：{}", e.getMessage());
            return null;
        }
    }

    /**
     * HTTP POST Form请求
     *
     * @param queryBean 请求参数对象
     * @return 查询结果
     */
    public static String getPostForm(HttpQueryBean queryBean) {
        String url = getQueryParamString(queryBean.getUrl(), queryBean.getQueryParams());
        HttpRequest request = HttpRequest.post(url);
        if (Objects.nonNull(queryBean.getHeaders())) {
            request.addHeaders(queryBean.getHeaders());
        }
        if (Objects.nonNull(queryBean.getBodyParams())) {
            request.form(queryBean.getBodyParams());
        }
        if (Objects.nonNull(queryBean.getTimeout())) {
            request.setConnectionTimeout(queryBean.getTimeout());
        }
        try (HttpResponse response = request.execute()) {
            return response.body();
        } catch (Exception e) {
            logger.error("POST表单请求异常，错误信息：{}", e.getMessage());
            return null;
        }
    }

    /**
     * HTTP GET请求
     *
     * @param queryBean 请求参数对象
     * @return 查询结果
     */
    private static String getQueryString(HttpQueryBean queryBean) {
        String url = getQueryParamString(queryBean.getUrl(), queryBean.getQueryParams());
        HttpRequest request = HttpRequest.get(url);
        if (Objects.nonNull(queryBean.getHeaders())) {
            request.addHeaders(queryBean.getHeaders());
        }
        if (Objects.nonNull(queryBean.getHttpProxy())) {
            request.setHttpProxy(queryBean.getHttpProxy().getHost(), queryBean.getHttpProxy().getPort());
        }
        if (Objects.nonNull(queryBean.getTimeout())) {
            request.setConnectionTimeout(queryBean.getTimeout());
        }
        try (HttpResponse response = request.execute()) {
            return response.body();
        } catch (Exception e) {
            logger.error("GET请求异常，错误信息：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 删除请求，获取JSON返回值
     *
     * @param queryBean 请求参数
     * @return 请求结果
     */
    public static String getDeleteJson(HttpQueryBean queryBean) {
        String url = getQueryParamString(queryBean.getUrl(), queryBean.getQueryParams());
        HttpRequest request = HttpRequest.delete(url);
        if (Objects.nonNull(queryBean.getHeaders())) {
            request.addHeaders(queryBean.getHeaders());
        }
        if (Objects.nonNull(queryBean.getBodyParams())) {
            request.body(JSONUtil.toJsonStr(queryBean.getBodyParams()));
        }
        if (Objects.nonNull(queryBean.getBody())) {
            request.body(queryBean.getBody());
        }
        if (Objects.nonNull(queryBean.getTimeout())) {
            request.setConnectionTimeout(queryBean.getTimeout());
        }
        try (HttpResponse response = request.execute()) {
            return response.body();
        } catch (Exception e) {
            logger.error("DELETE请求异常，错误信息：{}", e.getMessage());
            return null;
        }
    }


    /**
     * 获取POST请求参数
     *
     * @param url 请求地址
     * @param queryParams 请求参数
     * @return 请求地址
     */
    private static String getQueryParamString(String url, Map<String, Object> queryParams) {
        if (Objects.isNull(queryParams)) {
            return url;
        }
        if (!queryParams.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            sb.deleteCharAt(sb.length() - 1);
            url = url + "?" + sb.toString();
        }
        return url;
    }

    /**
     * 获取最终的跳转地址
     *
     * @param url 请求地址
     * @return 目标地址
     */
    public static String getFinalUrl(String url, Boolean cleanParams) {
        String finalUrl;
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setInstanceFollowRedirects(false);
            connection.connect();
            int code = connection.getResponseCode();
            if (code >= 300 && code < 400) {
                finalUrl = connection.getHeaderField("Location");
                if (!finalUrl.startsWith("http")) {
                    URL u = new URL(url);
                    String domain = u.getHost();
                    String protocol = u.getProtocol();
                    finalUrl = protocol + "://" + domain + finalUrl;
                }
            } else {
                finalUrl = url;
            }
            connection.disconnect();
        } catch (IOException e) {
            logger.error("跳转请求异常，错误信息：{}", e.getMessage());
            return null;
        }
        if (cleanParams) {
            finalUrl = finalUrl.lastIndexOf("?") == -1 ? finalUrl : finalUrl.substring(0, finalUrl.indexOf("?"));
        }
        return finalUrl;
    }

    /**
     * 获取域名
     *
     * @param urlStr 请求地址
     * @return 域名
     */
    public static String getHost(String urlStr) {
        if (!urlStr.startsWith("http")) {
            urlStr = "http://" + urlStr;
        }
        try {
            URL url = new URL(urlStr);
            return url.getHost();
        } catch (MalformedURLException e) {
            logger.error("获取主域名异常，错误信息：{}", e.getMessage());
        }
        return null;
    }

    public static String getMethedGetResponse(String requestStr, String cookie) {
        HttpURLConnection connection = null;
        BufferedReader reader = null;

        try {
            // 创建 URL 对象
            URL url = new URL(requestStr);
            connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法为 GET
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 设置连接超时时间
            connection.setReadTimeout(30000);    // 设置读取超时时间

            // 添加请求头
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("Accept-Language", "zh-CN");
            connection.setRequestProperty("Cookie", cookie);

            // 发起请求并获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 读取响应
                InputStreamReader inputStreamReader = new InputStreamReader(connection.getInputStream(), "UTF-8");
                reader = new BufferedReader(inputStreamReader);
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            } else {
                throw new RuntimeException("HTTP 请求失败，响应码: " + responseCode);
            }
        } catch (Exception e) {
            throw new RuntimeException("网络请求异常，原因: " + e.getMessage(), e);
        } finally {
            // 关闭资源
            try {
                if (reader != null) {
                    reader.close();
                }
                if (connection != null) {
                    connection.disconnect();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}
