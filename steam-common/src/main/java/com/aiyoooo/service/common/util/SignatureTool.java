package com.aiyoooo.service.common.util;

import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.common.exception.BizException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.stereotype.Component;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/17 20:54
 */
@Component
public class SignatureTool {


    private static String privateKeyStrAll = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC2TYoBu3A36IpS"
            + "RzuoTfYbdouAz4QENmNqDeajhZEx+RctaI4hnA1mhrXyOIrgOWHIa3jod1iEJYYz"
            + "wr1ACsqeWI3nS/lZMaYeLULeE6in+pIRvjw+LxA6gsihTjjyzjFie9ZifknIttMw"
            + "mSDUiCo2g1DCpA/fdx03NO2YBesa8HCeO0Yc0ac7S+DyWvt2LyLwhQypkc7oc8KL"
            + "BuIReD1QnFk/zhjOqol16eDg1irCNnqs2OW4M4N3IDJ7eA2sDo3GLJgP6SOXBivp"
            + "X8EnFbNY2AP+y2FwSDBIKM8yZEe+644UI5YEe0aj7BVJb4lpuWATpsYMehEYZfSH"
            + "2kxiNweTAgMBAAECggEAPrXNk2r230SO0F7QlsS6tBfDRTwdHZXX/NNrUzTPDXGy"
            + "F1ywB7+2nF4amIf6vcb/bAIMRWtId6ywzX1fvHf52x57MeVgkN4TVq4bImmAysfX"
            + "F2NpNbraykXS3ioaTOvA8S/IP3lRyEmbvEvwjyuH2diyH0Jwc152HleGo5Tlmw4l"
            + "r2pNQ5EBj0OUt+riOi+YcQAPudRG2hBn61AAcUDXpas0JrCkfSVOjAaiMHJ8JRQ/"
            + "cargIdmIeTdHAFW48xBALkI8zvEBlq1JfvoJ+8fWXlLUl6zc/UXJqflJFToI78za"
            + "HRcvLIJEnAhnqiT/VGotF0HEDAwtyDMM6DTZii9+AQKBgQDn4FMhDtdOjz5EZo5j"
            + "YavV7ovRtrROAbsBX/ljWbNp3v3UOw967Q0/F5ugqcf8m8rZcdy65U9hU35YjM/y"
            + "mVxJ7snGPFT3+zbBhahCIziWk4RHUzMMQPm+hZYXkaiulKL7gulp7H+V1hoEWvNQ"
            + "WYHQEDpt6Is6iRWPHdFIvTNJ8QKBgQDJRObpHxOK/zVbMGoA23EaIMOfrIGPow0J"
            + "KmrucUJFpwDiNBxsTic90RMKWsuJ/7dXwsV1WoDQWQ06JBxICDxP2YnwVjz0Gy/j"
            + "+2MXNFqimxIdNamKTfr4ZPg/L1LI1nWE0+R1Fz1zDImHujJZepQ0+IjsPel1H+3c"
            + "1nvSavQFwwKBgQCRa1kXGWSkSyeE5ECWSTHoKAOeERRA+8rZEOSpjx763bOYvzV1"
            + "JWeyUs7yUqguX1I3TrZcq+U9p4xMYnsjrVR4tX3rifjL0MvFUFp2p/ocTQWfaalR"
            + "HQrDlqnLvDjqzCLu02opWP2m7bBBXPzNPmUwJI1QUo/GtSRFLjELikS5MQKBgGqu"
            + "3P6gfElm8S8+wFFn/9FroIWYeKxcLuK22ys8WXZ2CR/do1s9Kcu4cLFCYzTomJQq"
            + "J2QG7tSA/2PcFCmSQ/XHRzhfXbu+VWowq9t24rviGn3lHdKzdt4HFxbWzLisBGq9"
            + "rhDqmqXK9XM+HMYVFU3mlx0xy/dLT1eJUOS90E7lAoGAfJfmgTdiARBBTNm6MP4u"
            + "EHaU7FB4TLtynwg2lsNSNCf8cpZXzufTmyXwcr/cTh5lTJ06YcBTkZpxAHsF3XVN"
            + "4AkKJYyYG4mjfKzg4BtUtb9Ui4QIwMQTA3t/AWR6HzNvJTX+MsPnGOWBU6udKAGW"
            + "Uhgaj2O6qBSLv3KmWyMnqWA=";

    public static String publicKeyAll = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtk2KAbtwN+iKUkc7qE32G3aLgM+EBDZjag3mo4WRMfkXLWiOIZwNZoa18jiK4DlhyGt46HdYhCWGM8K9QArKnliN50v5WTGmHi1C3hOop/qSEb48Pi8QOoLIoU448s4xYnvWYn5JyLbTMJkg1IgqNoNQwqQP33cdNzTtmAXrGvBwnjtGHNGnO0vg8lr7di8i8IUMqZHO6HPCiwbiEXg9UJxZP84YzqqJdeng4NYqwjZ6rNjluDODdyAye3gNrA6NxiyYD+kjlwYr6V/BJxWzWNgD/sthcEgwSCjPMmRHvuuOFCOWBHtGo+wVSW+JablgE6bGDHoRGGX0h9pMYjcHkwIDAQAB";


    public static String generateSignature(Object obj, String privateKey) {
        String message = convertMessage(obj, "");
        return generateSignature(message, privateKey);
    }

    private static String convertMessage(Object obj, String skipSign) {
        ObjectMapper mapper = new ObjectMapper();
        TreeMap<String, Object> treeMap = null;
        try {
            String json = mapper.writeValueAsString(obj);

            TypeReference<TreeMap<String, Object>> typeRef = new TypeReference<TreeMap<String, Object>>() {
            };
            treeMap = mapper.readValue(json, typeRef);

        } catch (JsonProcessingException e) {

        }

        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, Object> entry : treeMap.entrySet()) {
            //跳过签名
            if (skipSign.equals(entry.getKey())) {
                continue;
            }

            if (!Objects.isNull(entry.getValue()) && !entry.getValue().toString().isEmpty()) {
                String keyValue = entry.getValue().toString();

                if (entry.getValue().getClass() == ArrayList.class) {
                    // 创建 Gson 实例
                    ObjectMapper objectMapper = new ObjectMapper();

                    try {
                        keyValue = objectMapper.writeValueAsString(entry.getValue());
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                } else if (entry.getValue().getClass().isArray()) {
                    // 创建 Gson 实例
                    ObjectMapper objectMapper = new ObjectMapper();

                    try {
                        keyValue = objectMapper.writeValueAsString(entry.getValue());
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                } else {
                    if (!isBaseType(entry.getValue())) {
                        // 创建 Gson 实例
                        ObjectMapper objectMapper = new ObjectMapper();

                        try {
                            keyValue = objectMapper.writeValueAsString(entry.getValue());
                        } catch (JsonProcessingException e) {
                            e.printStackTrace();
                        }
                    } else {
                        keyValue = entry.getValue().toString();
                    }
                }
                stringBuilder.append(entry.getKey()).append("=").append(keyValue).append("&");
            }
        }
        String valueText = stringBuilder.toString();
        String message = valueText.substring(0, valueText.length() - 1);
        return message;
    }

    private static String generateSignature(String message, String privateKeyStr) {
        try {
//            String privateKeyStr = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC0L1WSQX75S+ah61hOUIaIhetGAjJMYNa7e5ZKql2FiJoqM/H1bXfv2Qut8ngGjRB+pnamjM7Si1CvoNYFNVEg0Ru6sNaNmBkl4q+54Yo0a0zVClf4Jrbr+kXSSyAlfXu9EBpLlds7T4Ix1PuG39fNHze84nJySNctVfVEIFoLLq515wSLmD8NLhmrohTAiVJkR0StN+H1FlQUss1z7lp5h6FJa05fwtPHd78ou05ZGT8qcS++9wcj88vhblViNydBVr6fK77HjjGupBTd54iTg561ojmdj/Qt24Mp231oDil7pS21Fdu/ADWEKj6xpHF9jp5rzYlQfjuQ7SKA4hgzAgMBAAECggEACULfkpdkN9sTXSI0FT5lU9ISUDRfsnZNwzAsfuT3zpvBVMb7ukIopn7Q711lluycOu5CbfgRz1eSy5woM/dUFqw40SUyue9l54EZyCOUD3KubcrMz7fpsgJYoWD0I4kqGYMg3t6yeH9zMS2ByiJqPKRMoJxBBqfAy5c99oT1K9rhw3I3Xwb2MGitH/tWle++hf7bC5ee1wBc/KyO6Sz2PqLvzLtvtxURpdUNWk6VdFvhwaQsWCJiBV6E2Y1n4yVCqhP8d0T2jVvMCdaEMFUROwxHrjzbLPVaGxHQZfzsLcA1e4Mkv944lQj2VvBz33E5ApErmyHOQG8Xe6uwcbgbYQKBgQDlXPH858SDLeR0/XpP7VXaPNNhsGNvXwVlRyU/SMvu5n9yngGN2QTfMdWq1OPKZ8bD73se6PuAIGM2hEEOb2wTXdQk+b4iQdJpWFYY4UmITmFnxwVTpdT9UbkVYoCLmqeiDuDsnPscdhxj6E6aefaUiZ4bCCxI0ea0SAYqeitzEwKBgQDJHE13uARuqvke35hRLD7yuJSZzieE0QZ75AuEFLmCvw8kF859393H+ybL1f5eXauK4lpFQFMkPC4v5qNiCbIM/DK5A5hpdxPL3Fx/eacdteoI1nsMNXoOk8pPss+0AKt6ytfEE4JCtEWjVVjwVnOABeY9hhoPV+uz6e3p+ThKYQKBgEps+JEKW/f9Ik7cGZ5p8xvX9PeUFxACk2ujxHBenN4uqVh18ScoLq6GK0E9x5TcS+E0io0jKMkQuWDtVXbrrPQz1C0TZSEUnsRUR8vW/tdeJ7lXGWFlRFHW/On1hqTGUHMDxPTCk7FOmjPJJAh4Xw0gAs/evQ/8HJKPdIDAF+1FAoGAbG/nUVsWgLmNk9kKHxKVanieH4RqCJJQRbMDhpdKI7ZzKonvnC1+PxsroxLike091qgP9nElo08TtF5O0EjcCWZezyWiI5PCR9Ssx0tIEZwJ8fP76uxWwM9tcaW/mpYV4CLJTn8aaG8dcWFOkGB87JXBvIC3QnisrZi/4L2xXWECgYBg4c7YlyRpOiS2RE0H7CYUQEk+4jueK1s//yMv4Z7S8AV89bUkqIR1xCRua6uzRJoouWiKeCCS+MtQX6f3tTvdYXs3sZasdTSrjYju/wAJ2gCopaHhvQie9GN8U3Ah+lbXNJpDXk5dNi0xmYtZAN0/U1+KH4U9RmuAa0QmEaEPlQ==";
            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyStr);
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKeyObj = keyFactory.generatePrivate(spec);

            // 使用私钥进行签名
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKeyObj);
            signature.update(message.getBytes(StandardCharsets.UTF_8));
            byte[] signatureBytes = signature.sign();
            return Base64.getEncoder().encodeToString(signatureBytes);

        } catch (InvalidKeySpecException | NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            System.err.println("生成签名: " + e.getMessage());
            return "";
        }
    }

    /**
     * 判断object是否为基本类型
     */
    private static boolean isBaseType(Object object) {
        Class className = object.getClass();
        if (className.equals(Integer.class) ||
                className.equals(Byte.class) ||
                className.equals(Long.class) ||
                className.equals(Double.class) ||
                className.equals(Float.class) ||
                className.equals(Character.class) ||
                className.equals(Short.class) ||
                className.equals(Boolean.class) ||
                className.equals(String.class)) {
            return true;
        }

        return false;
    }

    public static void verifySignature(Object obj, String signed, String publicKeyStr) {
        String message = convertMessage(obj, "sign");
        boolean result = verifySignature(message, signed, publicKeyStr);
        if (!result) {
            throw new BizException("签名不正确");
        }
    }

    /**
     * 验证签名
     *
     * @param message      原始数据
     * @param signedData   签名值（Base64 编码）
     * @param publicKeyStr 公钥字符串（Base64 编码）
     * @return 验证结果，true 表示通过验证，false 表示失败
     */
    public static boolean verifySignature(String message, String signedData, String publicKeyStr) {
        try {
            // 解码公钥字符串
            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyStr);

            // 生成公钥对象
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            // 创建签名对象并初始化
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initVerify(publicKey);

            // 加载原始数据
            signature.update(message.getBytes(StandardCharsets.UTF_8));

            // 验证签名
            byte[] signedBytes = Base64.getDecoder().decode(signedData);
            return signature.verify(signedBytes);

        } catch (Exception e) {
            System.err.println("验证签名失败: " + e.getMessage());
            return false;
        }
    }

    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes());

            // 转成16进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found!", e);
        }
    }


    public static void main(String[] args) throws Exception {

//         authCode=test&count=10&id=1891417919443832832&timestamp=*************
//         authCode=test&count=10&id=1891417919443832832&timestamp=*************

        Map<String, Object> map = new HashMap<>();
        map.put("accountId", 1891417919443832832L);
        map.put("authCode", "test");
        map.put("timestamp", *************L);
        String sign = generateSignature(map, privateKeyStrAll);

        String message = convertMessage(map, "");
        System.out.println(message);

        map.put("sign", sign);

        System.out.println(JSONUtil.toJsonStr(map));

        verifySignature(map, sign, publicKeyAll);

        //---------------------md5------------

        System.out.println(generateMd5Signature(map, "testkey"));


        System.out.println(generateSecretKey());
    }

    public static String generateSecretKey() throws Exception {

        String secretKey = UUID.randomUUID().toString().replace("-", "");
        return secretKey;
//        KeyGenerator keyGen = KeyGenerator.getInstance("HmacSHA256");
//        keyGen.init(256); // 可以是 128 / 256
//        SecretKey secretKey = keyGen.generateKey();
//        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    public static String generateMd5Signature(Object obj, String secretKey) {
        String str = convertMessage(obj, "sign") + secretKey;
        return md5(str);
    }
}


