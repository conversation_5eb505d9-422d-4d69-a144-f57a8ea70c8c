package com.aiyoooo.service.common.bean;

import lombok.Data;
import java.util.Map;

/**
 * HTTP请求对象
 * <AUTHOR>
 * @since   2024/12/07
 */
@Data
public class HttpQueryBean {
    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求头信息
     */
    private Map<String, String> headers;

    /**
     * 请求URL后跟的参数
     */
    private Map<String, Object> queryParams;

    /**
     * 请求参数
     */
    private Map<String, Object> bodyParams;

    /**
     * 请求信息体（字符串）
     */
    private String body;

    /**
     * 请求超时时间：单位(毫秒)
     */
    private Integer timeout;

    /**
     * HTTP代理信息
     */
    private HttpProxy httpProxy;
}