package com.aiyoooo.service.common.util;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * <AUTHOR> zhoudong
 * @since : 2024/3/16 下午5:32
 */
@Slf4j
public class CustomerStrToJsonObjectSerialize extends JsonSerializer<String> {

    @Override
    public void serialize(String arg0, JsonGenerator arg1, SerializerProvider serializers) throws IOException {
        JSONObject result = null;
        try {
            if (StringUtils.isNotBlank(arg0)){
                result = JSONUtil.parseObj(arg0);
            }
        } catch (Exception e) {
            log.error("CustomerStrToJsonObjectSerialize",e);
        }
        if (arg0 != null) {
            arg1.writeObject(result);
        }
    }
}
