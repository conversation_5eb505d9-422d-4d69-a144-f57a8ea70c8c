package com.aiyoooo.service.common.vo;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2025/2/24 14:07
 */
@Data
public class SteamDataRes {


    private Session Session; // 外层 Session 字段

    public static class Session {

        @SerializedName("SteamID") // 映射 JSON 中的大写字段
        private String steamID;

        // Getter
        public String getSteamID() {
            return steamID;
        }
    }

    // Getter
    public Session getSession() {
        return Session;
    }

    @SerializedName("revocation_code")
    private String revocationCode;

    @SerializedName("account_name")
    private String accountName;

    @SerializedName("shared_secret")
    private String sharedSecret;

    @SerializedName("identity_secret")
    private String identitySecret;


}

    
    