package com.aiyoooo.service.common.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/25 14:35
 */
public class HttpRequestUtil {

    public static String sendHttpRequest(String requestStr, String cookie) {
        HttpURLConnection connection = null;
        BufferedReader reader = null;

        try {
            // 创建 URL 对象
            URL url = new URL(requestStr);
            connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法为 GET
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 设置连接超时时间
            connection.setReadTimeout(10000);    // 设置读取超时时间

            // 添加请求头
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("Language", "zh-cn");
            connection.setRequestProperty("Cookie", cookie);

            // 发起请求并获取响应码
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 读取响应
                InputStreamReader inputStreamReader = new InputStreamReader(connection.getInputStream(), "UTF-8");
                reader = new BufferedReader(inputStreamReader);
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            } else {
                throw new RuntimeException("HTTP 请求失败，响应码: " + responseCode);
            }
        } catch (Exception e) {
            throw new RuntimeException("网络请求异常，原因: " + e.getMessage(), e);
        } finally {
            // 关闭资源
            try {
                if (reader != null) {
                    reader.close();
                }
                if (connection != null) {
                    connection.disconnect();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {
//        String cookie = "browserid=363197193274520856; timezoneOffset=28800,0; steamMachineAuth76561199802956476=; steamMachineAuth*****************=; recentapps=%7B%222567870%22%3A1733129569%7D; steamLoginSecure=*****************%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.g8_y0YqtVu1Ad5HBuWKsXD-3CjNIneXVIptNZoFfROC52oaYl0gzcSL0nOtwQoO0LjyWEyBLurf885veVYw6Bg; sessionid=266ce7923393bdae221a80da; steamCountry=US%7C0ed05f2a6402e1dc5d4207fd782e15a6; app_impressions=1363080@1_4_661__651|990080@1_4_661__651|2688950@1_4_661__651|1363080@1_4_661__651|2688950@1_4_661__651|990080@1_4_661__651|1363080@1_4_661__651|990080@1_4_661__651|2688950@1_4_661__651|2933620@1_4_661__651|1845910@1_4_661__651|1144200@1_4_661__651|2933620@1_4_661__651|1845910@1_4_661__651|1144200@1_4_661__651|2933620@1_4_661__651|1845910@1_4_661__651|1144200@1_4_661__651";
//        SteamBalanceVo data = getAccountBalance(cookie);
//        System.out.println(JSONUtil.toJsonStr(data));

        System.out.println(
                sendHttpRequest("https://steamcommunity.com/inventory/*****************/730/2?l=schinese&count=20&start_assetid=null",
                        "timezoneOffset=28800,0; browserid=226963306218210814; steamMachineAuth*****************=; strInventoryLastContext=730_2; recentlyVisitedAppHubs=730; steamLoginSecure=*****************%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nJwbdYN-0xTxjNjcHkkG7bFWJRgyuidPVOqPhE2stdXQxv7UwbmDhZGNhkNr7ULvNULsMHS0Mch7GVCz55GzDQ; sessionid=3e7be6a51ca607238c8310ba; steamCountry=US%7Caaf49abd04134503c73c30b026bdf266"));
    }
}

    
    