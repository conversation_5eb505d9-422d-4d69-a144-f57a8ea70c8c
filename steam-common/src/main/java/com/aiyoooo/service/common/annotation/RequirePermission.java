package com.aiyoooo.service.common.annotation;

import java.lang.annotation.*;

/**
 * 权限控制注解
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {

    /**
     * 权限编码/接口路径
     * 如果不指定，则使用请求的URI作为权限编码
     */
    String value() default "";

    /**
     * 是否必须有权限
     * true: 没有权限时抛出异常
     * false: 没有权限时记录日志但允许继续访问
     */
    boolean required() default true;

    /**
     * 权限描述
     */
    String description() default "";
} 