package com.aiyoooo.service.common.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 17:25
 */
public class SnowflakeIdGenerator {

    // 创建雪花算法实例
    private static final Snowflake snowflake = IdUtil.createSnowflake(1, 1);

    public static Long getNextId() {
        return snowflake.nextId();
    }

    public static void main(String[] args) {
        System.out.println(getNextId());
    }
}

    
    