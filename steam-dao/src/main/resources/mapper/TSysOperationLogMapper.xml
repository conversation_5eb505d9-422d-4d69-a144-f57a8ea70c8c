<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyoooo.service.dao.mapper.tsys.TSysOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.aiyoooo.service.dao.entity.t.TSysOperationLog">
        <id column="id" property="id" />
        <result column="module" property="module" />
        <result column="operation_type" property="operationType" />
        <result column="description" property="description" />
        <result column="method" property="method" />
        <result column="request_method" property="requestMethod" />
        <result column="request_url" property="requestUrl" />
        <result column="request_ip" property="requestIp" />
        <result column="request_param" property="requestParam" />
        <result column="response_result" property="responseResult" />
        <result column="auth_code" property="authCode" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="cost_time" property="costTime" />
        <result column="operation_time" property="operationTime" />
    </resultMap>

</mapper>
