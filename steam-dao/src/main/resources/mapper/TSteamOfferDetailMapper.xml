<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyoooo.service.dao.mapper.TSteamOfferDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.aiyoooo.service.dao.entity.t.TSteamOfferDetail">
        <id column="id" property="id" />
        <result column="offer_id" property="offerId" />
        <result column="assetid" property="assetid" />
        <result column="amount" property="amount" />
        <result column="new_assetid" property="newAssetid" />
    </resultMap>

    <!-- 批量插入或更新 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO t_steam_offer_detail
        (
            id,
            offer_id,
            assetid,
            amount,
            new_assetid
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.offerId},
                #{item.assetid},
                #{item.amount},
                #{item.newAssetid}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            offer_id = VALUES(offer_id),
            assetid = VALUES(assetid),
            amount = VALUES(amount),
            new_assetid = VALUES(new_assetid)
    </insert>
    
    <!-- 根据交易报价ID删除明细 -->
    <delete id="deleteByOfferId">
        DELETE FROM t_steam_offer_detail
        WHERE offer_id = #{offerId}
    </delete>

</mapper> 