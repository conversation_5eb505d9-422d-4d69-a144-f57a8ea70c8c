<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyoooo.service.dao.mapper.TSysPermissionMapper">

    <!-- 根据授权码查询权限列表 -->
    <select id="selectPermissionsByAuthCode" resultType="com.aiyoooo.service.dao.entity.tsys.TSysPermission">
        SELECT id, auth_code, interface as interfacePath
        FROM tsys_permission
        WHERE auth_code = #{authCode}
    </select>

    <!-- 检查授权码是否有指定接口的权限 -->
    <select id="checkPermission" resultType="int">
        SELECT COUNT(1)
        FROM tsys_permission
        WHERE auth_code = #{authCode}
        AND (
            interface = #{interfacePath}
            OR interface LIKE CONCAT(#{interfacePath}, '/%')
            OR #{interfacePath} LIKE CONCAT(interface, '/%')
            OR interface = '*'
        )
    </select>

    <!-- 批量插入权限 -->
    <insert id="insertBatch">
        INSERT INTO tsys_permission (auth_code, interface)
        VALUES
        <foreach collection="permissions" item="item" separator=",">
            (#{item.authCode}, #{item.interfacePath})
        </foreach>
    </insert>

</mapper> 