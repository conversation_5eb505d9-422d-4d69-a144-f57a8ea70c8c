<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyoooo.service.dao.mapper.TSteamOfferMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.aiyoooo.service.dao.entity.t.TSteamOffer">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="account_id" property="accountId" />
        <result column="trade_url" property="tradeUrl" />
        <result column="quantity" property="quantity" />
        <result column="status" property="status" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="create_datetime" property="createDatetime" />
        <result column="finish_datetime" property="finishDatetime" />
        <result column="remark" property="remark" />
        <result column="trade_offer_id" property="tradeOfferId" />
        <result column="trade_offer_state" property="tradeOfferState" />
        <result column="callback_url" property="callbackUrl" />
        <result column="trade_offer_json" property="tradeOfferJson" />
    </resultMap>

    <!-- 批量插入或更新 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO t_steam_offer
        (
            id,
            type,
            account_id,
            trade_url,
            quantity,
            status,
            cancel_reason,
            create_datetime,
            finish_datetime,
            remark,
            trade_offer_id,
            trade_offer_state,
            callback_url,
            trade_offer_json
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.type},
                #{item.accountId},
                #{item.tradeUrl},
                #{item.quantity},
                #{item.status},
                #{item.cancelReason},
                #{item.createDatetime},
                #{item.finishDatetime},
                #{item.remark},
                #{item.tradeOfferId},
                #{item.tradeOfferState},
                #{item.callbackUrl},
                #{item.tradeOfferJson}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            type = VALUES(type),
            account_id = VALUES(account_id),
            trade_url = VALUES(trade_url),
            quantity = VALUES(quantity),
            status = VALUES(status),
            cancel_reason = VALUES(cancel_reason),
            finish_datetime = VALUES(finish_datetime),
            remark = VALUES(remark),
            trade_offer_id = VALUES(trade_offer_id),
            trade_offer_state = VALUES(trade_offer_state),
            callback_url = VALUES(callback_url),
            trade_offer_json = VALUES(trade_offer_json)
    </insert>

</mapper> 