<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyoooo.service.dao.mapper.TSteamAccountAssetHistoryMapper">

    <insert id="upsertBatch">
        insert into t_steam_account_asset_history
        ( assetid,account_id, history_item_id, trade_date, trade_type, description, name, classid, instanceid, steam_id, flow_direction, create_datetime)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.assetid},#{item.accountId}, #{item.historyItemId}, #{item.tradeDate}, #{item.tradeType}, #{item.description}, #{item.name}, #{item.classid}, #{item.instanceid}, #{item.steamId}, #{item.flowDirection}, #{item.createDatetime})
        </foreach>
        on duplicate key update
        trade_date = IFNULL(values(trade_date), trade_date),
        trade_type = IFNULL(values(trade_type), trade_type),
        description = IFNULL(values(description), description),
        name = IFNULL(values(name), name),
        classid = IFNULL(values(classid), classid),
        instanceid = IFNULL(values(instanceid), instanceid),
        steam_id = IFNULL(values(steam_id), steam_id),
        flow_direction = IFNULL(values(flow_direction), flow_direction),
        create_datetime = IFNULL(values(create_datetime), create_datetime)
    </insert>

    <!-- 迁移资产历史数据到归档表 -->
    <insert id="migrateToArchive" parameterType="int">
        INSERT INTO t_steam_account_asset_history_archive 
        (history_item_id, trade_date, trade_type, description, name, 
        assetid, classid, instanceid, steam_id, flow_direction, 
        create_datetime, account_id, archive_time)
        SELECT 
            history_item_id, trade_date, trade_type, description, name, 
            assetid, classid, instanceid, steam_id, flow_direction, 
            create_datetime, account_id, NOW()
        FROM t_steam_account_asset_history
        WHERE t_steam_account_asset_history.create_datetime &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </insert>

</mapper>