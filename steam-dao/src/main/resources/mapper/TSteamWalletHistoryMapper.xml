<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyoooo.service.dao.mapper.TSteamWalletHistoryMapper">

    <insert id="insertOrUpdateBatch">
        INSERT INTO t_steam_wallet_history
        (
        transaction_date,
        account_id,
        item_description,
        transaction_type,
        payment_method,
        total_amount,
        wallet_change,
        wallet_balance,
        transaction_link,
        transaction_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.transactionDate},
            #{item.accountId},
            #{item.itemDescription},
            #{item.transactionType},
            #{item.paymentMethod},
            #{item.totalAmount},
            #{item.walletChange},
            #{item.walletBalance},
            #{item.transactionLink},
            #{item.transactionId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        transaction_date = IFNULL(VALUES(transaction_date), transaction_date),
        item_description = IFNULL(VALUES(item_description), item_description),
        transaction_type = IFNULL(VALUES(transaction_type), transaction_type),
        payment_method = IFNULL(VALUES(payment_method), payment_method),
        total_amount = IFNULL(VALUES(total_amount), total_amount),
        wallet_change = IFNULL(VALUES(wallet_change), wallet_change),
        wallet_balance = IFNULL(VALUES(wallet_balance), wallet_balance)
    </insert>

    <!-- 迁移钱包历史数据到归档表 -->
    <insert id="migrateToArchive" parameterType="int">
        INSERT INTO t_steam_wallet_history_archive 
        (id, account_id, transaction_date, item_description, transaction_type, 
        payment_method, total_amount, wallet_change, wallet_balance, 
        transaction_link, transaction_id, create_datetime, archive_time)
        SELECT 
            id, account_id, transaction_date, item_description, transaction_type, 
            payment_method, total_amount, wallet_change, wallet_balance, 
            transaction_link, transaction_id, create_datetime, NOW()
        FROM t_steam_wallet_history
        WHERE t_steam_wallet_history.create_datetime &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </insert>
</mapper>