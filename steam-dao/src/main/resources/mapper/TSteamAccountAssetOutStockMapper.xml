<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyoooo.service.dao.mapper.t.TSteamAccountAssetOutStockMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.aiyoooo.service.dao.entity.t.TSteamAccountAssetOutStock">
        <id column="assetid" property="assetid" />
        <result column="account_id" property="accountId" />
        <result column="classid" property="classid" />
        <result column="instanceid" property="instanceid" />
        <result column="market_name" property="marketName" />
        <result column="trade_start_datetime" property="tradeStartDatetime" />
        <result column="stock_in_datetime" property="stockInDatetime" />
        <result column="stock_out_datetime" property="stockOutDatetime" />
        <result column="source_steam_id" property="sourceSteamId" />
        <result column="target_steam_id" property="targetSteamId" />
        <result column="exterior_value" property="exteriorValue" />
        <result column="market_hash_name" property="marketHashName" />
        <result column="inventory_status" property="inventoryStatus" />
        <result column="description" property="description" />
        <result column="check_exist" property="checkExist" />
    </resultMap>

    <!-- 批量插入出库资产数据 -->
    <insert id="batchInsert">
        INSERT INTO t_steam_account_asset_out_stock
        (
            assetid,
            account_id,
            classid,
            instanceid,
            market_name,
            trade_start_datetime,
            stock_in_datetime,
            stock_out_datetime,
            source_steam_id,
            target_steam_id,
            exterior_value,
            market_hash_name,
            inventory_status,
            description,
            check_exist
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.assetid},
                #{item.accountId},
                #{item.classid},
                #{item.instanceid},
                #{item.marketName},
                #{item.tradeStartDatetime},
                #{item.stockInDatetime},
                #{item.stockOutDatetime},
                #{item.sourceSteamId},
                #{item.targetSteamId},
                #{item.exteriorValue},
                #{item.marketHashName},
                #{item.inventoryStatus},
                #{item.description},
                #{item.checkExist}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            account_id = VALUES(account_id),
            classid = VALUES(classid),
            instanceid = VALUES(instanceid),
            market_name = VALUES(market_name),
            trade_start_datetime = VALUES(trade_start_datetime),
            stock_in_datetime = VALUES(stock_in_datetime),
            stock_out_datetime = VALUES(stock_out_datetime),
            source_steam_id = VALUES(source_steam_id),
            target_steam_id = VALUES(target_steam_id),
            exterior_value = VALUES(exterior_value),
            market_hash_name = VALUES(market_hash_name),
            inventory_status = VALUES(inventory_status),
            description = VALUES(description),
            check_exist = VALUES(check_exist)
    </insert>
    
    <!-- 直接从资产表迁移数据到出库表 -->
    <insert id="migrateFromAsset">
        INSERT INTO t_steam_account_asset_out_stock
        (
            assetid,
            account_id,
            classid,
            instanceid,
            market_name,
            trade_start_datetime,
            stock_in_datetime,
            stock_out_datetime,
            source_steam_id,
            target_steam_id,
            exterior_value,
            market_hash_name,
            inventory_status,
            description,
            check_exist
        )
        SELECT
            assetid,
            account_id,
            classid,
            instanceid,
            market_name,
            trade_start_datetime,
            stock_in_datetime,
            stock_out_datetime,
            source_steam_id,
            target_steam_id,
            exterior_value,
            market_hash_name,
            inventory_status,
            description,
            check_exist
        FROM
            t_steam_account_asset
        WHERE
            t_steam_account_asset.inventory_status = '2'
       on duplicate key update
        inventory_status = IFNULL(values(inventory_status), inventory_status),
        trade_start_datetime = IFNULL(values(trade_start_datetime), trade_start_datetime),
        check_exist = IFNULL(values(check_exist), check_exist),
        stock_in_datetime = IFNULL(values(stock_in_datetime), stock_in_datetime),
        source_steam_id = IFNULL(values(source_steam_id), source_steam_id),
        stock_out_datetime = IFNULL(values(stock_out_datetime), stock_out_datetime),
        target_steam_id = IFNULL(values(target_steam_id), target_steam_id)
    </insert>
</mapper>
