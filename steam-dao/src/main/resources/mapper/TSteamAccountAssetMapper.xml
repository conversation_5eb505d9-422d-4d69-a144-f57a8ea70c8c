<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyoooo.service.dao.mapper.TSteamAccountAssetMapper">

    <insert id="upsertBatch">
        insert into t_steam_account_asset
        (assetid, account_id, classid, instanceid, market_name, market_hash_name, inventory_status,
        trade_start_datetime, description, exterior_value, check_exist, stock_in_datetime, source_steam_id,
        stock_out_datetime, target_steam_id,last_refresh_datetime)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.assetid}, #{item.accountId}, #{item.classid}, #{item.instanceid}, #{item.marketName},
            #{item.marketHashName}, #{item.inventoryStatus}, #{item.tradeStartDatetime}, #{item.description},
            #{item.exteriorValue}, #{item.checkExist}, #{item.stockInDatetime}, #{item.sourceSteamId},
            #{item.stockOutDatetime}, #{item.targetSteamId}, #{item.lastRefreshDatetime})
        </foreach>
        on duplicate key update
        classid = IFNULL(values(classid),classid),
        instanceid = IFNULL(values(instanceid), instanceid),
        market_name = IFNULL(values(market_name), market_name),
        market_hash_name = IFNULL(values(market_hash_name), market_hash_name),
        inventory_status = IFNULL(values(inventory_status), inventory_status),
        trade_start_datetime = IFNULL(values(trade_start_datetime), trade_start_datetime),
        description = IFNULL(values(description), description),
        exterior_value = IFNULL(values(exterior_value), exterior_value),
        check_exist = IFNULL(values(check_exist), check_exist),
        stock_in_datetime = IFNULL(values(stock_in_datetime), stock_in_datetime),
        source_steam_id = IFNULL(values(source_steam_id), source_steam_id),
        stock_out_datetime = IFNULL(values(stock_out_datetime), stock_out_datetime),
        target_steam_id = IFNULL(values(target_steam_id), target_steam_id),
        last_refresh_datetime = IFNULL(values(last_refresh_datetime), last_refresh_datetime)
    </insert>

    <update id="updateStockInDatetimeFromHistory">
        UPDATE t_steam_account_asset a
            JOIN (
            SELECT h.assetid, h.trade_date, h.steam_id
            FROM t_steam_account_asset_history h
            INNER JOIN (
            SELECT assetid, MAX(trade_date) AS max_trade_date
            FROM t_steam_account_asset_history
            WHERE flow_direction = 'IN' AND account_id = #{accountId}
            GROUP BY assetid
            ) t ON h.assetid = t.assetid AND h.trade_date = t.max_trade_date
            WHERE h.flow_direction = 'IN'  AND h.account_id = #{accountId}
            ) latest_in
        ON a.assetid = latest_in.assetid
            SET a.stock_in_datetime = latest_in.trade_date, a.source_steam_id = latest_in.steam_id
        WHERE a.stock_in_datetime IS NULL  AND a.account_id = #{accountId}
    </update>

    <update id="batchUpdateByAssetId">
        <foreach collection="list" item="item" separator=";">
            update t_steam_account_asset
            <set>
                <if test="item.classid != null">classid = #{item.classid},</if>
                <if test="item.instanceid != null">instanceid = #{item.instanceid},</if>
                <if test="item.marketName != null">market_name = #{item.marketName},</if>
                <if test="item.marketHashName != null">market_hash_name = #{item.marketHashName},</if>
                <if test="item.inventoryStatus != null">inventory_status = #{item.inventoryStatus},</if>
                <if test="item.tradeStartDatetime != null">trade_start_datetime = #{item.tradeStartDatetime},</if>
                <if test="item.description != null">description = #{item.description},</if>
                <if test="item.exteriorValue != null">exterior_value = #{item.exteriorValue},</if>
                <if test="item.checkExist != null">check_exist = #{item.checkExist},</if>
                <if test="item.stockInDatetime != null">stock_in_datetime = #{item.stockInDatetime},</if>
                <if test="item.sourceSteamId != null">source_steam_id = #{item.sourceSteamId},</if>
                <if test="item.stockOutDatetime != null">stock_out_datetime = #{item.stockOutDatetime},</if>
                <if test="item.targetSteamId != null">target_steam_id = #{item.targetSteamId},</if>
                <if test="item.lastRefreshDatetime != null">last_refresh_datetime = #{item.lastRefreshDatetime}</if>
            </set>
            where assetid = #{item.assetid}
        </foreach>
    </update>
</mapper>