package com.aiyoooo.service.dao.enums;

import lombok.Getter;

@Getter
public enum QueryLevelEnum {
    LOW("1", "0 0/5 * * * ?", "低频"),    // 每5分钟
    MEDIUM("2", "0 0/2 * * * ?", "中频"), // 每2分钟
    HIGH("3", "0/30 * * * * ?", "高频");  // 每30秒

    private final String code;
    private final String cron;
    private final String desc;

    QueryLevelEnum(String code, String cron, String desc) {
        this.code = code;
        this.cron = cron;
        this.desc = desc;
    }

    public static QueryLevelEnum getByCode(String code) {
        for (QueryLevelEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return LOW; // 默认返回低频
    }
} 