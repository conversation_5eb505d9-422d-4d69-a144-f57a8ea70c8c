package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/17 20:32
 */
@Data
public class OpenBaseReq {

    @ApiModelProperty(name = "authCode", value = "授权编号", required = true, position = 1)
    @NotBlank(message = "授权编号不能为空")
    private String authCode;

    @ApiModelProperty(name = "timestamp", value = "时间戳", required = true, position = 2)
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @ApiModelProperty(name = "sign", value = "签名", required = true, position = 1000)
    @NotBlank(message = "签名不能为空")
    private String sign;


    @ApiModelProperty(hidden = true)
    private Long id;

    @ApiModelProperty(hidden = true)
    private Long accountId;
}

    
    