package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "查询Steam玩家信息入参", description = "查询Steam玩家信息入参")
public class SteamPlayerDto extends OpenBaseReq{

    @ApiModelProperty(name = "steamId", value = "steamId", required = true, position = 20)
    @NotBlank(message = "steamId不能为空")
    private String steamId;
}

    
    