package com.aiyoooo.service.dao.enums;

/**
 * steam账号分类
 */
public enum ESteamAccountUseClass {

    /**
     * 空号
     */
    USE_CLASS_0("0", "空号"),

    /**
     * 充值号
     */
    USE_CLASS_1("1", "充值号"),

    /**
     * 库存号-需同步库存
     */
    USE_CLASS_3("3", "库存号"),

    /**
     * CS初始账号
     */
    USE_CLASS_4("4", "CS初始账号"),

    /**
     * CS挂箱号-需同步库存
     */
    USE_CLASS_5("5", "CS挂箱号"),

    /**
     * CS归箱号
     */
    USE_CLASS_6("6", "CS归箱号"),

    /**
     * 行动号-需同步库存
     */
    USE_CLASS_7("7", "行动号"),

    /**
     * 行动号归箱号
     */
    USE_CLASS_8("8", "行动归箱号"),

    /**
     * 行动完结号
     */
    USE_CLASS_9("9", "行动完结号"),

    /**
     * steam销售号
     */
    USE_CLASS_10("10", "steam销售号"),

    /**
     * steam销售号
     */
    USE_CLASS_11("11", "外部销售"),


    ;

    ESteamAccountUseClass(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
