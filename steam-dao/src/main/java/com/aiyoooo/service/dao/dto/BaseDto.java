package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "基础dto", description = "基础dto")
public class BaseDto {

    @ApiModelProperty(name = "id", value = "id", required = true, position = 10)
    @NotNull(message = "id不能为空")
    private Long id;
}

    
    