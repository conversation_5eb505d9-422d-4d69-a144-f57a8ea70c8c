package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "steam实时库存查询入参", description = "steam实时库存查询入参")
public class SteamAccountAssetInventoryDto  extends OpenBaseReq{

    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 20)
    @NotNull(message = "accountId不能为空")
    private Long accountId;

    @ApiModelProperty(name = "appid", value = "appid", required = true, position = 20)
    @NotBlank(message = "appid不能为空")
    private String appid;

    @ApiModelProperty(name = "count", value = "数量", required = true, position = 20)
    @NotBlank(message = "数量不能为空")
    private String count;

    @ApiModelProperty(name = "startAssetid", value = "开始的资产id", required = true, position = 20)
    private String startAssetid;

    @ApiModelProperty(name = "language", value = "语言", required = true, position = 20)
    private String language;
}

    
    