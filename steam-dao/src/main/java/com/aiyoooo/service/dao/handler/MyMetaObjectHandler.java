package com.aiyoooo.service.dao.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;

/**
 * MybatisPlus的TableField自动填充配置
 * <AUTHOR>
 * @since   2024/12/10
 */
@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        Object createdAt = metaObject.getValue("createdAt");
        if (ObjectUtils.isNull(createdAt)) {
            this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());
        }
        Object updatedAt = metaObject.getValue("updatedAt");
        if (ObjectUtils.isNull(updatedAt)) {
            this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Object updatedAt = metaObject.getValue("updatedAt");
        if (ObjectUtils.isNull(updatedAt)) {
            this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
        }
    }
}
