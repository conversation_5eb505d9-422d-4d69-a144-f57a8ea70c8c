package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * steam开箱任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Getter
@Setter
@TableName("t_steam_open_box_task")
@ApiModel(value = "TSteamOpenBoxTask对象", description = "steam开箱任务表")
public class TSteamOpenBoxTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("账号ID")
    private Long accountId;

    @ApiModelProperty("箱子名称")
    private String boxName;

    @ApiModelProperty("钥匙名称")
    private String keyName;

    @ApiModelProperty("总数量")
    private Integer totalCount;

    @ApiModelProperty("成功数量")
    private Integer successCount;

    @ApiModelProperty("失败数量")
    private Integer failedCount;

    @ApiModelProperty("任务状态 0-待处理 1-处理中 2-已完成 3-已失败")
    private String status;

    @ApiModelProperty("回调URL")
    private String callbackUrl;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("完成时间")
    private LocalDateTime finishTime;

    @ApiModelProperty("错误信息")
    private String errorMessage;

    @ApiModelProperty("备注")
    private String remark;
}
