package com.aiyoooo.service.dao.mapper;

import com.aiyoooo.service.dao.entity.tsys.TSysPermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 系统权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper
public interface TSysPermissionMapper extends BaseMapper<TSysPermission> {

    /**
     * 根据授权码查询权限列表
     * @param authCode 授权码
     * @return 权限列表
     */
    List<TSysPermission> selectPermissionsByAuthCode(@Param("authCode") String authCode);

    /**
     * 检查授权码是否有指定接口的权限
     * @param authCode 授权码
     * @param interfacePath 接口路径
     * @return 权限数量
     */
    int checkPermission(@Param("authCode") String authCode, 
                       @Param("interfacePath") String interfacePath);

    /**
     * 批量插入权限
     * @param permissions 权限列表
     * @return 插入数量
     */
    int insertBatch(@Param("permissions") List<TSysPermission> permissions);
} 