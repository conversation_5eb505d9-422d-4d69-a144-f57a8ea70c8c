package com.aiyoooo.service.dao.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * steam交易报价类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public enum ESteamOfferTypeEnum {

    /**
     * 平台出售
     */
    PLATFORM_SELL("0", "平台出售"),

    /**
     * 平台购买
     */
    PLATFORM_BUY("1", "平台购买"),
    ;

    private String code;
    private String value;

    ESteamOfferTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ESteamOfferTypeEnum> getTypeMap() {
        Map<String, ESteamOfferTypeEnum> map = new HashMap<>();
        for (ESteamOfferTypeEnum type : ESteamOfferTypeEnum.values()) {
            map.put(type.getCode(), type);
        }
        return map;
    }

    public static ESteamOfferTypeEnum getByCode(String code) {
        return getTypeMap().get(code);
    }

    public static String getValueByCode(String code) {
        ESteamOfferTypeEnum typeEnum = getByCode(code);
        return typeEnum == null ? "" : typeEnum.getValue();
    }
} 