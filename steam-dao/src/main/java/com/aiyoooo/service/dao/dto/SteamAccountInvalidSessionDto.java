package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "steam账号会话失效", description = "steam账号会话失效")
public class SteamAccountInvalidSessionDto extends OpenBaseReq{

    /**
     * ID
     */
    @ApiModelProperty(name = "id", value = "ID", required = true, position = 10)
    @NotNull(message = "id不能為空")
    private Long id;
}

    
    