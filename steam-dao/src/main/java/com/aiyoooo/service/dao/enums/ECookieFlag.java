package com.aiyoooo.service.dao.enums;

import java.util.HashMap;
import java.util.Map;

public enum ECookieFlag {

    /**
     * 待生效
     */
    TODO("0", "待生效"),

    /**
     * 已生效
     */
    VALID("1", "已生效"),

    /**
     * 请求失效
     */
    INVALID("2", "请求失效"),

    ;

    private String code;
    private String value;

    ECookieFlag(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECookieFlag> getResultMap() {
        Map<String, ECookieFlag> map = new HashMap<String, ECookieFlag>();
        for (ECookieFlag type : ECookieFlag.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECookieFlag getResult(String code) {
        Map<String, ECookieFlag> map = getResultMap();
        ECookieFlag result = map.get(code);

        return result;
    }

}
