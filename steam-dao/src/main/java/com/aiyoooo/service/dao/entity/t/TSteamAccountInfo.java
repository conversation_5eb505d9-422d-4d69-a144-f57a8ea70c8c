package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * steam账号
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@Setter
@TableName("t_steam_account_info")
@ApiModel(value = "TSteamAccountInfo对象", description = "steam账号")
public class TSteamAccountInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("steam账号")
    @TableId(value = "account_id", type = IdType.INPUT)
    private Long accountId;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("头像")
    private String avatarUrl;

    @ApiModelProperty("steam邮箱")
    private String steamEmail;

    @ApiModelProperty("apikey")
    private String apikey;

    @ApiModelProperty("steamId")
    private String steamId;

    @ApiModelProperty("交易url")
    private String tradeUrl;

    @ApiModelProperty("地区")
    private String region;

    @ApiModelProperty("steam注册时间")
    private Long timecreated;

    @ApiModelProperty("账户等级")
    private String accountLevel;

    @ApiModelProperty("是否红信")
    private Boolean hasRedFlag;

    @ApiModelProperty("红信日期")
    private LocalDateTime redFlagDate;

    @ApiModelProperty("在线状态：0 离线，1 在线，2 忙碌，3 离开，4 请勿打扰，5 打牌，6 看电影等。")
    private String personastate;

    @ApiModelProperty("余额")
    private BigDecimal balance;

    @ApiModelProperty("余额币种")
    private String currency;

    @ApiModelProperty("0.未启用，1.令牌电子邮件保护，2.令牌手机验证器保护，3.令牌手机验证器和电子邮件保护")
    private Integer protectionType;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updatedAt;

    @ApiModelProperty("备注")
    private String remark;
}
