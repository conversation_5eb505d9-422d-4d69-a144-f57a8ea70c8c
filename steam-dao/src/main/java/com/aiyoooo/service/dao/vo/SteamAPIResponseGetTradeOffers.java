package com.aiyoooo.service.dao.vo;

import cn.hutool.core.annotation.Alias;
import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/13 13:54
 */
@Data
public class SteamAPIResponseGetTradeOffers {

    public OffersResponse response;

    @Data
    public static class OffersResponse {
        @Alias("trade_offers_sent")
        private List<SentOffer> tradeOffersSent;

        @Alias("trade_offers_received")
        private List<ReceivedOffer> tradeOffersReceived;

        private List<SteamAPIResponseInventoryDescription> descriptions;

        @Data
        public static class SentOffer {
            @Alias("tradeofferid")
            public String tradeOfferId;

            @Alias("accountid_other")
            public long accountIdOther;

            public String message;

            @Alias("expiration_time")
            public long expirationTime;

            @Alias("trade_offer_state")
            public int tradeOfferState;

            @Alias("items_to_give")
            public List<Item> itemsToGive;

            @Alias("is_our_offer")
            public boolean isOurOffer;

            @Alias("time_created")
            public long timeCreated;

            @Alias("time_updated")
            public long timeUpdated;

            @Alias("from_real_time_trade")
            public boolean fromRealTimeTrade;

            @Alias("escrow_end_date")
            public int escrowEndDate;

            @Alias("confirmation_method")
            public int confirmationMethod;

            @Alias("eresult")
            public int eResult;
        }

        @Data
        public static class ReceivedOffer {
            @Alias("tradeofferid")
            public String tradeOfferId;

            @Alias("accountid_other")
            public long accountIdOther;

            public String message;

            @Alias("expiration_time")
            public long expirationTime;

            @Alias("trade_offer_state")
            public int tradeOfferState;

            @Alias("items_to_receive")
            public List<Item> itemsToReceive;

            @Alias("is_our_offer")
            public boolean isOurOffer;

            @Alias("time_created")
            public long timeCreated;

            @Alias("time_updated")
            public long timeUpdated;

            @Alias("from_real_time_trade")
            public boolean fromRealTimeTrade;

            @Alias("escrow_end_date")
            public int escrowEndDate;

            @Alias("confirmation_method")
            public int confirmationMethod;

            @Alias("eresult")
            public int eResult;
        }

        @Data
        public static class Item {
            @Alias("appid")
            public int appId;

            @Alias("contextid")
            public String contextId;

            @Alias("assetid")
            public String assetId;

            @Alias("classid")
            public String classId;

            @Alias("instanceid")
            public String instanceId;

            public String amount;
            public boolean missing;

            @Alias("est_usd")
            public String estUsd;
        }
    }

    public static void main(String[] args) {

        String str ="{\"response\":{\"trade_offers_sent\":[{\"tradeofferid\":\"**********\",\"accountid_other\":*********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":5,\"items_to_give\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"**********\",\"amount\":\"1\",\"missing\":false,\"est_usd\":\"1\"}],\"is_our_offer\":true,\"time_created\":**********,\"time_updated\":**********,\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":2,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":8,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":true,\"est_usd\":\"3\"}],\"is_our_offer\":true,\"time_created\":**********,\"time_updated\":**********,\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":3,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":true,\"est_usd\":\"6\"}],\"is_our_offer\":true,\"time_created\":**********,\"time_updated\":**********,\"tradeid\":\"801183764769165526\",\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":*********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":6,\"items_to_give\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"**********\",\"amount\":\"1\",\"missing\":false,\"est_usd\":\"1\"}],\"is_our_offer\":true,\"time_created\":**********,\"time_updated\":**********,\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":2,\"eresult\":1}],\"trade_offers_received\":[{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":2,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":false,\"est_usd\":\"39\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":3,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"**********\",\"amount\":\"1\",\"missing\":true,\"est_usd\":\"4\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"tradeid\":\"800058317598878773\",\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":3,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"**********\",\"amount\":\"1\",\"missing\":true,\"est_usd\":\"4\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"tradeid\":\"800058317598878754\",\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":3,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"**********\",\"amount\":\"1\",\"missing\":true,\"est_usd\":\"4\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"tradeid\":\"800058317598878728\",\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":10,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":false,\"est_usd\":\"38\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":6,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":false,\"est_usd\":\"38\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":6,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":false,\"est_usd\":\"40\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":7,\"items_to_give\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"*********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":false,\"est_usd\":\"20\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":3,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":true,\"est_usd\":\"2\"}],\"is_our_offer\":false,\"time_created\":1742969331,\"time_updated\":1742969588,\"tradeid\":\"800058317598647345\",\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"7895922858\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":1743401355,\"trade_offer_state\":3,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"**********\",\"instanceid\":\"*********\",\"amount\":\"1\",\"missing\":true,\"est_usd\":\"26\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"tradeid\":\"707733344118724735\",\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1},{\"tradeofferid\":\"**********\",\"accountid_other\":**********,\"message\":\"\",\"expiration_time\":**********,\"trade_offer_state\":3,\"items_to_receive\":[{\"appid\":730,\"contextid\":\"2\",\"assetid\":\"***********\",\"classid\":\"*********\",\"instanceid\":\"**********\",\"amount\":\"1\",\"missing\":true,\"est_usd\":\"21\"}],\"is_our_offer\":false,\"time_created\":**********,\"time_updated\":**********,\"tradeid\":\"707733344118394269\",\"from_real_time_trade\":false,\"escrow_end_date\":0,\"confirmation_method\":0,\"eresult\":1}],\"descriptions\":[{\"appid\":730,\"classid\":\"**********\",\"instanceid\":\"*********\",\"currency\":false,\"background_color\":\"\",\"icon_url\":\"-9a81dlWLwJ2UUGcVs_nsVtzdOEdtWwKGZZLQHTxDZ7I56KU0Zwwo4NUX4oFJZEHLbXH5ApeO4YmlhxYQknCRvCo04DEVlxkKgposem2LFZf0Ob3dm5R642Jk4yKk_LLPr7Vn35cppUo2rCTpo2i2VDhqUc6YWzxd4WUcgRqNV_RqAK_w-a90J_o6cuYyXF9-n517AhetYw\",\"icon_url_large\":\"\",\"descriptions\":[{\"type\":\"html\",\"value\":\"Exterior: Well-Worn\",\"name\":\"exterior_wear\"},{\"type\":\"html\",\"value\":\" \",\"name\":\"blank\"},{\"type\":\"html\",\"value\":\"The pricy G3SG1 lowers movement speed considerably but compensates with a higher rate of fire than other sniper rifles. A green forest gives way to a blue sky at the center of this custom paint job.\\n\\n\\u003Ci\\u003E\\\"There, a clearing in the forest!\\\"\\u003C/i\\u003E\",\"name\":\"description\"},{\"type\":\"html\",\"value\":\" \",\"name\":\"blank\"},{\"type\":\"html\",\"value\":\"The Dreams & Nightmares Collection\",\"color\":\"9da1a9\",\"name\":\"itemset_name\"},{\"type\":\"html\",\"value\":\" \",\"name\":\"blank\"}],\"tradable\":true,\"actions\":[{\"link\":\"steam://rungame/730/76561202255233023/+csgo_econ_action_preview%20S%owner_steamid%A%assetid%D3363140986915666621\",\"name\":\"Inspect in Game...\"}],\"name\":\"G3SG1 | Dream Glade\",\"name_color\":\"D2D2D2\",\"type\":\"Restricted Sniper Rifle\",\"market_name\":\"G3SG1 | Dream Glade (Well-Worn)\",\"market_hash_name\":\"G3SG1 | Dream Glade (Well-Worn)\",\"market_actions\":[{\"link\":\"steam://rungame/730/76561202255233023/+csgo_econ_action_preview%20M%listingid%A%assetid%D3363140986915666621\",\"name\":\"Inspect in Game...\"}],\"commodity\":false,\"market_tradable_restriction\":7,\"market_marketable_restriction\":7,\"marketable\":true,\"tags\":[{\"category\":\"Type\",\"internal_name\":\"CSGO_Type_SniperRifle\",\"localized_category_name\":\"Type\",\"localized_tag_name\":\"Sniper Rifle\"},{\"category\":\"Weapon\",\"internal_name\":\"weapon_g3sg1\",\"localized_category_name\":\"Weapon\",\"localized_tag_name\":\"G3SG1\"},{\"category\":\"ItemSet\",\"internal_name\":\"set_community_30\",\"localized_category_name\":\"Collection\",\"localized_tag_name\":\"The Dreams & Nightmares Collection\"},{\"category\":\"Quality\",\"internal_name\":\"normal\",\"localized_category_name\":\"Category\",\"localized_tag_name\":\"Normal\"},{\"category\":\"Rarity\",\"internal_name\":\"Rarity_Mythical_Weapon\",\"localized_category_name\":\"Quality\",\"localized_tag_name\":\"Restricted\",\"color\":\"8847ff\"},{\"category\":\"Exterior\",\"internal_name\":\"WearCategory3\",\"localized_category_name\":\"Exterior\",\"localized_tag_name\":\"Well-Worn\"}]}],\"next_cursor\":0}}";

        System.out.println(str);
        SteamAPIResponseGetTradeOffers steamAPIResponseGetTradeOffers = JSONUtil.toBean(str, SteamAPIResponseGetTradeOffers.class);
        System.out.println(JSONUtil.toJsonStr(steamAPIResponseGetTradeOffers));

    }

}