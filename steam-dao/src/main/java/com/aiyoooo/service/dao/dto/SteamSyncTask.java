package com.aiyoooo.service.dao.dto;

import cn.hutool.json.JSONObject;
import com.aiyoooo.service.dao.enums.TaskTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class SteamSyncTask  implements Serializable {
    private static final long serialVersionUID = 1L;

    private String taskId;
    private Long bizLockId;
    private TaskTypeEnum taskType;
    private Integer retryCount;
    private Long createTime;
    private JSONObject params;
}
