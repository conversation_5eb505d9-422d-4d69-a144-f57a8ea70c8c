package com.aiyoooo.service.dao.mapper;

import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * steam账号资产 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper
public interface TSteamAccountAssetMapper extends BaseMapper<TSteamAccountAsset> {


    void upsertBatch(List<TSteamAccountAsset> list);

    void updateStockInDatetimeFromHistory(@Param("accountId") Long accountId);

    /**
     * 根据assetId批量更新资产记录
     * 仅更新stock_in_datetime和source_steam_id字段
     *
     * @param list 要更新的资产列表
     */
    void batchUpdateByAssetId(@Param("list") List<TSteamAccountAsset> list);
}