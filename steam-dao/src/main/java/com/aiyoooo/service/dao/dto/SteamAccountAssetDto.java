package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "steam库存查询入参2", description = "steam账号库存查询入参")
public class SteamAccountAssetDto extends OpenBaseReq{



    @ApiModelProperty(name = "appid", value = "appid", position = 10)
    private String appid;


    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 20)
    @NotNull(message = "accountId不能为空")
    private Long accountId;

    @ApiModelProperty(name = "count", value = "数量", required = true, position = 20)
    @NotNull(message = "数量不能为空")
    private Integer count;

    @ApiModelProperty(name = "startAssetid", value = "开始的资产id", position = 30)
    private String startAssetid;

}

    
    