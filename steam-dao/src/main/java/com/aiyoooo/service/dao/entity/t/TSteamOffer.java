package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * steam交易报价
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@TableName("t_steam_offer")
@ApiModel(value = "TSteamOffer对象", description = "steam交易报价")
public class TSteamOffer implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键编号")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("类型，dict={\"0\":\"平台出售\",\"1\":\"平台购买\"}")
    private String type;

    @ApiModelProperty("accountId")
    private Long accountId;

    @ApiModelProperty("收货方steam交易链接")
    private String tradeUrl;

    @ApiModelProperty("数量")
    private Integer quantity;

    @ApiModelProperty("状态 dict={\"0\":\"待賣家發貨\",\"1\":\"發貨中-待賣家令牌確認\",\"2\":\"待買家確認\",\"3\":\"交易成功\",\"4\":\"訂單取消\",\"5\":\"交易暫掛\"}")
    private String status;

    @ApiModelProperty("取消理由")
    private String cancelReason;

    @ApiModelProperty("创建时间")
    private Date createDatetime;

    @ApiModelProperty("完成时间")
    private Date finishDatetime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("steam订单编号")
    private String tradeOfferId;

    @ApiModelProperty("steam订单状态")
    private String tradeOfferState;

    @ApiModelProperty("状态变更通知url")
    private String callbackUrl;

    @ApiModelProperty("orderNo")
    private String orderNo;

    @ApiModelProperty("steam订单json")
    private String tradeOfferJson;
} 