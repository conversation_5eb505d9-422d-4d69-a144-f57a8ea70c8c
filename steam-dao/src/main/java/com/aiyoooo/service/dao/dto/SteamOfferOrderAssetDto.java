package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "交易委托资产入参", description = "交易委托资产入参")
@AllArgsConstructor
@NoArgsConstructor
public class SteamOfferOrderAssetDto {

    private int appid;
    private String contextid;
    private int amount;
    private String assetid;
}

    
    