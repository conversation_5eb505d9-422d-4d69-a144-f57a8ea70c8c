package com.aiyoooo.service.dao.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class OfferOrderResultRes {

    @ApiModelProperty(name = "tradeofferid", value = "交易id", position = 1)
    private String tradeofferid;

    @ApiModelProperty(name = "errMsg", value = "错误原因", position = 2)
    private String errMsg;

}
