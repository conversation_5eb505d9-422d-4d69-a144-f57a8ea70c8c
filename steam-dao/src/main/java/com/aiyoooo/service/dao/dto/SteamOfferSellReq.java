package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 平台出售交易报价
 *
 * <AUTHOR> xieyj
 * @since : 2025-03-27 22:47
 */
@Data
public class SteamOfferSellReq extends OpenBaseReq {

    /**
     * steam账号id
     */
    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 10)
    @NotNull(message = "accountId")
    private Long accountId;

    @ApiModelProperty(name = "callbackUrl", value = "订单状态通知url", required = false, position = 20)
    private String callbackUrl;

    @ApiModelProperty(name = "orderNo", value = "业务订单号(可用于查询,不可重复)", required = false, position = 25)
    private String orderNo;

    /**
     * 收货方steam交易链接
     */
    @ApiModelProperty(name = "tradeUrl", value = "收货方steam交易链接", required = true, position = 30)
    @NotBlank(message = "收货方steam交易链接不能为空")
    private String tradeUrl;

    /**
     * 资产列表
     */
    @ApiModelProperty(name = "assetidList", value = "资产列表", required = true, position = 40)
    @NotEmpty(message = "assetidList不能为空")
    private List<String> assetidList;
}
