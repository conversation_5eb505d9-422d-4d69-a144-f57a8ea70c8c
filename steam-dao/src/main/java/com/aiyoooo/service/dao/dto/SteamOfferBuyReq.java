package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 平台购买交易报价
 *
 * <AUTHOR> xieyj
 * @since : 2025-03-27 22:47
 */
@Data
public class SteamOfferBuyReq extends OpenBaseReq{

    /**
     * steam账号id
     */
    @ApiModelProperty(name = "tradeOfferId", value = "steam交易OfferId", required = true, position = 10)
    @NotNull(message = "steam交易OfferId")
    private String tradeOfferId;

    /**
     * 收货方steam交易链接
     */
    @ApiModelProperty(name = "accountId", value = "收货方accountId", required = true, position = 20)
    @NotNull(message = "收货方accountId不能为空")
    private Long accountId;


    @ApiModelProperty(name = "orderNo", value = "业务订单号(可用于查询,不可重复)", required = false, position = 25)
    private String orderNo;

    /**
     * 资产列表
     */
    @ApiModelProperty(name = "assetidList", value = "资产列表", required = true, position = 30)
    @NotEmpty(message = "资产不能为空")
    private List<String> assetidList;


    @ApiModelProperty(name = "callbackUrl", value = "订单状态通知url", required = false, position = 40)
    private String callbackUrl;

}
