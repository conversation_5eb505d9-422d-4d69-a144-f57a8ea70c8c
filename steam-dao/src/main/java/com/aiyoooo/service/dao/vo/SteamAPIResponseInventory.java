package com.aiyoooo.service.dao.vo;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/13 13:54
 */
@Data
public class SteamAPIResponseInventory {

    private List<SteamAPIResponseInventoryAsset> assets;
    private List<SteamAPIResponseInventoryDescription> descriptions;

    private Integer totalInventoryCount;
    private Integer success;
    private Integer rwgrsn;

    /**
     * 有分页情况这两个字段会有数据
     */
    private Integer moreItems;
    private String lastAssetid;
}