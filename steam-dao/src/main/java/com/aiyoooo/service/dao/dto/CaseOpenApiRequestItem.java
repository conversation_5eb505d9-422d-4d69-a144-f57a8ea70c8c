package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "开箱资产ID参数", description = "开箱资产ID参数")
public class CaseOpenApiRequestItem {

    @ApiModelProperty(name = "caseId", value = "caseId", position = 10)
    @NotNull(message = "caseId不能为空")
    private Long caseId;

    @ApiModelProperty(name = "keyId", value = "keyId", position = 20)
    @NotNull(message = "keyId不能为空")
    private Long keyId;
}
