package com.aiyoooo.service.dao.mapper;

import com.aiyoooo.service.dao.entity.t.TSteamAccountAssetHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * steam账号资产历史 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper
public interface TSteamAccountAssetHistoryMapper extends BaseMapper<TSteamAccountAssetHistory> {

    void upsertBatch(List<TSteamAccountAssetHistory> list);

    /**
     * 迁移资产历史数据到归档表
     * @param days 迁移多少天前的数据
     * @return 迁移的记录数量
     */
    int migrateToArchive(int days);
} 