package com.aiyoooo.service.dao.enums;

import lombok.Getter;

/**
 * Steam账号资产流向枚举
 */
@Getter
public enum EAccountAssetFlowDirectionEnum {
    
    IN("in", "收入"),
    OUT("out", "支出");

    private final String code;
    private final String desc;

    EAccountAssetFlowDirectionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EAccountAssetFlowDirectionEnum getByCode(String code) {
        for (EAccountAssetFlowDirectionEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 