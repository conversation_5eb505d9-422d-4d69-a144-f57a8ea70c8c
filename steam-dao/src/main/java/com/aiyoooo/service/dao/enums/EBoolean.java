package com.aiyoooo.service.dao.enums;

import java.util.HashMap;
import java.util.Map;

public enum EBoolean {

    /**
     * 错
     */
    NO("0", "错"),

    /**
     * 对
     */
    YES("1", "对"),

    ;

    private String code;
    private String value;

    EBoolean(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EBoolean> getBooleanResultMap() {
        Map<String, EBoolean> map = new HashMap<String, EBoolean>();
        for (EBoolean type : EBoolean.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EBoolean getBooleanResult(String code) {
        Map<String, EBoolean> map = getBooleanResultMap();
        EBoolean result = map.get(code);

        return result;
    }

}
