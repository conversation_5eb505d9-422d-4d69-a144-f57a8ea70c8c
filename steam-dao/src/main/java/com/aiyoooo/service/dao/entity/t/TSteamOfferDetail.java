package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * steam交易报价明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@TableName("t_steam_offer_detail")
@ApiModel(value = "TSteamOfferDetail对象", description = "steam交易报价明细")
public class TSteamOfferDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键编号")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("steam订单编号")
    private Long offerId;

    @ApiModelProperty("资产id")
    private String assetid;

    @ApiModelProperty("数量")
    private Integer amount;

    @ApiModelProperty("新资产id")
    private String newAssetid;
} 