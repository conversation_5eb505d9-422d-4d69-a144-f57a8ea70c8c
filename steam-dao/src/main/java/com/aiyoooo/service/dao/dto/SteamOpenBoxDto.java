package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "开箱结果通知", description = "开箱结果通知")
public class SteamOpenBoxDto {

    @ApiModelProperty(name = "taskId", value = "taskId", required = true, position = 20)
    @NotNull(message = "taskId不能为空")
    private Long taskId;

    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 20)
    @NotNull(message = "accountId不能为空")
    private Long accountId;

    @ApiModelProperty(name = "count", value = "数量", required = true, position = 20)
    @NotNull(message = "数量不能为空")
    private Integer count;

    @ApiModelProperty(name = "startTime", value = "开始的时间", required = true, position = 30)
    @NotBlank(message = "开始的时间不能为空")
    private String startTime;

    @ApiModelProperty(name = "endTime", value = "结束的时间", required = true, position = 40)
    @NotBlank(message = "结束的时间不能为空")
    private String endTime;

    @ApiModelProperty(name = "callbackUrl", value = "通知的Url", required = true, position = 50)
    @NotBlank(message = "通知的Url不能为空")
    private String callbackUrl;
}

    
    