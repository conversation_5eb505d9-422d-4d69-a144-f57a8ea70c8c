package com.aiyoooo.service.dao.vo;

import java.math.BigDecimal;
import lombok.Data;

/**
 * Steam账号
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class SteamAccountDetailVo {

    /**
     * Steam ID
     */
    String steamId;

    /**
     * 货币类型
     */
    String currency;

    /**
     * 余额
     */
    BigDecimal balance;

    /**
     * 地区
     */
    String place;

    /**
     * 邮箱
     */
    String email;

    /**
     * 保护类型：0.未启用，1.令牌电子邮件保护，2.令牌手机验证器保护，3.令牌手机验证器和电子邮件保护
     */
    Integer protectType;

    /**
     * 注册时间
     */
    Long timecreated;

    /**
     * 状态
     */
    String personastate;

    /**
     * 主页url
     */
    String profileurl;

    String cookieAccount;

    String cookieInventory;

    private String tradeUrl;

    String mafile;

    String mafileDepart;
}
