package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "交易offer详情查询Req", description = "账号通用查询Req")
public class SteamOfferDetailDto extends OpenBaseReq{

    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 10)
    @NotNull(message = "accountId不能为空")
    private Long accountId;

    @ApiModelProperty(name = "tradeOfferId", value = "tradeOfferId", required = true, position = 10)
    @NotNull(message = "id不能为空")
    private String tradeOfferId;

}

    
    