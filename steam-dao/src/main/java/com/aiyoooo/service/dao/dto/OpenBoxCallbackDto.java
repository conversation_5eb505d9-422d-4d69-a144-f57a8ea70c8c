package com.aiyoooo.service.dao.dto;

import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 开箱回调通知DTO
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Data
@ApiModel(value = "开箱回调通知", description = "开箱回调通知")
public class OpenBoxCallbackDto {

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("批次号")
    private Integer batchNo;

    @ApiModelProperty("成功数量")
    private Integer successCount;

    @ApiModelProperty("失败数量")
    private Integer failedCount;

    @ApiModelProperty("通知类型 BATCH_COMPLETED-批次完成 TASK_COMPLETED-整个任务完成")
    private String notifyType;

    @ApiModelProperty("开箱得到的资产列表（仅任务完成通知包含）")
    private List<TSteamAccountAsset> assets;


}
