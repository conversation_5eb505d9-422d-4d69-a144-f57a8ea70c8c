package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@TableName("t_sys_operation_log")
@ApiModel(value = "TSysOperationLog对象", description = "系统操作日志")
public class TSysOperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("日志主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("模块标题")
    private String module;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("操作描述")
    private String description;

    @ApiModelProperty("请求方法")
    private String method;

    @ApiModelProperty("请求方式")
    private String requestMethod;

    @ApiModelProperty("请求URL")
    private String requestUrl;

    @ApiModelProperty("请求IP地址")
    private String requestIp;

    @ApiModelProperty("请求参数")
    private String requestParam;

    @ApiModelProperty("响应结果")
    private String responseResult;

    @ApiModelProperty("授权编号")
    private String authCode;

    @ApiModelProperty("操作状态（0成功 1失败）")
    private String status;

    @ApiModelProperty("错误消息")
    private String errorMsg;

    @ApiModelProperty("操作时长(毫秒)")
    private Long costTime;

    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;
} 