package com.aiyoooo.service.dao.vo;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Steam账号
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class SteamAccountDetailSafeVo {

    @ApiModelProperty("steam账号")
    private String steamAccount;

    @ApiModelProperty("steam邮箱")
    private String steamEmail;

    @ApiModelProperty("steam密码")
    private String steamPwd;

    @ApiModelProperty("steamId")
    private String steamId;

    @ApiModelProperty("apikey")
    private String apikey;

    @ApiModelProperty("交易url")
    private String tradeUrl;

    @ApiModelProperty("地区")
    private String region;

    @ApiModelProperty("注册时间")
    private Long timecreated;

    @ApiModelProperty("账户等级")
    private String accountLevel;

    @ApiModelProperty("是否红信")
    private Boolean hasRedFlag;

    @ApiModelProperty("是否在线")
    private String personastate;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("头像")
    private String avatarUrl;

    @ApiModelProperty("余额")
    private BigDecimal balance;

    @ApiModelProperty("余额币种")
    private String currency;

    @ApiModelProperty("保护类型 0.未启用，1.令牌电子邮件保护，2.令牌手机验证器保护，3.令牌手机验证器和电子邮件保护")
    private Integer protectionType;

    @ApiModelProperty("cookieFlag")
    private String cookieFlag;

    @ApiModelProperty("mafile")
    private String mafile;

    @ApiModelProperty(name ="mafileDepart",value = "mafileDepart的json或者url")
    private String mafileDepart;
}
