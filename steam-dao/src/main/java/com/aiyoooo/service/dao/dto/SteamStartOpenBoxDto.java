package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "开始开箱", description = "开始开箱")
public class SteamStartOpenBoxDto {

    @ApiModelProperty(name = "taskId", value = "taskId", required = true, position = 20)
    @NotNull(message = "taskId")
    private Long taskId;

    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 20)
    @NotNull(message = "accountId不能为空")
    private Long accountId;

}

    
    