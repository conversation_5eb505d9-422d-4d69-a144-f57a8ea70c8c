package com.aiyoooo.service.dao.mapper;

import com.aiyoooo.service.dao.entity.t.TSteamOffer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * steam交易报价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper
public interface TSteamOfferMapper extends BaseMapper<TSteamOffer> {

    /**
     * 批量插入或更新
     *
     * @param list 交易报价列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<TSteamOffer> list);
} 