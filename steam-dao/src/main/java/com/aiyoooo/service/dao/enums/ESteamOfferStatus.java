package com.aiyoooo.service.dao.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * steam交易报价状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public enum ESteamOfferStatus {

    /**
     * 待卖家发货
     */
    STEAM_OFFER_STATUS_0("0", "待賣家發貨"),

    /**
     * 发货中-待卖家令牌确认
     */
    STEAM_OFFER_STATUS_1("1", "發貨中-待賣家令牌確認"),

    /**
     * 待买家确认
     */
    STEAM_OFFER_STATUS_2("2", "待買家確認"),

    /**
     * 交易成功
     */
    STEAM_OFFER_STATUS_3("3", "交易成功"),

    /**
     * 订单取消
     */
    STEAM_OFFER_STATUS_4("4", "訂單取消"),

    /**
     * 买方取消
     */
    STEAM_OFFER_STATUS_5("5", "买方取消"),

    /**
     * 卖方訂單取消
     */
    STEAM_OFFER_STATUS_6("6", "卖方取消"),

    /**
     * 不在库取消
     */
    STEAM_OFFER_STATUS_7("7", "不在库取消"),

    /**
     * 交易暫掛
     */
    STEAM_OFFER_STATUS_9("9", "交易暫掛"),

    ;

    private String code;
    private String value;

    ESteamOfferStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ESteamOfferStatus> getStatusMap() {
        Map<String, ESteamOfferStatus> map = new HashMap<>();
        for (ESteamOfferStatus type : ESteamOfferStatus.values()) {
            map.put(type.getCode(), type);
        }
        return map;
    }

    public static ESteamOfferStatus getByCode(String code) {
        return getStatusMap().get(code);
    }

    public static String getValueByCode(String code) {
        ESteamOfferStatus statusEnum = getByCode(code);
        return statusEnum == null ? "" : statusEnum.getValue();
    }
} 