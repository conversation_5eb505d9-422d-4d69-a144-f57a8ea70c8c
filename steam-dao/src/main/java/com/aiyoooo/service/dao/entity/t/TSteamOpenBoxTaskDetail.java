package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * steam开箱任务详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Getter
@Setter
@TableName("t_steam_open_box_task_detail")
@ApiModel(value = "TSteamOpenBoxTaskDetail对象", description = "steam开箱任务详情表")
public class TSteamOpenBoxTaskDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("批次号")
    private Integer batchNo;

    @ApiModelProperty("批次数量")
    private Integer batchCount;

    @ApiModelProperty("箱子资产ID列表，逗号分隔")
    private String caseAssetIds;

    @ApiModelProperty("钥匙资产ID列表，逗号分隔")
    private String keyAssetIds;

    @ApiModelProperty("状态 0-待处理 1-处理中 2-已完成 3-已失败")
    private String status;

    @ApiModelProperty("开箱结果资产ID列表，逗号分隔")
    private String resultAssetIds;

    @ApiModelProperty("开箱结果资产信息，List<TSteamAccountAsset>的JSON")
    private String resultAssetInfo;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("完成时间")
    private LocalDateTime finishTime;

    @ApiModelProperty("错误信息")
    private String errorMessage;
}
