package com.aiyoooo.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.TemplateType;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.baomidou.mybatisplus.generator.fill.Property;
import java.sql.Types;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

/**
 * MyBatis-Plus代码生成工具
 *
 * <AUTHOR>
 * @since 2024/12/10
 */
public class CodeGen {

    /**
     * 数据库配置
     **/
    static final String URL = "********************************************************************************************************************";
    static final String USER = "root";
    static final String PWD = "%McC^8N5Y#JJsaHB";
    static final String AUTHOR = "zhoudong";

    /**
     * 项目路径
     **/
    private static final String PARENT_DIR = System.getProperty("user.dir") + "/";
    /**
     * 基本路径
     **/
    private static final String SRC_MAIN_JAVA = "/src/main/java/";
    /**
     * dao模块
     **/
    private static final String DAO_MODEL = "steam-dao";
    /**
     * service模块
     **/
    private static final String SERVICE_MODEL = "steam-biz";
    /**
     * xml路径
     **/
    private static final String XML_PATH = PARENT_DIR + DAO_MODEL + "/src/main/resources/mapper";
    /**
     * entity路径
     **/
    private static final String ENTITY_PATH = PARENT_DIR + DAO_MODEL + SRC_MAIN_JAVA + "com/aiyoooo/service/dao/entity";
    /**
     * mapper路径
     **/
    private static final String MAPPER_PATH = PARENT_DIR + DAO_MODEL + SRC_MAIN_JAVA + "com/aiyoooo/service/dao/mapper";
    /**
     * service路径
     **/
    private static final String SERVICE_PATH = PARENT_DIR + SERVICE_MODEL + SRC_MAIN_JAVA + "com/aiyoooo/service/biz/service";
    /**
     * serviceImpl路径
     **/
    private static final String SERVICE_IMPL_PATH = PARENT_DIR + SERVICE_MODEL + SRC_MAIN_JAVA + "com/aiyoooo/service/biz/impl";

    /**
     * 读取控制台内容
     *
     * <AUTHOR>
     */
    protected static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder builder = new StringBuilder();
        builder.append("请输入" + tip + "：");
        System.out.println(builder.toString());
        if (scanner.hasNext()) {
            String val = scanner.next();
            if (StringUtils.isNotBlank(val)) {
                return val;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    /**
     * 处理all情况
     */
    protected static List<String> getTables(String tables) {
        return "all".equals(tables) ? Collections.emptyList() : Arrays.asList(tables.split(","));
    }

    /**
     * 获取路径信息Map
     */
    private static Map<OutputFile, String> getPathInfo(String tablePrefix) {
        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.entity, ENTITY_PATH + "/" + tablePrefix);
        pathInfo.put(OutputFile.mapper, MAPPER_PATH + "/" + tablePrefix);
        pathInfo.put(OutputFile.service, SERVICE_PATH + "/" + tablePrefix);
        pathInfo.put(OutputFile.serviceImpl, SERVICE_IMPL_PATH + "/" + tablePrefix);
        pathInfo.put(OutputFile.xml, XML_PATH);
        return pathInfo;
    }

    /**
     * 创建代码
     */
    protected static void create(String model, String table) {
        String modelPath = System.getProperty("user.dir") + "/" + model + "/";
        String tablePrefix = table.substring(0, table.indexOf("_"));
        FastAutoGenerator.create(URL, USER, PWD)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author(AUTHOR)
                            .outputDir(modelPath + "src/main/java")
                            .enableSwagger()
                            .disableOpenDir();
                })
                .dataSourceConfig(builder -> builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                    int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                    if (typeCode == Types.TINYINT) {
                        // 自定义类型转换
                        return DbColumnType.INTEGER;
                    }
                    return typeRegistry.getColumnType(metaInfo);

                }))
                // 包配置
                .packageConfig(builder -> builder
                        .parent("com.aiyoooo.service")
                        .entity("dao.entity." + tablePrefix)
                        .mapper("dao.mapper." + tablePrefix)
                        .service("biz.service." + tablePrefix)
                        .serviceImpl("biz.impl." + tablePrefix)
                        .pathInfo(getPathInfo(tablePrefix))
                )
                // 模板配置
                .templateConfig(builder -> {
                    if (SERVICE_MODEL.equals(model)) {
                        builder.disable(TemplateType.ENTITY).disable(TemplateType.MAPPER).xml(null);
                    } else if (DAO_MODEL.equals(model)) {
                        builder.disable(TemplateType.SERVICE).disable(TemplateType.SERVICE_IMPL);
                    }
                    builder.disable(TemplateType.CONTROLLER);
                })
                // 策略配置
                .strategyConfig(builder -> {
                    if (SERVICE_MODEL.equals(model)) {
                        builder.addInclude(table)
                                .serviceBuilder()
                                .formatServiceFileName("%sService");
                    } else if (DAO_MODEL.equals(model)) {
                        builder.addInclude(table)
                                .addTablePrefix("")
                                .entityBuilder()
                                .enableLombok()
                                .enableFileOverride()
                                .addTableFills(new Column("created_at", FieldFill.INSERT))
                                .addTableFills(new Property("createdBy", FieldFill.INSERT))
                                .addTableFills(new Property("updatedAt", FieldFill.UPDATE))
                                .addTableFills(new Property("updatedBy", FieldFill.INSERT_UPDATE));
                    }
                })
                .execute();
    }

    /**
     * 入口方法
     */
    public static void main(String[] args) {
        String model = scanner("模块名<dao|service|all>");
        String[] models = null;
        if ("dao".equals(model)) {
            models = new String[1];
            models[0] = DAO_MODEL;
        } else if ("service".equals(model)) {
            models = new String[1];
            models[0] = SERVICE_MODEL;
        } else {
            models = new String[2];
            models[0] = DAO_MODEL;
            models[1] = SERVICE_MODEL;
        }
        List<String> tables = getTables(scanner("请输入表名，多个英文逗号分隔？所有输入 all"));
        for (String m : models) {
            for (String table : tables) {
                create(m, table);
            }
        }
    }
}
