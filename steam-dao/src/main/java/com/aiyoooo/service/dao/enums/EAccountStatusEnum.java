package com.aiyoooo.service.dao.enums;

import lombok.Getter;

/**
 * Steam账号状态枚举
 */
@Getter
public enum EAccountStatusEnum {
    
    BINDING("0", "绑定中"),
    UNBOUND("1", "已解绑");

    private final String code;
    private final String desc;

    EAccountStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EAccountStatusEnum getByCode(String code) {
        for (EAccountStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 