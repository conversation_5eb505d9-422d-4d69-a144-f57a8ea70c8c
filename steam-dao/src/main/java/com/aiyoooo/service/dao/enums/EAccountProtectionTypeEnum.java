package com.aiyoooo.service.dao.enums;

import lombok.Getter;

/**
 * Steam账号保护类型枚举
 */
@Getter
public enum EAccountProtectionTypeEnum {
    
    DISABLED("0", "未启用"),
    EMAIL("1", "令牌电子邮件保护"),
    MOBILE("2", "令牌手机验证器保护"),
    BOTH("3", "令牌手机验证器和电子邮件保护");

    private final String code;
    private final String desc;

    EAccountProtectionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EAccountProtectionTypeEnum getByCode(String code) {
        for (EAccountProtectionTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 