package com.aiyoooo.service.dao.entity.t;

import com.aiyoooo.service.common.util.CustomerStrToJsonObjectSerialize;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * steam账号资产
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@TableName("t_steam_account_asset")
@ApiModel(value = "TSteamAccountAsset对象", description = "steam账号资产")
public class TSteamAccountAsset implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("资产id")
    private String assetid;

    @ApiModelProperty("账号id")
    private Long accountId;

    @ApiModelProperty("道具id")
    private String classid;

    @ApiModelProperty("instanceid")
    private String instanceid;

    @ApiModelProperty("名称")
    private String marketName;

    @ApiModelProperty("market_hash_name")
    private String marketHashName;

    @ApiModelProperty("在库状态 0在库可用 1在库CD中 2已出库")
    private String inventoryStatus;

    @ApiModelProperty("可交易时间")
    private Date tradeStartDatetime;

    @ApiModelProperty("入库时间")
    private Date stockInDatetime;

    @ApiModelProperty("出库时间")
    private Date stockOutDatetime;

    @ApiModelProperty("来源账号")
    private String sourceSteamId;

    @ApiModelProperty("流向账号")
    private String targetSteamId;

    @ApiModelProperty("磨损度")
    private BigDecimal exteriorValue;

    @ApiModelProperty("资产json")
    @JsonSerialize(using = CustomerStrToJsonObjectSerialize.class)
    private String description;

    @ApiModelProperty(value = "checkExist", hidden = true)
    private String checkExist;

    @ApiModelProperty(value = "lastRefreshDatetime", hidden = true)
    private Date lastRefreshDatetime;
} 