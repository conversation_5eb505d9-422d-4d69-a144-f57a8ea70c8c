package com.aiyoooo.service.dao.entity.tsys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * apikey 配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
@TableName("tsys_apikey")
@ApiModel(value = "TsysApikey对象", description = "apikey 配置")
public class SysApikey implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("授权编号")
    private String authCode;

    @ApiModelProperty("RSA pulic_key")
    private String publicKey;

    @ApiModelProperty("secretKey")
    private String secretKey;

    @ApiModelProperty("状态(1=生效中 0=已失效)")
    private String status;

    @ApiModelProperty("offer自动确认开关(1=开 0=关)")
    private String offerAutoSwitch;

    @ApiModelProperty("创建时间")
    private LocalDateTime createDatetime;

    @ApiModelProperty("更新人")
    private String updater;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateDatetime;

    @ApiModelProperty("备注")
    private String remark;
}
