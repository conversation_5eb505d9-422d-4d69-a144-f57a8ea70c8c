package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "steam账号登录会话", description = "steam账号登录会话")
public class SteamAccountLoginSessionDto extends OpenBaseReq{

    /**
     * ID
     */
    @ApiModelProperty(name = "id", value = "ID", required = true, position = 10)
    @NotNull(message = "id不能為空")
    private Long id;

    /**
     * 令牌码
     */
    @NotBlank(message = "令牌码不能为空")
    @ApiModelProperty(name = "tokenCode", value = "令牌码不能为空", position = 30)
    private String tokenCode;
}

    
    