package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "steam账号更新cookie", description = "steam账号更新cookie")
public class SteamAccountRefreshCookieDto {

    @ApiModelProperty(name = "id", value = "id", required = true, position = 10)
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(name = "cookieAccount", value = "账户cookie", required = true, position = 20)
    private String cookieAccount;

    @ApiModelProperty(name = "cookieInventory", value = "库存cookie", required = true, position = 30)
    private String cookieInventory;
}

    
    