package com.aiyoooo.service.dao.enums;

import lombok.Getter;

/**
 * 开箱任务状态枚举
 */
@Getter
public enum EOpenBoxTaskStatusEnum {
    
    PENDING("0", "待处理"),
    PROCESSING("1", "处理中"),
    COMPLETED("2", "已完成"),
    FAILED("3", "已失败");

    private final String code;
    private final String desc;

    EOpenBoxTaskStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EOpenBoxTaskStatusEnum getByCode(String code) {
        for (EOpenBoxTaskStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
