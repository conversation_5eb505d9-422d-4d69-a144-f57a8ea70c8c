package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "开箱入参", description = "开箱入参")
public class OpenCaseCreateDto extends OpenBaseReq {


    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 10)
    @NotNull(message = "accountId")
    private Long accountId;

    @ApiModelProperty(name = "itemList", value = "要开资产列表", required = true, position = 20)
    @NotEmpty(message = "待开资产不能为空")
    private List<CaseOpenApiRequestItem> itemList;

}

    
    