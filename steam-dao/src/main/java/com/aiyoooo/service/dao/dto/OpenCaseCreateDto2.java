package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 周栋
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "异步开箱入参", description = "异步开箱入参")
public class OpenCaseCreateDto2 extends OpenBaseReq {


    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 10)
    @NotNull(message = "accountId")
    private Long accountId;

    @ApiModelProperty(name = "boxName", value = "箱子名称", required = true, position = 20)
    @NotBlank(message = "箱子名称不能为空")
    private String boxName;

    @ApiModelProperty(name = "keyName", value = "钥匙名称", required = true, position = 30)
    @NotBlank(message = "钥匙名称不能为空")
    private String keyName;

    @ApiModelProperty(name = "count", value = "开启数量", required = true, position = 40)
    @NotNull(message = "数量不能为空")
    private Integer count;

    @ApiModelProperty(name = "callbackUrl", value = "通知的Url", required = true, position = 50)
    @NotBlank(message = "通知的Url不能为空")
    private String callbackUrl;

}

    
    