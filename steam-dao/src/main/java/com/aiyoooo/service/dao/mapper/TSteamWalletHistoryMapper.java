package com.aiyoooo.service.dao.mapper;

import com.aiyoooo.service.dao.entity.t.TSteamWalletHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * steam钱包历史 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper
public interface TSteamWalletHistoryMapper extends BaseMapper<TSteamWalletHistory> {

    void insertOrUpdateBatch(@Param("list") List<TSteamWalletHistory> list);

    /**
     * 迁移钱包历史数据到归档表
     * @param days 迁移多少天前的数据
     * @return 迁移的记录数量
     */
    int migrateToArchive(int days);
} 