package com.aiyoooo.service.dao.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/13 13:54
 */
@Data
public class SteamAPIResponseInvestoryDescriptionsActions {

    private String link;

    private String name;

    private String d;

    public String getD() {
        if (StringUtils.isNotBlank(link)){
            int dIndex = link.indexOf("D");
            if (dIndex != -1) {
                 d = link.substring(dIndex);
            }
        }
        return d;
    }

}