package com.aiyoooo.service.dao.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.util.List;

/**
 * Steam交易详情
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class SteamTradeDetailVo {

    public Response response;

    @Data
    public static class Response {

        public Offer offer;

        @Data
        public static class Offer {
            @Alias("tradeofferid")
            public String tradeOfferId;

            @Alias("accountid_other")
            public long accountIdOther;

            public String message;

            @<PERSON>as("expiration_time")
            public long expirationTime;

            @Alias("trade_offer_state")
            public int tradeOfferState;

            @Alias("items_to_give")
            public List<Item> itemsToGive;

            @Alias("items_to_receive")
            public List<Item> itemsToReceive;

            @Alias("is_our_offer")
            public boolean isOurOffer;

            @Alias("time_created")
            public long timeCreated;

            @Alias("time_updated")
            public long timeUpdated;

            @<PERSON>as("from_real_time_trade")
            public boolean fromRealTimeTrade;

            @<PERSON>as("tradeid")
            public String tradeid;

            @Alias("escrow_end_date")
            public int escrowEndDate;

            @Alias("confirmation_method")
            public int confirmationMethod;

            @Alias("eresult")
            public int eResult;


            @Data
            public static class Item {
                @Alias("appid")
                public int appId;

                @Alias("contextid")
                public String contextId;

                @Alias("assetid")
                public String assetId;

                @Alias("classid")
                public String classId;

                @Alias("instanceid")
                public String instanceId;

                public String amount;

                public boolean missing;

                @Alias("est_usd")
                public String estUsd;
            }
        }
    }
}
