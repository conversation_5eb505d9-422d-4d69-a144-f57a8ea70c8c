package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "steam账号更新cookie标志", description = "steam账号更新cookie标志")
public class SteamAccountRefreshCookieFlagDto {

    @ApiModelProperty(name = "id", value = "id", required = true, position = 10)
    @NotNull(message = "id不能为空")
    private Long id;
}

    
    