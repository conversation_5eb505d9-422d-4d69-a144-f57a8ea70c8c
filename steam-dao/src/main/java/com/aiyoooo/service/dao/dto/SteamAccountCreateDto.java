package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "steam账号入参", description = "steam账号入参")
public class SteamAccountCreateDto extends OpenBaseReq {

    /**
     * steam账号
     */
    @ApiModelProperty(name = "steamAccount", value = "steam账号", required = true, position = 10)
    @NotBlank(message = "steam账号不能为空")
    private String steamAccount;
    /**
     * steam密码
     */
    @ApiModelProperty(name = "steamPwd", value = "steam密码", required = true, position = 20)
    @NotBlank(message = "steam密码不能为空")
    private String steamPwd;

    @ApiModelProperty(name ="mafile",value = "mafile的json或者url",  required = false, position = 30)
    private String mafile;

    @ApiModelProperty(name ="mafileDepart",value = "mafileDepart的json或者url",  required = false, position = 35)
    private String mafileDepart;

    @ApiModelProperty(name ="queryLevel",value = "频率（如：1-低频、2-中频、3-高频）",  required = true, position = 40)
    @NotBlank(message = "queryLevel不能为空")
    private String queryLevel;

    @ApiModelProperty(name ="safeLevel",value = "安全等级 1 一般",  required = true, position = 50)
    @NotBlank(message = "safeLevel不能为空")
    private String safeLevel;


    @ApiModelProperty(name ="tag",value = "标记",  required = false, position = 70)
    private String tag;

    @ApiModelProperty(name ="useCategory",value = "使用大类",  required = false, position = 80)
    private String useCategory;

    @ApiModelProperty(name ="useClass",value = "使用小类",  required = false, position = 85)
    private String  useClass;


}

    
    