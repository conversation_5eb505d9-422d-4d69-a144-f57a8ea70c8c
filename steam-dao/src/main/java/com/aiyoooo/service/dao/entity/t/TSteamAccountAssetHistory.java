package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * steam账号资产历史
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@TableName("t_steam_account_asset_history")
@ApiModel(value = "TSteamAccountAssetHistory对象", description = "steam账号资产历史")
public class TSteamAccountAssetHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("唯一交易项ID")
    private String historyItemId;

    @ApiModelProperty("交易时间")
    private Date tradeDate;

    @ApiModelProperty("交易类型")
    private String tradeType;

    @ApiModelProperty("交易说明")
    private String description;

    @ApiModelProperty("饰品名称")
    private String name;

    @ApiModelProperty("物品资产ID")
    private String assetid;

    @ApiModelProperty("物品类型ID")
    private String classid;

    @ApiModelProperty("instanceid")
    private String instanceid;

    @ApiModelProperty("来源账号或流向账号")
    private String steamId;

    @ApiModelProperty("物品流向 in=收入，out=支出")
    private String flowDirection;

    @ApiModelProperty("createTime")
    private Date createDatetime;

    @ApiModelProperty("accountId")
    private Long accountId;
} 