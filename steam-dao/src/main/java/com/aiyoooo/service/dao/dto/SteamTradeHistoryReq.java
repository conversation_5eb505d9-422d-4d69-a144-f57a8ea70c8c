package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 平台出售交易报价
 *
 * <AUTHOR> xieyj
 * @since : 2025-03-27 22:47
 */
@Data
public class SteamTradeHistoryReq extends OpenBaseReq {

    /**
     * steam账号id
     */
    @ApiModelProperty(name = "accountId", value = "accountId", required = true, position = 10)
    @NotNull(message = "accountId")
    private Long accountId;

    @ApiModelProperty(name = "startAfterTime", value = "在某个时间之后开始 (秒)", required = false, position = 20)
    private Long startAfterTime;

    @ApiModelProperty(name = "startAfterTradeid", value = "在某个交易之后开始", required = false, position = 20)
    private Long startAfterTradeid;

}
