package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "steam账号更新steamId", description = "steam账号更新steamId")
public class SteamAccountRefreshSteamAccountDto  extends OpenBaseReq{

    @ApiModelProperty(name = "id", value = "id", required = true, position = 10)
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(name = "steam密码", value = "steamPwd", required = false, position = 20)
    private String steamPwd;

    @ApiModelProperty(name = "mafile", value = "mafile", required = false, position = 30)
    private String mafile;

    @ApiModelProperty(name ="mafileDepart",value = "mafileDepart的json或者url",  required = false, position = 35)
    private String mafileDepart;

    @ApiModelProperty(name = "status", value = "账号绑定状态 0绑定,1解绑(解绑后不再进行数据的查询)", required = false, position = 40)
    private String status;

    @ApiModelProperty(name ="queryLevel",value = "频率（如：1-低频、2-中频、3-高频）",  required = false, position = 50)
    private String queryLevel;

    @ApiModelProperty(name ="safeLevel",value = "安全等级 1 一般",  required = false, position = 60)
    private String safeLevel;


    @ApiModelProperty(name ="tag",value = "标记",  required = false, position = 70)
    private String tag;

    @ApiModelProperty(name ="useCategory",value = "使用大类",  required = false, position = 80)
    private String useCategory;

    @ApiModelProperty(name ="useClass",value = "使用小类",  required = false, position = 85)
    private String  useClass;
}

    
    