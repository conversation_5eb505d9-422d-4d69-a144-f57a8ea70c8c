package com.aiyoooo.service.dao.enums;

/**
 * steam官方的交易状态
 */
public enum ESteamOfferTradeState {

    /**
     * 交易无效
     */
    STEAM_OFFER_TRADE_STATE_1(1, "交易无效"),

    /**
     * 待对方确认
     */
    STEAM_OFFER_TRADE_STATE_2(2, "待对方确认"),

    /**
     * 交易成功
     */
    STEAM_OFFER_TRADE_STATE_3(3, "交易成功"),

    /**
     * 发起还价
     */
    STEAM_OFFER_TRADE_STATE_4(4, "发起还价"),

    /**
     * 在规定的时间未被接受
     */
    STEAM_OFFER_TRADE_STATE_5(5, "在规定的时间未被接受"),

    /**
     * 发起人取消
     */
    STEAM_OFFER_TRADE_STATE_6(6, "发起人取消"),

    /**
     * 接收人取消
     */
    STEAM_OFFER_TRADE_STATE_7(7, "接收人取消"),

    /**
     * 交易中涉及的某些物品变得无效（例如：物品已被交易、已出售或被市场移除）
     */
    STEAM_OFFER_TRADE_STATE_8(8, "报价中的一些项目不再可用（由输出中的缺失标志表示）"),

    /**
     * 待手机确认(offer还没有发出，正在等待邮件/手机确认。报价只对发送方可见。)
     */
    STEAM_OFFER_TRADE_STATE_9(9, "待手机确认"),

    /**
     * 任何一方通过电子邮件/手机取消报价。报价对双方都是可见的，即使发送方在发送之前取消了报价。
     */
    STEAM_OFFER_ORDER_STATE_10(10, "手机确认撤销"),

    /**
     * 该交易已被暂停。此次交易中涉及的物品已经全部从双方的库存中移除，并将在未来自动发货。
     */
    STEAM_OFFER_ORDER_STATE_11(11, "交易暂挂"),

    ;

    private int code;
    private String value;

    ESteamOfferTradeState(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
