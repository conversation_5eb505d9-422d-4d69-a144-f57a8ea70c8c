package com.aiyoooo.service.dao.mapper;

import com.aiyoooo.service.dao.entity.t.TSteamOfferDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * steam交易报价明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper
public interface TSteamOfferDetailMapper extends BaseMapper<TSteamOfferDetail> {

    /**
     * 批量插入或更新
     *
     * @param list 交易报价明细列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<TSteamOfferDetail> list);
    
    /**
     * 根据交易报价ID删除明细
     *
     * @param offerId 交易报价ID
     * @return 影响行数
     */
    int deleteByOfferId(@Param("offerId") Long offerId);
} 