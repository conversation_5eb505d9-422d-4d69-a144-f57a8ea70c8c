package com.aiyoooo.service.dao.mapper.t;

import com.aiyoooo.service.dao.entity.t.TSteamAccountAssetOutStock;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Mapper
public interface TSteamAccountAssetOutStockMapper extends BaseMapper<TSteamAccountAssetOutStock> {

    /**
     * 批量插入出库资产数据
     *
     * @param list 出库资产列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TSteamAccountAssetOutStock> list);
    
    /**
     * 直接从资产表迁移数据到出库表
     *
     * @return 迁移的记录数
     */
    int migrateFromAsset();
}
