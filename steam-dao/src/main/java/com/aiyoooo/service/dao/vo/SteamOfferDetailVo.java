package com.aiyoooo.service.dao.vo;

import com.aiyoooo.service.dao.entity.t.TSteamOfferDetail;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * steam交易报价
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@TableName("t_steam_offer")
@ApiModel(value = "SteamOfferDetailVo对象", description = "SteamOfferDetailVo")
public class SteamOfferDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("steam订单编号")
    private Long offerId;

    @ApiModelProperty("资产id")
    private String assetid;

    @ApiModelProperty("数量")
    private Integer amount;

    @ApiModelProperty("新资产id")
    private String newAssetid;

} 