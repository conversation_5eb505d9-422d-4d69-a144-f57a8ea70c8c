package com.aiyoooo.service.dao.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/23 16:52
 */
@Data
@ApiModel(value = "详情查询Req", description = "详情查询Req")
public class BaseDetailDto extends OpenBaseReq{

    @ApiModelProperty(name = "id", value = "id", required = false, position = 10)
    private Long id;

    @ApiModelProperty(name = "orderNo", value = "orderNo", required = false, position = 20)
    private String orderNo;

}

    
    