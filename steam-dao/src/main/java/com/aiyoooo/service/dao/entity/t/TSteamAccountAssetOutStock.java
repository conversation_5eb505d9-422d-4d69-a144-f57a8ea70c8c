package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@Setter
@TableName("t_steam_account_asset_out_stock")
@ApiModel(value = "TSteamAccountAssetOutStock对象", description = "")
public class TSteamAccountAssetOutStock implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("资产id")
    private String assetid;

    @ApiModelProperty("账号id")
    private Long accountId;

    @ApiModelProperty("道具id")
    private String classid;

    @ApiModelProperty("instanceid")
    private String instanceid;

    @ApiModelProperty("名称")
    private String marketName;

    @ApiModelProperty("可交易时间")
    private LocalDateTime tradeStartDatetime;

    @ApiModelProperty("入库时间")
    private LocalDateTime stockInDatetime;

    @ApiModelProperty("出库时间")
    private LocalDateTime stockOutDatetime;

    @ApiModelProperty("来源账号")
    private String sourceSteamId;

    @ApiModelProperty("流向账号")
    private String targetSteamId;

    @ApiModelProperty("磨损度")
    private BigDecimal exteriorValue;

    @ApiModelProperty("market_hash_name")
    private String marketHashName;

    @ApiModelProperty("在库状态 0在库可用 1在库CD中 2已出库")
    private String inventoryStatus;

    @ApiModelProperty("资产json")
    private String description;

    @ApiModelProperty("是否存在check")
    private String checkExist;
}
