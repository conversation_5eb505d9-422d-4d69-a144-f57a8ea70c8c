package com.aiyoooo.service.dao.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * steam账号Enum
 *
 * <AUTHOR> xieyj
 * @since : 2024-12-10 15:56
 */
public enum ESteamAccountStatus {

    /**
     * 绑定中
     */
    STEAM_ACCOUNT_STATUS_0("0", "放置中"),

    /**
     * 激活中
     */
    STEAM_ACCOUNT_STATUS_1("1", "激活中"),

    /**
     * 已解绑
     */
    STEAM_ACCOUNT_STATUS_2("2", "已解绑"),

    ;

    private String code;
    private String value;

    ESteamAccountStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ESteamAccountStatus> getSteamAccountStatusResultMap() {
        Map<String, ESteamAccountStatus> map = new HashMap<String, ESteamAccountStatus>();
        for (ESteamAccountStatus type : ESteamAccountStatus.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ESteamAccountStatus getSteamAccountStatus(String code) {
        Map<String, ESteamAccountStatus> map = getSteamAccountStatusResultMap();
        ESteamAccountStatus result = map.get(code);

        return result;
    }

}
