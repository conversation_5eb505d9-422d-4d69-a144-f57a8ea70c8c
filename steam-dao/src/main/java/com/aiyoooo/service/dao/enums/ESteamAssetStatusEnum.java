package com.aiyoooo.service.dao.enums;

import lombok.Getter;

/**
 * Steam账号资产状态枚举
 */
@Getter
public enum ESteamAssetStatusEnum {
    
    AVAILABLE("0", "在库可用"),
    COOLDOWN("1", "在库CD中"),
    OUT_OF_STOCK("2", "已出库");

    private final String code;
    private final String desc;

    ESteamAssetStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ESteamAssetStatusEnum getByCode(String code) {
        for (ESteamAssetStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 