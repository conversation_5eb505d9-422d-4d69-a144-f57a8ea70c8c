package com.aiyoooo.service.dao.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Steam账号
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class OpenCaseApiVo {

    //{
    //    "success": false,
    //    "obtainedAssetIds": [],
    //    "errorMessage": "所有开箱请求都失败了",
    //    "errorCode": "CASE_OPEN_FAILED",
    //    "processedCount": 9,
    //    "successfulCount": 0,
    //    "processingTimeSeconds": null,
    //    "timestamp": "2025-08-27T05:58:49.675591Z",
    //    "sessionStatus": {
    //        "isConnected": true,
    //        "isLoggedIn": true,
    //        "isGCReady": true,
    //        "isSessionReady": true,
    //        "username": "dx823374",
    //        "isReconnecting": false,
    //        "reconnectAttempts": 0,
    //        "pendingRequestsCount": 0,
    //        "timestamp": "2025-08-27T05:58:49.675594Z"
    //    }
    //}


    private Boolean success;

    private List<Long> obtainedAssetIds;

    private String errorCode;

    private String errorMessage;

    private Integer processedCount;

    private Integer successfulCount;

}
