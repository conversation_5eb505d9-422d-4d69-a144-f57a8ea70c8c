package com.aiyoooo.service.dao.vo;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> xieyj
 * @since : 2024/12/13 13:54
 */
@Data
public class SteamAPIResponseInventoryDescription {

    private int appid;
    private String classid;
    private String instanceid;
    private String currency;

    private String backgroundColor;
    private String iconUrl;

    private List<Object> descriptions;

    private List<SteamAPIResponseInvestoryOwnerDescription> ownerDescriptions;

    private String name;
    private String nameColor;

    private String type;
    private String marketName;
    private String marketHashName;

    private List<SteamAPIResponseInvestoryDescriptionsActions> marketActions;

    private String tradable;

    private List<SteamAPIResponseInvestoryDescriptionsActions> actions;

    private String commodity;
    private String marketTradableRestriction;
    private String marketMarketableRestriction;
    private String marketable;

    private List<SteamAPIResponseInvestorTag> tags;
}