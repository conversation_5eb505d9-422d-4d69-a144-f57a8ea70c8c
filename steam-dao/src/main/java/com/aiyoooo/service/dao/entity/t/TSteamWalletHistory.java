package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * steam钱包历史
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@TableName("t_steam_wallet_history")
@ApiModel(value = "TSteamWalletHistory对象", description = "steam钱包历史")
public class TSteamWalletHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Integer id;

    private Long accountId;

    @ApiModelProperty("交易日期")
    private Date transactionDate;

    @ApiModelProperty("交易物品或内容描述")
    private String itemDescription;

    @ApiModelProperty("交易类型")
    private String transactionType;

    @ApiModelProperty("支付方式或来源")
    private String paymentMethod;

    @ApiModelProperty("交易总金额")
    private String totalAmount;

    @ApiModelProperty("钱包变更金额")
    private String walletChange;

    @ApiModelProperty("交易后的钱包余额")
    private String walletBalance;

    @ApiModelProperty("帮助页面链接")
    private String transactionLink;

    @ApiModelProperty("交易ID")
    private String transactionId;

    @ApiModelProperty("createDatetime")
    private Date createDatetime;
} 