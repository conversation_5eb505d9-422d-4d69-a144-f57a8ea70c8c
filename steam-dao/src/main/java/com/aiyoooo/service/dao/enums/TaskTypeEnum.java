package com.aiyoooo.service.dao.enums;

// 任务类型枚举
public enum TaskTypeEnum {
    COOKIE_SYNC("steam:sync:cookie", "cookie同步","account_cookie"),
    REFRESH_TOKEN("steam:refreshtoken", "cookie刷新","account_cookie"),
    BASE_INFO_SYNC("steam:sync:baseinfo", "基本信息同步","account"),
    TRADE_URL_SYNC("steam:sync:tradeurl", "交易URL同步","account"),
    API_KEY_SYNC("steam:sync:apikey", "API Key同步","account"),
    INVENTORY_SYNC("steam:sync:inventory", "库存同步","account"),
    WALLET_SYNC("steam:sync:wallet", "钱包同步","account"),
    STEAM_OFFER_SYNC("steam:offer", "offer同步","offer"),

//    STEAM_OFFER_CANCEL_SYNC("steam:offer:cancel", "offer同步取消","offer"),

    OPEN_BOX_SYNC("steam:openbox", "开箱","openbox"),
    ;

    private final String prefix;
    private final String desc;
    private final String model;

    TaskTypeEnum(String prefix, String desc, String model) {
        this.prefix = prefix;
        this.desc = desc;
        this.model = model;
    }

    public String getQueueKey() {
        return prefix + ":queue";
    }

    public String getSetKey() {
        return prefix + ":set";
    }

    public String getDesc() {
        return desc;
    }

    public String getModel() {
        return model;
    }
}