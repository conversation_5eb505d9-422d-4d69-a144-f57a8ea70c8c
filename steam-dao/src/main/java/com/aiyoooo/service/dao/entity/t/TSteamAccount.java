package com.aiyoooo.service.dao.entity.t;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * steam账号
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
@TableName("t_steam_account")
@ApiModel(value = "TSteamAccount对象", description = "steam账号")
public class TSteamAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty("授权编号")
    private String authCode;

    @ApiModelProperty("steam账号")
    private String steamAccount;

    @ApiModelProperty("steam密码")
    private String steamPwd;

    @ApiModelProperty("mafile")
    private String mafile;

    @ApiModelProperty(name ="mafileDepart",value = "mafileDepart的json或者url")
    private String mafileDepart;

    @ApiModelProperty("cookie标志 0=待生效,1=有效 2=已失效")
    private String cookieFlag;

    @ApiModelProperty("访问令牌")
    private String accessToken;

    @ApiModelProperty("刷新令牌")
    private String refreshToken;

    @ApiModelProperty("最新sessionid")
    private String lastSessionid;

    @ApiModelProperty("最新cookie")
    private String lastCookie;

    @ApiModelProperty("状态 dict={\"0\":\"绑定中\",\"1\":\"已解绑\"}")
    private String status;

    @ApiModelProperty("频率（如：1-低频、2-中频、3-高频）")
    private String  queryLevel;

    @ApiModelProperty("安全等级（如：")
    private String safeLevel;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updatedAt;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(name ="tag",value = "标记")
    private String tag;

    @ApiModelProperty(name ="useCategory",value = "使用大类")
    private String useCategory;

    @ApiModelProperty(name ="useClass",value = "使用小类")
    private String  useClass;

    @ApiModelProperty("资产最后变动id")
    private String assetLastHistoryJson;

}
