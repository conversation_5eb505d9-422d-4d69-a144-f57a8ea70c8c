package com.aiyoooo.service.dao.enums;

import lombok.Getter;

/**
 * 开箱通知类型枚举
 */
@Getter
public enum EOpenBoxNotifyTypeEnum {

    BATCH_COMPLETED("BATCH_COMPLETED", "批次完成"),
    TASK_COMPLETED("TASK_COMPLETED", "整个任务完成");

    private final String code;
    private final String desc;

    EOpenBoxNotifyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EOpenBoxNotifyTypeEnum getByCode(String code) {
        for (EOpenBoxNotifyTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
