<?xml version="1.0" encoding="UTF-8"?>
<configuration status="WARN" monitorInterval="30">

    <Properties>
        <property name="logPattern">%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight{%-5level}{ERROR=Bright RED, WARN=Bright Yellow, INFO=Bright
            Green, DEBUG=Bright Cyan, TRACE=Bright White} %style{[%t]}{bright,magenta} %style{%c{1.}.%M(%L)}{cyan}: %msg%n
        </property>
        <property name="basePath">/mnt/www/steam/logs</property>
        <property name="projectName">steam-service-admin</property>
    </Properties>
    <CustomLevels>
        <CustomLevel name="DIAG" intLevel="199"/>
        <CustomLevel name="NOTICE" intLevel="399"/>
    </CustomLevels>
    <!--先定义所有的appender-->
    <appenders>
        <!--这个输出控制台的配置-->
        <console name="Console" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${logPattern}"/>
        </console>
        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingFile name="RollingFileInfo" fileName="${basePath}/info.log"
                filePattern="${basePath}/%d{yyyy-MM}/info-%d{yyyy-MM-dd}-%i.log"
                immediateFlush="true">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${logPattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingFile>

        <RollingFile name="RollingFileError" fileName="${basePath}/error.log"
                filePattern="${basePath}/$${date:yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log">
            <filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </filters>
            <PatternLayout pattern="${logPattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

    </appenders>
    <!--然后定义logger，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>
        <!--过滤掉spring、motan、rocketmq的一些无用的信息-->
        <logger name="org.springframework" level="ERROR" additivity="false"/>
        <logger name="org.mybatis" level="INFO"></logger>

        <root level="INFO">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileError"/>
        </root>
    </loggers>

</configuration>