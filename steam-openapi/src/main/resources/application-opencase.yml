#----------------------------------------------------------------------------------------------------------------------
# 数据库配置
#----------------------------------------------------------------------------------------------------------------------
spring:
  datasource:
    url: **********************************************************************************************************************************************************
    username: "key_mall_prod"
    password: "kn8aHOdvIDK7vL6L"
    druid:
      initial-size: 5                                                  # 连接池初始化大小
      min-idle: 10                                                     # 最小空闲连接数
      max-active: 20                                                   # 最大连接数
      web-stat-filter:
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"      # 不统计这些请求数据
      stat-view-servlet: # 访问监控网页的登录用户名和密码
        login-username: druid
        login-password: druid
    driver-class-name: com.mysql.cj.jdbc.Driver

  #----------------------------------------------------------------------------------------------------------------------
  # Redis配置
  #----------------------------------------------------------------------------------------------------------------------
  redis:
    database: 10                                                          # Redis数据库索引（默认为0）
    host: ************                                                    # Redis服务器地址
    port: 6379                                                           # Redis服务器连接端口
#    username: r-bp1x00bcij4m28mjvt
    password: HuN!RPu!yNz6Bhb                                   # Redis服务器连接密码（默认为空）
    lettuce:
      pool:
        max-active: 200                                                  # 连接池最大连接数
        max-wait: -1ms                                                   # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10                                                     # 连接池中的最大空闲连接
        min-idle: 0                                                      # 连接池中的最小空闲连接

#----------------------------------------------------------------------------------------------------------------------
# sa-token环境配置
#----------------------------------------------------------------------------------------------------------------------
sa-token:
  token-name: Authorization                                              # token名称 (同时也是cookie名称)
  timeout: -1                                                            # 设置成永不过期
  is-concurrent: true
  is-share: false
  oauth2: # OAuth2.0 配置
    is-code: true
    is-implicit: true
    is-password: true
    is-client: true

#----------------------------------------------------------------------------------------------------------------------
# 日志配置
#----------------------------------------------------------------------------------------------------------------------
logging:
  config: classpath:log4j2-dev.xml
  level:
    root: info

springdoc:
  swagger-ui:
    path: /api-docs
  api-docs:
    path: /openapi.json

task:
  consumer-count: 3

open-case-url: http://localhost:5000/api/caseopener/open-cases
