#----------------------------------------------------------------------------------------------------------------------
# 服务配置
#----------------------------------------------------------------------------------------------------------------------
server:
  port: 8800

#----------------------------------------------------------------------------------------------------------------------
# 环境配置
#----------------------------------------------------------------------------------------------------------------------
spring:
  application:
    name: steam-service
  profiles:
    active: opencase                                   # 默认为开发环境
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# Swagger配置
springfox:
  documentation:
    swagger:
      v2:
        path: /v2/api-docs
    swagger-ui:
      path: /swagger-ui.html