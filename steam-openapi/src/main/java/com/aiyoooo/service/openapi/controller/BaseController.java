package com.aiyoooo.service.openapi.controller;

import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.service.tsys.TsysApikeyService;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.dao.dto.OpenBaseReq;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;


import javax.annotation.Resource;

public abstract class BaseController {

    @Resource
    protected TsysApikeyService tsysApikeyService;

    @Resource
    protected TSteamAccountService tSteamAccountService;


    /**
     * 验证签名,验证账号
     *
     * @param request
     * @param verifyAccount
     * @return
     */
    protected TSteamAccount verifyAndGetAccount(Object request, boolean verifyAccount) {
        OpenBaseReq baseReq = (OpenBaseReq) request;
        String authCode = baseReq.getAuthCode();
        this.tsysApikeyService.verifySignature(request, baseReq.getSign(), authCode);

        if (!verifyAccount) {
            return null;
        }

        Long accountId = baseReq.getAccountId() != null ? baseReq.getAccountId() : baseReq.getId();
        TSteamAccount account = this.tSteamAccountService.getById(accountId);
        if (account == null) {
            throw new BizException("account not exist");
        }
        if (!account.getAuthCode().equals(authCode)) {
            throw new BizException("account auth code error");
        }

        return account;
    }

}
