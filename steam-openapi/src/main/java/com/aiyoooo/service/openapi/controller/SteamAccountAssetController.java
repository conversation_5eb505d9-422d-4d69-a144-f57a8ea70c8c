package com.aiyoooo.service.openapi.controller;

import com.aiyoooo.service.biz.service.SteamAccountAssetService;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.task.TaskProducer;
import com.aiyoooo.service.common.annotation.RequirePermission;
import com.aiyoooo.service.common.annotation.RestResponse;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.result.ResponseResult;
import com.aiyoooo.service.common.util.RedisUtil;
import com.aiyoooo.service.dao.dto.SteamAccountAssetDto;
import com.aiyoooo.service.dao.dto.SteamOpenBoxDto;
import com.aiyoooo.service.dao.dto.SteamStartOpenBoxDto;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.vo.SteamAccountAssetVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import com.aiyoooo.service.common.annotation.OperationLog;

/**
 * Steam账户资产控制器
 */
@Slf4j
@RestResponse
@RequestMapping("/v1/steam_account_asset")
@Api(tags = "Steam账户资产", description = "提供Steam账户资产和库存的查询功能")
public class SteamAccountAssetController extends BaseController {

    @Resource
    private SteamAccountAssetService steamAccountAssetService;

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountAssetService tSteamAccountAssetService;

    @Resource
    private TaskProducer taskProducer;

    @Resource
    private RedisUtil redisUtil;


    @PostMapping("/inventory")
    @ApiOperation(value = "获取资产列表", notes = "查询Steam账户的资产列表")
    @OperationLog(module = "Steam资产", operationType = "查询", description = "获取资产列表")
    @RequirePermission(value = "steam_account_asset.inventory", description = "获取资产列表")
    public ResponseResult<SteamAccountAssetVo> queryAssetList(@RequestBody @Valid SteamAccountAssetDto request) {
        verifyAndGetAccount(request, true);
        return ResponseResult.success(steamAccountAssetService.queryAssetList(request));
    }


//    @PostMapping("/start_open_box")
//    @ApiOperation(value = "开始开箱通知", notes = "开始开箱通知")
//    public ResponseResult openBoxNotify(@RequestBody @Valid SteamStartOpenBoxDto request) {
//
//        TSteamAccount account = tSteamAccountService.getById(request.getAccountId());
//        tSteamAccountAssetService.getFirstAsset(account,1);
//
//        LambdaQueryWrapper<TSteamAccountAsset> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(TSteamAccountAsset::getAccountId, account.getId());
//        queryWrapper.orderByDesc(TSteamAccountAsset::getAssetid);
//        queryWrapper.last("LIMIT 1");
//        TSteamAccountAsset asset = tSteamAccountAssetService.getOne(queryWrapper);
//        if (null == asset) {
//            throw new BizException("账号暂无库存");
//        }
//        String key = "steam:box:" + account.getId() + ":" + request.getTaskId();
//
//        redisUtil.set(key, asset.getAssetid(),300);
//
//        return ResponseResult.success();
//    }
//
//
//    @PostMapping("/open_box_notify")
//    @ApiOperation(value = "开箱结束通知", notes = "开箱结束通知")
//    public ResponseResult openBoxNotify(@RequestBody @Valid SteamOpenBoxDto request) {
//        taskProducer.produceOpenBoxNotify(request);
//        return ResponseResult.success();
//    }


//
//
//    @GetMapping("/get_open_box_size")
//    @ApiOperation(value = "获取账号已开箱子个数", notes = "获取账号已开箱子个数")
//    public ResponseResult get_open_box_size(@RequestBody @Valid SteamStartOpenBoxDto request) {
//        TSteamAccount account = tSteamAccountService.getBySteamAccount(request.getSteamAccount());
//        LambdaQueryWrapper<TSteamAccountAsset> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(TSteamAccountAsset::getAccountId, account.getId());
//        queryWrapper.orderByDesc(TSteamAccountAsset::getAssetid);
//        queryWrapper.last("LIMIT 1");
//        TSteamAccountAsset asset = tSteamAccountAssetService.getOne(queryWrapper);
//        if (null == asset) {
//            throw new BizException("账号暂无库存");
//        }
//        String key = "steam:box:" + account.getId() + ":" + request.getTaskId();
//
//        redisUtil.set(key, asset.getAssetid(),300);
//
//        return ResponseResult.success();
//    }

}
