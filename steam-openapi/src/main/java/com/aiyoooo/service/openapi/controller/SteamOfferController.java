package com.aiyoooo.service.openapi.controller;

import com.aiyoooo.service.biz.service.SteamOfferService;
import com.aiyoooo.service.biz.service.tsys.TsysApikeyService;
import com.aiyoooo.service.common.annotation.OperationLog;
import com.aiyoooo.service.common.annotation.RequirePermission;
import com.aiyoooo.service.common.annotation.RestResponse;
import com.aiyoooo.service.common.result.ResponseResult;
import com.aiyoooo.service.dao.dto.BaseDetailDto;
import com.aiyoooo.service.dao.dto.SteamOfferBuyReq;
import com.aiyoooo.service.dao.dto.SteamOfferSellReq;
import com.aiyoooo.service.dao.vo.BaseCreateVo;
import com.aiyoooo.service.dao.vo.SteamOfferVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * steam账号管理控制器
 */
@Slf4j
@RestResponse
@RequestMapping("/v1/offer")
@Api(tags = "steam交易报价", description = "steam交易报价")
public class SteamOfferController extends BaseController {

    @Resource
    private SteamOfferService steamOfferService;


    @PostMapping("/sell")
    @ApiOperation(value = "卖出", notes = "steam转出交易报价")
    @OperationLog(module = "交易Offer", operationType = "创建", description = "创建卖出Offer")
    @RequirePermission(value = "offer.sell", description = "创建卖出Offer")
    public ResponseResult<BaseCreateVo> sell(@RequestBody @Valid SteamOfferSellReq request) {
        verifyAndGetAccount(request, true);
        return ResponseResult.success(steamOfferService.sell(request));
    }


    @PostMapping("/buy")
    @ApiOperation(value = "买入", notes = "steam买入交易报价")
    @OperationLog(module = "交易Offer", operationType = "创建", description = "创建买入Offer")
    @RequirePermission(value = "offer.buy", description = "创建买入Offer")
    public ResponseResult<BaseCreateVo> buy(@RequestBody @Valid SteamOfferBuyReq request) {
        verifyAndGetAccount(request, true);
        return ResponseResult.success(steamOfferService.buy(request));
    }


    @PostMapping("/detail")
    @ApiOperation(value = "详情", notes = "steam交易报价详情查询")
    @OperationLog(module = "交易Offer", operationType = "查询", description = "查询Offer详情")
    @RequirePermission(value = "offer.detail", description = "查询Offer详情")
    public ResponseResult<SteamOfferVo> sell(@RequestBody @Valid BaseDetailDto request) {
        verifyAndGetAccount(request, false);
        return ResponseResult.success(steamOfferService.detail(request));
    }


}
