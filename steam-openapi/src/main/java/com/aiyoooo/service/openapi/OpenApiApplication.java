package com.aiyoooo.service.openapi;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * OpenApi服务
 *
 * <AUTHOR>
 * @since 2024/12/14
 */
@SpringBootApplication(scanBasePackages = {"com.aiyoooo.service.*"})
@Configuration
@EnableScheduling
@Slf4j
@EnableTransactionManagement
public class OpenApiApplication {


    public static void main(String[] args) {
        SpringApplication.run(OpenApiApplication.class, args);
    }

    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(200);// 自定义线程个数
        return taskScheduler;
    }

}
