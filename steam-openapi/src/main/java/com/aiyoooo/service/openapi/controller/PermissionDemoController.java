package com.aiyoooo.service.openapi.controller;

import com.aiyoooo.service.common.annotation.RequirePermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 权限演示控制器
 * 用于测试权限控制功能
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@RestController
@RequestMapping("/api/permission-demo")
@Api(tags = "权限演示")
public class PermissionDemoController {

    /**
     * 公开接口 - 无需权限
     */
    @GetMapping("/public")
    @ApiOperation("公开接口")
    public String publicEndpoint() {
        return "这是一个公开接口，无需权限验证";
    }

    /**
     * 受保护接口 - 需要权限
     */
    @GetMapping("/protected")
    @ApiOperation("受保护接口")
    @RequirePermission(value = "steam_account.create", description = "访问受保护的演示接口")
    public String protectedEndpoint(@ApiParam("授权码") @RequestParam String authCode) {
        return "恭喜！您有权限访问这个受保护的接口。授权码: " + authCode;
    }

    /**
     * 可选权限接口 - 没有权限时不抛异常
     */
    @GetMapping("/optional")
    @ApiOperation("可选权限接口")
    @RequirePermission(value = "/api/permission-demo/optional", required = false, description = "可选权限的演示接口")
    public String optionalPermissionEndpoint(@ApiParam("授权码") @RequestParam String authCode) {
        return "这个接口即使没有权限也可以访问，但会记录日志。授权码: " + authCode;
    }

    /**
     * 管理员接口 - 需要管理员权限
     */
    @PostMapping("/admin")
    @ApiOperation("管理员接口")
    @RequirePermission(value = "/api/permission-demo/admin", description = "管理员专用接口")
    public String adminEndpoint(@ApiParam("授权码") @RequestParam String authCode,
                               @ApiParam("操作内容") @RequestBody String operation) {
        return "管理员操作成功: " + operation + "，授权码: " + authCode;
    }

    /**
     * 使用URI作为权限编码的接口
     */
    @GetMapping("/auto-permission")
    @ApiOperation("自动权限接口")
    @RequirePermission(description = "使用URI作为权限编码的接口")
    public String autoPermissionEndpoint(@ApiParam("授权码") @RequestParam String authCode) {
        return "这个接口使用请求URI作为权限编码进行验证。授权码: " + authCode;
    }

    /**
     * 测试权限检查
     */
    @GetMapping("/test-permission")
    @ApiOperation("测试权限检查")
    public String testPermission(@ApiParam("授权码") @RequestParam String authCode,
                                @ApiParam("接口路径") @RequestParam String interfacePath) {
        // 这里可以手动调用权限服务进行测试
        return String.format("测试权限检查 - 授权码: %s, 接口路径: %s", authCode, interfacePath);
    }
} 