package com.aiyoooo.service.openapi.controller;

import com.aiyoooo.service.common.annotation.RestResponse;
import com.aiyoooo.service.common.vo.VersionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 健康心跳检测控制器
 */
@Slf4j
@RestResponse
@RequestMapping("ok")
@Api(tags = "健康检查", description = "提供服务的健康状态检查和版本信息")
public class OkController {

    @GetMapping("")
    @ApiOperation(value = "健康检查", notes = "检查服务是否正常运行，并返回版本信息")
    public VersionVo ok() {
        return VersionVo.builder()
                .version("1.0.0")
                .description("Steam Service V2.0.1")
                .pubDate("2025/07/17 proxy")
                .build();
    }
}