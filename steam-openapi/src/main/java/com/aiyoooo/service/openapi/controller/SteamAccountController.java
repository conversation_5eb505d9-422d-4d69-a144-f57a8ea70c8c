package com.aiyoooo.service.openapi.controller;

import cn.hutool.json.JSONObject;
import com.aiyoooo.service.biz.service.SteamAccountService;
import com.aiyoooo.service.biz.service.tsys.TsysApikeyService;
import com.aiyoooo.service.common.annotation.OperationLog;
import com.aiyoooo.service.common.annotation.RequirePermission;
import com.aiyoooo.service.common.annotation.RestResponse;
import com.aiyoooo.service.common.result.ResponseResult;
import com.aiyoooo.service.dao.dto.SteamAccountCreateDto;
import com.aiyoooo.service.dao.dto.BaseDetailDto;
import com.aiyoooo.service.dao.dto.SteamAccountInvalidSessionDto;
import com.aiyoooo.service.dao.dto.SteamAccountLoginSessionDto;
import com.aiyoooo.service.dao.dto.SteamAccountRefreshSteamAccountDto;
import com.aiyoooo.service.dao.vo.SteamAccountCreateVo;
import com.aiyoooo.service.dao.vo.SteamAccountDetailNewVo;
import com.aiyoooo.service.dao.vo.SteamAccountDetailSafeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * steam账号管理控制器
 */
@Slf4j
@RestResponse
@RequestMapping("/v1/steam_account")
@Api(tags = "Steam账号管理", description = "提供Steam账号的创建、登录、会话管理等功能")
public class SteamAccountController extends BaseController {

    @Resource
    private SteamAccountService steamAccountService;

    @PostMapping("/create")
    @ApiOperation(value = "创建Steam账号", notes = "创建新的Steam账号并返回账号信息")
    @RequirePermission(value = "steam_account.create", description = "创建Steam账号")
    @OperationLog(module = "Steam账号", operationType = "创建", description = "创建Steam账号")
    public ResponseResult<SteamAccountCreateVo> create(@RequestBody @Valid SteamAccountCreateDto request) {
        verifyAndGetAccount(request, false);

        return ResponseResult.success(steamAccountService.create(request));
    }

    @PostMapping("/refresh_steam_account")
    @ApiOperation(value = "更新Steam账号信息", notes = "更新Steam账号的基本信息")
    @OperationLog(module = "Steam账号", operationType = "更新", description = "更新Steam账号")
    @RequirePermission(value = "steam_account.update", description = "更新Steam账号")
    public ResponseResult refreshSteamAccount(@RequestBody @Valid SteamAccountRefreshSteamAccountDto request) {
        verifyAndGetAccount(request, true);
        steamAccountService.refreshSteamAccount(request);
        return ResponseResult.success();
    }

    @PostMapping("/login_session")
    @ApiOperation(value = "手动登录会话", notes = "手动进行Steam账号的登录会话")
    @RequirePermission(value = "steam_account.login", description = "手动登录会话")
    public ResponseResult loginSession(@RequestBody @Valid SteamAccountLoginSessionDto request) {
        verifyAndGetAccount(request, true);
        steamAccountService.loginSession(request);
        return ResponseResult.success();
    }

    @PostMapping("/invalid_session")
    @ApiOperation(value = "session失效", notes = "外部session失效通知")
    @RequirePermission(value = "steam_account.invalid_session", description = "外部session失效通知")
    public ResponseResult invalidSession(@RequestBody @Valid SteamAccountInvalidSessionDto request) {
        verifyAndGetAccount(request, true);
        steamAccountService.invalidSession(request);
        return ResponseResult.success();
    }

    @PostMapping("/detail")
    @ApiOperation(value = "获取账号详情", notes = "根据ID获取Steam账号的详细信息")
    @OperationLog(module = "Steam账号", operationType = "查询", description = "查询Steam账号详情")
    @RequirePermission(value = "steam_account.detail", description = "查询Steam账号详情")
    public ResponseResult<SteamAccountDetailNewVo> detailNew(@RequestBody @Valid BaseDetailDto request) {
        verifyAndGetAccount(request, true);
        return ResponseResult.success(steamAccountService.detailNew(request.getId()));
    }

    @PostMapping("/detail_safe")
    @ApiOperation(value = "获取账号详情(安全)", notes = "根据ID获取Steam账号的详细信息(安全)")
    @OperationLog(module = "Steam账号", operationType = "查询", description = "查询Steam账号详情(安全)")
    @RequirePermission(value = "steam_account.detail.safe", description = "查询Steam账号详情(安全)")
    public ResponseResult<SteamAccountDetailSafeVo> detailSafe(@RequestBody @Valid BaseDetailDto request) {
        verifyAndGetAccount(request, true);
        return ResponseResult.success(steamAccountService.detailSafe(request.getId()));
    }

//    @GetMapping("/detail_account_info")
//    @ApiOperation(value = "获取账户密码code信息", notes = "根据SteamID获取账户密码code信息")
//    public ResponseResult<JSONObject> detailAccountInfo(
//            @ApiParam(value = "accountId", required = true)
//            @RequestParam @Valid String accountId) {
//        return ResponseResult.success(steamAccountService.detailAccountInfo(accountId));
//    }

}
