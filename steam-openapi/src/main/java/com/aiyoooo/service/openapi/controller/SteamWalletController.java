package com.aiyoooo.service.openapi.controller;

import com.aiyoooo.service.biz.service.SteamWalletHistoryService;
import com.aiyoooo.service.common.annotation.RestResponse;
import com.aiyoooo.service.common.result.ResponseResult;
import com.aiyoooo.service.dao.dto.*;
import com.aiyoooo.service.dao.entity.t.TSteamWalletHistory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * steam账号管理控制器
 */
@Slf4j
@RestResponse
@RequestMapping("/v1/wallet_history")
@Api(tags = "Steam钱包账号历史", description = "Steam钱包账号历史")
public class SteamWalletController {

    @Resource
    private SteamWalletHistoryService steamWalletHistoryService;

    @PostMapping("/list")
    @ApiOperation(value = "列表查询历史", notes = "列表查询历史")
    public ResponseResult<List<TSteamWalletHistory>> list(@RequestBody @Valid BaseDto request) {
        return ResponseResult.success(steamWalletHistoryService.list(request.getId()));
    }

}
