package com.aiyoooo.service.openapi.controller;

import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.biz.service.SteamAccountAssetService;
import com.aiyoooo.service.biz.service.SteamOpenBoxTaskService;
import com.aiyoooo.service.common.annotation.OperationLog;
import com.aiyoooo.service.common.annotation.RequirePermission;
import com.aiyoooo.service.common.annotation.RestResponse;
import com.aiyoooo.service.common.exception.BizException;
import com.aiyoooo.service.common.result.ResponseResult;
import com.aiyoooo.service.common.util.RedisLock;
import com.aiyoooo.service.common.util.SignatureTool;
import com.aiyoooo.service.dao.dto.*;
import com.aiyoooo.service.dao.entity.t.TSteamAccountAsset;
import com.aiyoooo.service.dao.entity.t.TSteamOpenBoxTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * steam账号管理控制器
 */
@Slf4j
@RestResponse
@RequestMapping("/v1/open_cases")
@Api(tags = "Steam账号管理", description = "提供Steam账号的创建、登录、会话管理等功能")
public class OpenCaseController extends BaseController {

    @Resource
    private SteamAccountAssetService steamAccountAssetService;

    @Resource
    private SteamOpenBoxTaskService steamOpenBoxTaskService;

    @Resource
    private RedisLock redisLock;


    @PostMapping("/csgo")
    @ApiOperation(value = "开启csgo箱子", notes = "开启csgo箱子")
    @RequirePermission(value = "open_cases.csgo", description = "开启csgo箱子")
    @OperationLog(module = "开箱", operationType = "创建", description = "创建开启csgo箱子")
    public ResponseResult<List<TSteamAccountAsset>> open(@RequestBody @Valid OpenCaseCreateDto request) {
        verifyAndGetAccount(request, true);

        log.info("开始开箱,{},{}", request.getId(), JSONUtil.toJsonStr(request));
        long time = System.currentTimeMillis() + 180000;

        if (!redisLock.lock("steam:openbox:" + request.getAccountId(), String.valueOf(time))) {
            log.error("{}正在开箱中...,请勿重复提交!", request.getAccountId());
            throw new BizException("正在开箱中...,请勿重复提交!");
        }
        try {
            return ResponseResult.success(steamAccountAssetService.openCsgoCases(request));
        } finally {
            redisLock.unlock("steam:openbox:" + request.getAccountId(),  String.valueOf(time));
        }
    }


    @PostMapping("/csgo2")
    @ApiOperation(value = "开启csgo箱子", notes = "开启csgo箱子")
    @RequirePermission(value = "open_cases.csgo2", description = "开启csgo箱子2")
    @OperationLog(module = "开箱", operationType = "创建", description = "创建异步开启csgo箱子")
    public ResponseResult<TSteamOpenBoxTask> open(@RequestBody @Valid OpenCaseCreateDto2 request) {
        verifyAndGetAccount(request, true);

        log.info("创建开始开箱任务,{},{}", request.getId(), JSONUtil.toJsonStr(request));
        long time = System.currentTimeMillis() + 180000;

        if (!redisLock.lock("steam:openbox:" + request.getAccountId(), String.valueOf(time))) {
            log.error("{}创建开始开箱任务...,请勿重复提交!", request.getAccountId());
            throw new BizException("正在开箱中...,请勿重复提交!");
        }
        try {
            return ResponseResult.success(steamOpenBoxTaskService.createOpenBoxTask(request));
        } finally {
            redisLock.unlock("steam:openbox:" + request.getAccountId(),  String.valueOf(time));
        }
    }

    @GetMapping("/task/current/{taskId}")
    @ApiOperation(value = "查询账号当前开箱任务", notes = "查询账号当前开箱任务")
    public ResponseResult<OpenBoxCallbackDto> getCurrentTask(@PathVariable Long taskId) {
        OpenBoxCallbackDto currentTask = steamOpenBoxTaskService.getCurrentOpenBoxTask(taskId);
        return ResponseResult.success(currentTask);
    }


    public static void main(String[] args) {
        String privateKeyStrAll = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC2TYoBu3A36IpS"
                + "RzuoTfYbdouAz4QENmNqDeajhZEx+RctaI4hnA1mhrXyOIrgOWHIa3jod1iEJYYz"
                + "wr1ACsqeWI3nS/lZMaYeLULeE6in+pIRvjw+LxA6gsihTjjyzjFie9ZifknIttMw"
                + "mSDUiCo2g1DCpA/fdx03NO2YBesa8HCeO0Yc0ac7S+DyWvt2LyLwhQypkc7oc8KL"
                + "BuIReD1QnFk/zhjOqol16eDg1irCNnqs2OW4M4N3IDJ7eA2sDo3GLJgP6SOXBivp"
                + "X8EnFbNY2AP+y2FwSDBIKM8yZEe+644UI5YEe0aj7BVJb4lpuWATpsYMehEYZfSH"
                + "2kxiNweTAgMBAAECggEAPrXNk2r230SO0F7QlsS6tBfDRTwdHZXX/NNrUzTPDXGy"
                + "F1ywB7+2nF4amIf6vcb/bAIMRWtId6ywzX1fvHf52x57MeVgkN4TVq4bImmAysfX"
                + "F2NpNbraykXS3ioaTOvA8S/IP3lRyEmbvEvwjyuH2diyH0Jwc152HleGo5Tlmw4l"
                + "r2pNQ5EBj0OUt+riOi+YcQAPudRG2hBn61AAcUDXpas0JrCkfSVOjAaiMHJ8JRQ/"
                + "cargIdmIeTdHAFW48xBALkI8zvEBlq1JfvoJ+8fWXlLUl6zc/UXJqflJFToI78za"
                + "HRcvLIJEnAhnqiT/VGotF0HEDAwtyDMM6DTZii9+AQKBgQDn4FMhDtdOjz5EZo5j"
                + "YavV7ovRtrROAbsBX/ljWbNp3v3UOw967Q0/F5ugqcf8m8rZcdy65U9hU35YjM/y"
                + "mVxJ7snGPFT3+zbBhahCIziWk4RHUzMMQPm+hZYXkaiulKL7gulp7H+V1hoEWvNQ"
                + "WYHQEDpt6Is6iRWPHdFIvTNJ8QKBgQDJRObpHxOK/zVbMGoA23EaIMOfrIGPow0J"
                + "KmrucUJFpwDiNBxsTic90RMKWsuJ/7dXwsV1WoDQWQ06JBxICDxP2YnwVjz0Gy/j"
                + "+2MXNFqimxIdNamKTfr4ZPg/L1LI1nWE0+R1Fz1zDImHujJZepQ0+IjsPel1H+3c"
                + "1nvSavQFwwKBgQCRa1kXGWSkSyeE5ECWSTHoKAOeERRA+8rZEOSpjx763bOYvzV1"
                + "JWeyUs7yUqguX1I3TrZcq+U9p4xMYnsjrVR4tX3rifjL0MvFUFp2p/ocTQWfaalR"
                + "HQrDlqnLvDjqzCLu02opWP2m7bBBXPzNPmUwJI1QUo/GtSRFLjELikS5MQKBgGqu"
                + "3P6gfElm8S8+wFFn/9FroIWYeKxcLuK22ys8WXZ2CR/do1s9Kcu4cLFCYzTomJQq"
                + "J2QG7tSA/2PcFCmSQ/XHRzhfXbu+VWowq9t24rviGn3lHdKzdt4HFxbWzLisBGq9"
                + "rhDqmqXK9XM+HMYVFU3mlx0xy/dLT1eJUOS90E7lAoGAfJfmgTdiARBBTNm6MP4u"
                + "EHaU7FB4TLtynwg2lsNSNCf8cpZXzufTmyXwcr/cTh5lTJ06YcBTkZpxAHsF3XVN"
                + "4AkKJYyYG4mjfKzg4BtUtb9Ui4QIwMQTA3t/AWR6HzNvJTX+MsPnGOWBU6udKAGW"
                + "Uhgaj2O6qBSLv3KmWyMnqWA=";

        OpenCaseCreateDto dto = new OpenCaseCreateDto();
        dto.setAccountId(1891417919443832832L);
        dto.setAuthCode("test");
        dto.setTimestamp(System.currentTimeMillis() + "");
        CaseOpenApiRequestItem item = new CaseOpenApiRequestItem();
        item.setKeyId(44037420838L);
        item.setCaseId(44037420839L);
        List<CaseOpenApiRequestItem> objects = new ArrayList<>();
        objects.add(item);
        dto.setItemList(objects);
        System.out.println(JSONUtil.toJsonStr(dto));
        System.out.println(SignatureTool.generateSignature(dto, privateKeyStrAll));
    }

}
