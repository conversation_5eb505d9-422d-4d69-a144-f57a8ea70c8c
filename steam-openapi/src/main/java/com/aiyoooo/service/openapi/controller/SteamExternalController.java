package com.aiyoooo.service.openapi.controller;

import com.aiyoooo.service.biz.util.SteamUtil;
import com.aiyoooo.service.common.annotation.OperationLog;
import com.aiyoooo.service.common.annotation.RequirePermission;
import com.aiyoooo.service.common.annotation.RestResponse;
import com.aiyoooo.service.common.result.ResponseResult;
import com.aiyoooo.service.dao.dto.*;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.dao.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;

/**
 * steam账号管理控制器
 */
@Slf4j
@RestResponse
@RequestMapping("/v1/steam")
@Api(tags = "steam api", description = "steam api")
public class SteamExternalController extends BaseController {


    @PostMapping("/player")
    @ApiOperation(value = "获取玩家信息", notes = "查询Steam玩家信息")
    @OperationLog(module = "steam—api", operationType = "查询", description = "查询Steam玩家信息")
    @RequirePermission(value = "steam.player", description = "查询Steam玩家信息")
    public ResponseResult<SteamPlayerVo> getPlayer(@RequestBody @Valid SteamPlayerDto request) {
        verifyAndGetAccount(request, false);
        return ResponseResult.success(SteamUtil.getPlayerInfo(request.getSteamId()));
    }


    @PostMapping("/inventory")
    @ApiOperation(value = "获取库存列表", notes = "查询Steam账户的库存物品列表")
    @OperationLog(module = "steam—api", operationType = "查询", description = "查询实时库存列表")
    @RequirePermission(value = "steam.inventory", description = "查询实时库存列表")
    public ResponseResult<SteamAPIResponseInventory> queryInventoryList(@RequestBody @Valid SteamAccountAssetInventoryDto request) {
        TSteamAccount account = verifyAndGetAccount(request, true);
        return ResponseResult.success(SteamUtil.getInventoryByUserId(account, request.getAppid(), request.getCount(), request.getStartAssetid(),
                request.getLanguage()));
    }

    @PostMapping("/trade_history")
    @ApiOperation(value = "交易历史", notes = "steam交易历史")
    @OperationLog(module = "steam—api", operationType = "查询", description = "查询交易历史")
    @RequirePermission(value = "trade.history", description = "查询交易历史")
    public ResponseResult<SteamAPIResponseTradeHistory> getTradeHistory(@RequestBody @Valid SteamTradeHistoryReq request) {
        TSteamAccount account = verifyAndGetAccount(request, true);
        if(null != request.getStartAfterTime()){
            return ResponseResult.success(SteamUtil.getTradeHistory(account, request.getStartAfterTime()));
        } else if(null != request.getStartAfterTradeid()){
            return ResponseResult.success(SteamUtil.getTradeHistoryByTradeid(account, request.getStartAfterTradeid()));
        } else{
            return null;
        }
    }


    @PostMapping("/trade_offers")
    @ApiOperation(value = "交易offers", notes = "交易offer列表")
    @OperationLog(module = "steam—api", operationType = "查询", description = "交易offer列表")
    @RequirePermission(value = "trade.offers", description = "交易offer列表")
    public ResponseResult<SteamAPIResponseGetTradeOffers> getTradeOffers(@RequestBody @Valid BaseAccountDto request) {
        TSteamAccount account = verifyAndGetAccount(request, true);
        return ResponseResult.success(SteamUtil.getTradeOffers(account));
    }


    @PostMapping("/trade_offer_detail")
    @ApiOperation(value = "交易offer详情", notes = "交易offer详情")
    @OperationLog(module = "steam—api", operationType = "查询", description = "交易offer详情")
    @RequirePermission(value = "trade.offer.detail", description = "交易offer详情")
    public ResponseResult<SteamTradeDetailVo> getTradeOfferDetail(@RequestBody @Valid SteamOfferDetailDto request) {
        TSteamAccount account = verifyAndGetAccount(request, true);
        return ResponseResult.success(SteamUtil.getOfferTradeId(account,request.getTradeOfferId()));
    }




}
