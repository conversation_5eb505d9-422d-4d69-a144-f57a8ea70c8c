package com.aiyoooo.service.openapi.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aiyoooo.service.common.result.ResponseResult;
import com.aiyoooo.service.dao.dto.OpenBoxCallbackDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 开箱回调接收Controller（示例）
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Slf4j
@RestController
@RequestMapping("/callback")
@Api(tags = "开箱回调接收")
public class OpenBoxCallbackController {

    @PostMapping("/open_box")
    @ApiOperation(value = "接收开箱回调通知", notes = "接收开箱回调通知")
    public ResponseResult<String> receiveOpenBoxCallback(@RequestBody JSONObject callback) {
        log.info("收到开箱回调通知: {}", JSONUtil.toJsonStr(callback));
        
        try {
            log.info("处理开箱回调通知: {}", JSONUtil.toJsonStr(callback));

            return ResponseResult.success("回调处理成功");
        } catch (Exception e) {
            log.error("处理开箱回调失败", e);
            return ResponseResult.error("回调处理失败: " + e.getMessage());
        }
    }


}
