import com.aiyoooo.service.biz.service.SteamAccountService;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetHistoryService;
import com.aiyoooo.service.biz.service.t.TSteamAccountAssetService;
import com.aiyoooo.service.biz.service.t.TSteamAccountService;
import com.aiyoooo.service.biz.service.t.TSteamWalletHistoryService;
import com.aiyoooo.service.dao.entity.t.TSteamAccount;
import com.aiyoooo.service.openapi.OpenApiApplication;
import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Steam数据获取单元测试
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@SpringBootTest(classes = OpenApiApplication.class)
public class SteamTest {

    @Resource
    private TSteamAccountService tSteamAccountService;

    @Resource
    private TSteamAccountAssetService tSteamAccountAssetService;

    @Resource
    private TSteamAccountAssetHistoryService tSteamAccountAssetHistoryService;

    @Resource
    private TSteamWalletHistoryService tSteamWalletHistoryService;

    @Test
    void a(){
        TSteamAccount byId = tSteamAccountService.getById(1933080424913965057L);

//        tSteamAccountAssetService.doSyncAsset(byId);

//        tSteamAccountAssetHistoryService.syncAccountAssetHistory(byId);

        tSteamAccountAssetService.syncTradeOutHistory(byId);

    }

//    /**
//     * 获取账户余额
//     */
//    @Test
//    void getAccountBalance() {
////        String steamAccount = "ceshi1216";
//        String steamAccount = "zhangsf5898";
//        SteamBalanceVo balance = steamService.getAccountBalance(steamAccount);
//        System.out.println(balance);
//    }
//
//    /**
//     * 获取账户资料信息
//     */
//    @Test
//    void getAccountProfile() {
//        String steamAccount = "zhangsf5898";
//        SteamProfileVo profileVo = steamService.getAccountProfile(steamAccount);
//        System.out.println(profileVo);
//    }
}
