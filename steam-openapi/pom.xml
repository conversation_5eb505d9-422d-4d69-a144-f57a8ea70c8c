<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.aiyoooo.service</groupId>
        <artifactId>steam-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>steam-openapi</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.aiyoooo.service</groupId>
            <artifactId>steam-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aiyoooo.service</groupId>
            <artifactId>steam-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aiyoooo.service</groupId>
            <artifactId>steam-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aiyoooo.service</groupId>
            <artifactId>steam-job</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.aiyoooo.service</groupId>
            <artifactId>steam-job</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- Swagger UI -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>steam-openapi</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.14</version>
                <configuration>
                    <mainClass>com.aiyoooo.service.openapi.OpenApiApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>