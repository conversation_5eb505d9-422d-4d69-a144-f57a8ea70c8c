
DROP TABLE IF EXISTS `t_steam_account`;

CREATE TABLE `t_steam_account` (
                                   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                   `auth_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授权编号',
                                   `steam_account` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'steam账号',
                                   `steam_pwd` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'steam密码',
                                   `mafile` text COLLATE utf8mb4_unicode_ci COMMENT 'mafile',
                                   `mafile_depart` text COLLATE utf8mb4_unicode_ci COMMENT '部分令牌',
                                   `cookie_flag` varchar(4) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'cookie标志',
                                   `access_token` varchar(800) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问令牌',
                                   `refresh_token` text COLLATE utf8mb4_unicode_ci COMMENT '刷新令牌',
                                   `last_sessionid` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最新sessionId',
                                   `last_cookie` text COLLATE utf8mb4_unicode_ci COMMENT '最新cookie',
                                   `status` varchar(4) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态 dict={"0":"绑定中","1":"已解绑"}',
                                   `query_level` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '3' COMMENT '查询等级（如：1-低频、2-中频、3-高频',
                                   `safe_level` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '安全等级',
                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
                                   `asset_last_history_json` text COLLATE utf8mb4_unicode_ci COMMENT '资产最后更新记录,用来判断需要拉取的资产',
                                   `tag` text COLLATE utf8mb4_unicode_ci COMMENT 'tag',
                                   `use_category` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '使用大类',
                                   `use_class` varchar(4) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '使用小类',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1928029014006566913 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='steam账号';

/*Table structure for table `t_steam_account_asset` */

DROP TABLE IF EXISTS `t_steam_account_asset`;

CREATE TABLE `t_steam_account_asset` (
                                         `assetid` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资产id',
                                         `account_id` bigint(20) NOT NULL COMMENT '账号id',
                                         `classid` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '道具id',
                                         `instanceid` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'instanceid',
                                         `market_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
                                         `trade_start_datetime` datetime DEFAULT NULL COMMENT '可交易时间',
                                         `stock_in_datetime` datetime DEFAULT NULL COMMENT '入库时间',
                                         `stock_out_datetime` datetime DEFAULT NULL COMMENT '出库时间',
                                         `source_steam_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源账号',
                                         `target_steam_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '流向账号',
                                         `exterior_value` decimal(18,17) DEFAULT NULL COMMENT '磨损度',
                                         `market_hash_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'market_hash_name',
                                         `inventory_status` varchar(1) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '在库状态 0在库可用 1在库CD中 2已出库',
                                         `description` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '资产json',
                                         `check_exist` varchar(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '是否存在check',
                                         `last_refresh_datetime` datetime DEFAULT NULL COMMENT '最后刷新时间',
                                         PRIMARY KEY (`assetid`),
                                         KEY `account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Table structure for table `t_steam_account_asset_history` */

DROP TABLE IF EXISTS `t_steam_account_asset_history`;

CREATE TABLE `t_steam_account_asset_history` (
                                                 `history_item_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '唯一交易项ID，用于去重（例如：history095e56c6fa7eae68501e8fa58f54168a590af311_item0）',
                                                 `trade_date` datetime DEFAULT NULL COMMENT '交易时间',
                                                 `trade_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易类型（如：任务奖励）',
                                                 `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易说明',
                                                 `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '饰品名称（如：沙漠之鹰 | 跷跷板）',
                                                 `assetid` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品资产ID（如：***********）',
                                                 `classid` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型ID（classid）',
                                                 `steam_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源账号 或者 流向账号',
                                                 `instanceid` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'instanceid',
                                                 `flow_direction` varchar(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物品流向（in=收入，out=支出）',
                                                 `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                                 `account_id` bigint(20) NOT NULL COMMENT '账号id',
                                                 PRIMARY KEY (`history_item_id`),
                                                 UNIQUE KEY `history_item_id` (`history_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Table structure for table `t_steam_account_asset_history_archive` */

DROP TABLE IF EXISTS `t_steam_account_asset_history_archive`;

CREATE TABLE `t_steam_account_asset_history_archive` (
                                                         `history_item_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '唯一交易项ID，用于去重（例如：history095e56c6fa7eae68501e8fa58f54168a590af311_item0）',
                                                         `trade_date` datetime DEFAULT NULL COMMENT '交易时间',
                                                         `trade_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易类型（如：任务奖励）',
                                                         `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易说明',
                                                         `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '饰品名称（如：沙漠之鹰 | 跷跷板）',
                                                         `assetid` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品资产ID（如：***********）',
                                                         `classid` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型ID（classid）',
                                                         `steam_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源账号 或者 流向账号',
                                                         `instanceid` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'instanceid',
                                                         `flow_direction` varchar(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物品流向（in=收入，out=支出）',
                                                         `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                                         `account_id` bigint(20) NOT NULL COMMENT '账号id',
                                                         `archive_time` datetime DEFAULT NULL COMMENT '迁移时间',
                                                         PRIMARY KEY (`history_item_id`),
                                                         UNIQUE KEY `history_item_id` (`history_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Table structure for table `t_steam_account_asset_out_stock` */

DROP TABLE IF EXISTS `t_steam_account_asset_out_stock`;

CREATE TABLE `t_steam_account_asset_out_stock` (
                                                   `assetid` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资产id',
                                                   `account_id` bigint(20) NOT NULL COMMENT '账号id',
                                                   `classid` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '道具id',
                                                   `instanceid` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'instanceid',
                                                   `market_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
                                                   `trade_start_datetime` datetime DEFAULT NULL COMMENT '可交易时间',
                                                   `stock_in_datetime` datetime DEFAULT NULL COMMENT '入库时间',
                                                   `stock_out_datetime` datetime DEFAULT NULL COMMENT '出库时间',
                                                   `source_steam_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源账号',
                                                   `target_steam_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '流向账号',
                                                   `exterior_value` decimal(18,17) DEFAULT NULL COMMENT '磨损度',
                                                   `market_hash_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'market_hash_name',
                                                   `inventory_status` varchar(1) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '在库状态 0在库可用 1在库CD中 2已出库',
                                                   `description` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '资产json',
                                                   `check_exist` varchar(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '是否存在check',
                                                   PRIMARY KEY (`assetid`),
                                                   KEY `account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Table structure for table `t_steam_account_info` */

DROP TABLE IF EXISTS `t_steam_account_info`;

CREATE TABLE `t_steam_account_info` (
                                        `account_id` bigint(20) NOT NULL COMMENT 'accountId',
                                        `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
                                        `avatar_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像',
                                        `steam_email` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'steam邮箱',
                                        `apikey` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'apikey',
                                        `steam_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'steamId',
                                        `trade_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易url',
                                        `region` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
                                        `timecreated` bigint(20) DEFAULT NULL COMMENT 'steam注册时间',
                                        `account_level` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '账户等级',
                                        `has_red_flag` tinyint(1) DEFAULT NULL COMMENT '是否红信',
                                        `red_flag_date` datetime DEFAULT NULL COMMENT '红信日期',
                                        `personastate` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '在线状态：0 离线，1 在线，2 忙碌，3 离开，4 请勿打扰，5 打牌，6 看电影等。',
                                        `balance` decimal(10,2) DEFAULT NULL COMMENT '余额',
                                        `currency` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '余额币种',
                                        `protection_type` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '0.未启用，1.令牌电子邮件保护，2.令牌手机验证器保护，3.令牌手机验证器和电子邮件保护',
                                        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
                                        PRIMARY KEY (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='steam账号';

/*Table structure for table `t_steam_offer` */

DROP TABLE IF EXISTS `t_steam_offer`;

CREATE TABLE `t_steam_offer` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键编号',
                                 `type` varchar(4) NOT NULL COMMENT '类型，dict={"0":"平台出售","1":"平台购买"}',
                                 `account_id` bigint(20) DEFAULT NULL COMMENT 'steam账号id，平台购买是没有的',
                                 `trade_url` varchar(255) NOT NULL COMMENT '收货方steam交易链接',
                                 `quantity` int(11) NOT NULL COMMENT '数量',
                                 `status` char(1) NOT NULL COMMENT '状态 dict={"0":"待賣家發貨","1":"發貨中-待賣家令牌確認","2":"待買家確認","3":"交易成功","4":"訂單取消","5":"交易暫掛"}',
                                 `cancel_reason` varchar(255) DEFAULT NULL COMMENT '取消理由',
                                 `create_datetime` datetime NOT NULL COMMENT '创建时间',
                                 `finish_datetime` datetime DEFAULT NULL COMMENT '完成时间',
                                 `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                 `trade_offer_id` varchar(32) DEFAULT NULL COMMENT 'steam订单编号',
                                 `trade_offer_state` varchar(32) DEFAULT NULL COMMENT 'steam订单状态',
                                 `callback_url` varchar(1024) DEFAULT NULL COMMENT '状态变更通知url',
                                 `order_no` varchar(64) DEFAULT NULL COMMENT '外部订单号',
                                 `trade_offer_json` text COMMENT 'steam订单json',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `order_no` (`order_no`),
                                 KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1927994565508337665 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='steam交易报价';

/*Table structure for table `t_steam_offer_detail` */

DROP TABLE IF EXISTS `t_steam_offer_detail`;

CREATE TABLE `t_steam_offer_detail` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键编号',
                                        `offer_id` bigint(20) DEFAULT NULL COMMENT 'steam订单编号',
                                        `assetid` varchar(64) NOT NULL COMMENT '资产id',
                                        `amount` int(11) NOT NULL COMMENT '数量',
                                        `new_assetid` varchar(64) DEFAULT NULL COMMENT '新资产id',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=360 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='steam交易报价明细';

/*Table structure for table `t_steam_wallet_history` */

DROP TABLE IF EXISTS `t_steam_wallet_history`;

CREATE TABLE `t_steam_wallet_history` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增，唯一标识每条记录',
                                          `transaction_date` date DEFAULT NULL COMMENT '交易日期',
                                          `account_id` bigint(20) DEFAULT NULL,
                                          `item_description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易物品或内容描述',
                                          `transaction_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易类型（如：购买、转换、退款等）',
                                          `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式或来源（如：零售、钱包）',
                                          `total_amount` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易总金额',
                                          `wallet_change` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '钱包变更金额',
                                          `wallet_balance` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易后的钱包余额',
                                          `transaction_link` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '帮助页面链接（如有）',
                                          `transaction_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '从 transaction_link 提取的交易 ID',
                                          `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                          PRIMARY KEY (`id`),
                                          UNIQUE KEY `uniq_transaction_id` (`transaction_id`)
) ENGINE=InnoDB AUTO_INCREMENT=967138 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Table structure for table `t_steam_wallet_history_archive` */

DROP TABLE IF EXISTS `t_steam_wallet_history_archive`;

CREATE TABLE `t_steam_wallet_history_archive` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增，唯一标识每条记录',
                                                  `transaction_date` date DEFAULT NULL COMMENT '交易日期',
                                                  `account_id` bigint(20) DEFAULT NULL,
                                                  `item_description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易物品或内容描述',
                                                  `transaction_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易类型（如：购买、转换、退款等）',
                                                  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式或来源（如：零售、钱包）',
                                                  `total_amount` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易总金额',
                                                  `wallet_change` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '钱包变更金额',
                                                  `wallet_balance` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易后的钱包余额',
                                                  `transaction_link` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '帮助页面链接（如有）',
                                                  `transaction_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '从 transaction_link 提取的交易 ID',
                                                  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
                                                  `archive_time` datetime DEFAULT NULL COMMENT '迁移时间',
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `uniq_transaction_id` (`transaction_id`)
) ENGINE=InnoDB AUTO_INCREMENT=215570 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钱包流水历史数据';

/*Table structure for table `t_sys_operation_log` */

DROP TABLE IF EXISTS `t_sys_operation_log`;

CREATE TABLE `t_sys_operation_log` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
                                       `module` varchar(50) DEFAULT '' COMMENT '模块标题',
                                       `operation_type` varchar(50) DEFAULT '' COMMENT '操作类型',
                                       `description` varchar(255) DEFAULT '' COMMENT '操作描述',
                                       `method` varchar(255) DEFAULT '' COMMENT '请求方法',
                                       `request_method` varchar(10) DEFAULT '' COMMENT '请求方式',
                                       `request_url` varchar(255) DEFAULT '' COMMENT '请求URL',
                                       `request_ip` varchar(128) DEFAULT '' COMMENT '请求IP地址',
                                       `request_param` text COMMENT '请求参数',
                                       `response_result` text COMMENT '响应结果',
                                       `auth_code` varchar(50) NOT NULL DEFAULT '' COMMENT '授权编号',
                                       `status` char(1) DEFAULT '0' COMMENT '操作状态（0成功 1失败）',
                                       `error_msg` varchar(2000) DEFAULT '' COMMENT '错误消息',
                                       `cost_time` bigint(20) DEFAULT NULL COMMENT '操作时长(毫秒)',
                                       `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_auth_code` (`auth_code`),
                                       KEY `idx_operation_time` (`operation_time`),
                                       KEY `idx_auth_code_operation_time` (`auth_code`,`operation_time`),
                                       KEY `idx_module_auth_code` (`module`,`auth_code`),
                                       KEY `idx_status_auth_code` (`status`,`auth_code`)
) ENGINE=InnoDB AUTO_INCREMENT=59453 DEFAULT CHARSET=utf8 COMMENT='系统操作日志';

/*Table structure for table `tsys_apikey` */

DROP TABLE IF EXISTS `tsys_apikey`;

CREATE TABLE `tsys_apikey` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                               `auth_code` varchar(32) DEFAULT NULL COMMENT '授权编号',
                               `public_key` varchar(2048) DEFAULT NULL COMMENT 'RSA pulic_key',
                               `secret_key` varchar(32) DEFAULT NULL COMMENT '回调md5key',
                               `status` varchar(4) NOT NULL DEFAULT '0' COMMENT '状态(1=生效中 0=已失效)',
                               `offer_auto_switch` varchar(2) NOT NULL DEFAULT '0' COMMENT 'offer自动确认开关 0关,1开',
                               `create_datetime` datetime NOT NULL COMMENT '创建时间',
                               `updater` varchar(32) DEFAULT NULL COMMENT '更新人',
                               `update_datetime` datetime DEFAULT NULL COMMENT '修改时间',
                               `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='apikey 配置';

/*Table structure for table `tsys_interface` */

DROP TABLE IF EXISTS `tsys_interface`;

CREATE TABLE `tsys_interface` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                  `code` varchar(255) NOT NULL COMMENT '接口编码',
                                  `name` varchar(255) NOT NULL COMMENT '接口名称',
                                  `create_datetime` datetime NOT NULL COMMENT '创建时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4;

/*Table structure for table `tsys_permission` */

DROP TABLE IF EXISTS `tsys_permission`;

CREATE TABLE `tsys_permission` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                   `auth_code` varchar(32) NOT NULL COMMENT '授权编号',
                                   `interface` varchar(255) DEFAULT NULL COMMENT '接口',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4;

