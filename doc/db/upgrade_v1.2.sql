

insert  into `tsys_apikey`(`id`,`auth_code`,`public_key`,`secret_key`,`status`,`offer_auto_switch`,`create_datetime`,`updater`,`update_datetime`,`remark`) values (1,'test','MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtk2KAbtwN+iKUkc7qE32G3aLgM+EBDZjag3mo4WRMfkXLWiOIZwNZoa18jiK4DlhyGt46HdYhCWGM8K9QArKnliN50v5WTGmHi1C3hOop/qSEb48Pi8QOoLIoU448s4xYnvWYn5JyLbTMJkg1IgqNoNQwqQP33cdNzTtmAXrGvBwnjtGHNGnO0vg8lr7di8i8IUMqZHO6HPCiwbiEXg9UJxZP84YzqqJdeng4NYqwjZ6rNjluDODdyAye3gNrA6NxiyYD+kjlwYr6V/BJxWzWNgD/sthcEgwSCjPMmRHvuuOFCOWBHtGo+wVSW+JablgE6bGDHoRGGX0h9pMYjcHkwIDAQAB',NULL,'1','0','2025-05-22 10:46:50',NULL,'2025-05-22 10:46:56','测试'),(2,'mlkj-open-case','MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsLaYU/D1NHB1FTrGZzW1G+IvFQXCf5UZqyDMwjU0S8uQGOgGb/oHTxWmi+SmPH5ygOKEGSng9IzJQ3akVURcrOqqsP7COZ5r1PJpRlAG+2ouPAD1ovx68F45QbaS+cEoysC2VNEKkSy09RKlwK11obLVf8fW3+p0fkBiRFhYK+i3P9mQNCJpf7YXBeNxE7qgz6RdCXVx6nkyXQN/5fpRuKvbHkFoBndjp/9Zno/caeqaWL/CPLTuCM7jzHnbpgEU1bbiKELXUDVmw4Gm9mTlyGQT1TrrN/HjmQTXN2g+LUI5zpU2xSz4/OTjz4d21jcPXzdxOrj+gvzVcTIhOE6FYQIDAQAB','36280f1c26c14697a429847369559ba0','1','1','2025-05-22 15:32:14',NULL,'2025-05-22 15:32:16','开箱'),(3,'aiyoooo','MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtC9VkkF++UvmoetYTlCGiIXrRgIyTGDWu3uWSqpdhYiaKjPx9W1379kLrfJ4Bo0QfqZ2pozO0otQr6DWBTVRINEburDWjZgZJeKvueGKNGtM1QpX+Ca26/pF0ksgJX17vRAaS5XbO0+CMdT7ht/XzR83vOJyckjXLVX1RCBaCy6udecEi5g/DS4Zq6IUwIlSZEdErTfh9RZUFLLNc+5aeYehSWtOX8LTx3e/KLtOWRk/KnEvvvcHI/PL4W5VYjcnQVa+nyu+x44xrqQU3eeIk4OetaI5nY/0LduDKdt9aA4pe6UttRXbvwA1hCo+saRxfY6ea82JUH47kO0igOIYMwIDAQAB','d6563cf0faba44ffbe5a93f1f0dd7b62','1','0','2025-05-30 15:32:14',NULL,'2025-05-22 15:32:16','aiyoooo测试'),(4,'skinmarket','MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtC9VkkF++UvmoetYTlCGiIXrRgIyTGDWu3uWSqpdhYiaKjPx9W1379kLrfJ4Bo0QfqZ2pozO0otQr6DWBTVRINEburDWjZgZJeKvueGKNGtM1QpX+Ca26/pF0ksgJX17vRAaS5XbO0+CMdT7ht/XzR83vOJyckjXLVX1RCBaCy6udecEi5g/DS4Zq6IUwIlSZEdErTfh9RZUFLLNc+5aeYehSWtOX8LTx3e/KLtOWRk/KnEvvvcHI/PL4W5VYjcnQVa+nyu+x44xrqQU3eeIk4OetaI5nY/0LduDKdt9aA4pe6UttRXbvwA1hCo+saRxfY6ea82JUH47kO0igOIYMwIDAQAB','d6563cf0faba44ffbe5a93f1f0dd7b62','1','0','2025-05-30 15:32:14',NULL,'2025-05-22 15:32:16','skinmarket测试');

/*Data for the table `tsys_interface` */

insert  into `tsys_interface`(`id`,`code`,`name`,`create_datetime`) values (1,'steam_account.create','steam账号创建','2025-05-23 11:53:43'),(2,'steam_account.update','更新Steam账号','2025-05-23 11:54:19'),(3,'steam_account.login','手动登录会话','2025-05-23 11:54:48'),(4,'steam_account.detail','查询Steam账号详情','2025-05-23 11:55:16'),(5,'steam_account_asset.inventory','获取资产列表','2025-05-23 11:56:14'),(6,'offer.sell','创建卖出Offer','2025-05-23 11:57:02'),(7,'offer.buy','创建买入Offer','2025-05-23 11:57:27'),(8,'offer.detail','查询Offer详情','2025-05-23 11:57:54'),(9,'trade.history','交易历史','2025-05-23 14:17:51'),(10,'trade.offers','交易offers','2025-05-23 14:20:25'),(11,'trade.offer.detail','交易offer详情','2025-05-23 14:52:56'),(12,'steam.inventory','获取实时库存列表','2025-05-26 18:12:12'),(13,'steam.player','查询Steam玩家信息','2025-05-28 16:47:10');

/*Data for the table `tsys_permission` */

insert  into `tsys_permission`(`id`,`auth_code`,`interface`) values (1,'mlkj-open-case','steam_account.create'),(2,'mlkj-open-case','steam_account.update'),(3,'mlkj-open-case','steam_account_asset.inventory'),(4,'mlkj-open-case','offer.sell'),(5,'mlkj-open-case','offer.detail'),(6,'aiyoooo','steam_account.create'),(7,'aiyoooo','steam_account.update'),(8,'aiyoooo','steam_account.login'),(9,'aiyoooo','steam_account.detail'),(10,'aiyoooo','steam_account_asset.inventory'),(11,'aiyoooo','steam.inventory'),(12,'skinmarket','steam_account.create'),(13,'skinmarket','steam_account.update'),(14,'skinmarket','steam_account.login'),(15,'skinmarket','steam_account.detail'),(16,'skinmarket','steam_account_asset.inventory'),(17,'skinmarket','steam.inventory'),(18,'skinmarket','offer.sell'),(19,'skinmarket','offer.buy'),(20,'skinmarket','offer.detail'),(21,'skinmarket','steam.player'),(22,'skinmarket','trade.offers'),(23,'skinmarket','trade.history');

