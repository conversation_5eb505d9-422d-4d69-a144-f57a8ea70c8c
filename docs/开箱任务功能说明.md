# 异步开箱任务功能说明

## 功能概述

异步开箱任务功能允许用户根据箱子和钥匙名称批量开箱，系统会自动分批处理并实时通知开箱结果。

## 主要特性

- ✅ **批次通知**：每批次（最多10个箱子）完成后立即通知
- ✅ **分批处理**：每批最多10个箱子，避免单次处理过多
- ✅ **状态跟踪**：完整的任务状态管理和错误处理
- ✅ **自动重试**：定时器每10秒扫描待处理任务
- ✅ **双重通知**：批次完成通知 + 任务完成通知
- ✅ **并发控制**：防止同一账号创建多个开箱任务
- ✅ **资产冲突检测**：确保不会分配已被占用的箱子和钥匙

## API接口

### 1. 创建开箱任务

**接口地址：** `POST /open_cases/csgo2`

**安全检查：**
- 检查账号是否存在正在进行的开箱任务
- 检查箱子和钥匙数量是否足够
- 检查资产是否被其他任务占用

**请求参数：**
```json
{
  "accountId": 1891417919443832832,
  "boxName": "CS:GO Weapon Case",
  "keyName": "CS:GO Case Key", 
  "count": 50,
  "callbackUrl": "http://your-domain.com/callback/open_box"
}
```

**响应结果：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": **********,
    "accountId": 1891417919443832832,
    "boxName": "CS:GO Weapon Case",
    "keyName": "CS:GO Case Key",
    "totalCount": 50,
    "processedCount": 0,
    "successCount": 0,
    "failedCount": 0,
    "status": "0",
    "callbackUrl": "http://your-domain.com/callback/open_box",
    "createTime": "2025-08-26T10:00:00"
  }
}
```

### 2. 查询当前开箱任务

**接口地址：** `GET /open_cases/task/current/{accountId}`

**响应结果：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": **********,
    "accountId": 1891417919443832832,
    "boxName": "CS:GO Weapon Case",
    "keyName": "CS:GO Case Key",
    "totalCount": 50,
    "processedCount": 20,
    "successCount": 18,
    "failedCount": 2,
    "status": "1",
    "callbackUrl": "http://your-domain.com/callback/open_box",
    "createTime": "2025-08-26T10:00:00"
  }
}
```

### 3. 查询开箱任务历史

**接口地址：** `GET /open_cases/task/history/{accountId}?limit=10`

**响应结果：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": **********,
      "status": "2",
      "totalCount": 50,
      "successCount": 48,
      "failedCount": 2,
      "createTime": "2025-08-26T10:00:00",
      "finishTime": "2025-08-26T10:15:30"
    }
  ]
}
```

## 回调通知

系统会发送两种类型的回调通知：

### 1. 批次完成通知 (BATCH_COMPLETED)

每完成一个批次（最多10个箱子）就会发送此通知：

```json
{
  "taskId": **********,
  "accountId": 1891417919443832832,
  "boxName": "CS:GO Weapon Case",
  "keyName": "CS:GO Case Key",
  "totalCount": 50,
  "notifyType": "BATCH_COMPLETED",
  "createTime": "2025-08-26T10:00:00",
  "results": [
    {
      "batchNo": 1,
      "caseAssetId": "***********,***********,***********",
      "keyAssetId": "***********,***********,***********",
      "status": "2",
      "resultAssetId": "***********,***********,***********",
      "resultAssetName": "[{\"assetid\":\"***********\",\"marketHashName\":\"AK-47 | Redline (Field-Tested)\",\"exteriorValue\":0.25,...},{\"assetid\":\"***********\",\"marketHashName\":\"M4A4 | Howl (Minimal Wear)\",\"exteriorValue\":0.08,...}]",
      "finishTime": "2025-08-26T10:01:30"
    }
  ],
  "assets": [
    {
      "assetid": "***********",
      "marketHashName": "AK-47 | Redline (Field-Tested)",
      "exteriorValue": 0.25,
      "description": "{...}"
    },
    {
      "assetid": "***********",
      "marketHashName": "M4A4 | Howl (Minimal Wear)",
      "exteriorValue": 0.08,
      "description": "{...}"
    },
    {
      "assetid": "***********",
      "marketHashName": "AWP | Dragon Lore (Factory New)",
      "exteriorValue": 0.02,
      "description": "{...}"
    }
  ]
}
```

### 2. 整个任务完成通知 (TASK_COMPLETED)

所有箱子处理完成后发送此通知，包含所有开箱得到的资产：

```json
{
  "taskId": **********,
  "accountId": 1891417919443832832,
  "boxName": "CS:GO Weapon Case",
  "keyName": "CS:GO Case Key",
  "totalCount": 50,
  "successCount": 48,
  "failedCount": 2,
  "status": "2",
  "notifyType": "TASK_COMPLETED",
  "createTime": "2025-08-26T10:00:00",
  "finishTime": "2025-08-26T10:15:30",
  "results": [],
  "assets": [
    {
      "assetid": "***********",
      "accountId": 1891417919443832832,
      "classid": "310776",
      "instanceid": "*********",
      "marketName": "AK-47 | Redline (Field-Tested)",
      "marketHashName": "AK-47 | Redline (Field-Tested)",
      "inventoryStatus": "0",
      "exteriorValue": 0.25,
      "description": "{...}"
    },
    {
      "assetid": "***********",
      "accountId": 1891417919443832832,
      "classid": "310777",
      "instanceid": "*********",
      "marketName": "M4A4 | Howl (Minimal Wear)",
      "marketHashName": "M4A4 | Howl (Minimal Wear)",
      "inventoryStatus": "0",
      "exteriorValue": 0.08,
      "description": "{...}"
    }
  ]
}
```

## 状态说明

### 任务状态
- `0` - 待处理
- `1` - 处理中  
- `2` - 已完成
- `3` - 已失败

### 通知类型
- `BATCH_COMPLETED` - 批次完成
- `TASK_COMPLETED` - 整个任务完成

## 数据库表结构

### 主任务表 (t_steam_open_box_task)
```sql
CREATE TABLE `t_steam_open_box_task` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `account_id` bigint(20) NOT NULL COMMENT '账号ID',
  `box_name` varchar(255) NOT NULL COMMENT '箱子名称',
  `key_name` varchar(255) NOT NULL COMMENT '钥匙名称',
  `total_count` int(11) NOT NULL COMMENT '总数量',
  `processed_count` int(11) NOT NULL DEFAULT '0' COMMENT '已处理数量',
  `success_count` int(11) NOT NULL DEFAULT '0' COMMENT '成功数量',
  `failed_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败数量',
  `status` varchar(10) NOT NULL DEFAULT '0' COMMENT '任务状态',
  `callback_url` varchar(500) NOT NULL COMMENT '回调URL',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `error_message` text COMMENT '错误信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
);
```

### 详情表 (t_steam_open_box_task_detail)
```sql
CREATE TABLE `t_steam_open_box_task_detail` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `batch_no` int(11) NOT NULL COMMENT '批次号',
  `batch_count` int(11) NOT NULL COMMENT '批次数量',
  `case_asset_ids` text NOT NULL COMMENT '箱子资产ID列表，逗号分隔',
  `key_asset_ids` text NOT NULL COMMENT '钥匙资产ID列表，逗号分隔',
  `status` varchar(10) NOT NULL DEFAULT '0' COMMENT '状态',
  `result_asset_ids` text DEFAULT NULL COMMENT '开箱结果资产ID列表，逗号分隔',
  `result_asset_info` longtext DEFAULT NULL COMMENT '开箱结果资产信息，List<TSteamAccountAsset>的JSON',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `error_message` text COMMENT '错误信息',
  PRIMARY KEY (`id`)
);
```

## 部署说明

1. **执行SQL脚本**：运行 `sql/create_open_box_task_tables.sql` 创建数据库表
2. **重启应用**：让新的定时任务生效
3. **配置回调地址**：确保回调URL可以正常访问
4. **监控日志**：观察任务处理情况

## 注意事项

- 确保账号有足够的箱子和钥匙
- 回调URL必须能正常接收POST请求
- 建议在测试环境先验证回调逻辑
- 定时任务每10秒执行一次，每次最多处理5个任务
- 单个任务按批次处理，每批次最多10个箱子一次性开箱
- 每个批次完成后立即发送通知，无需等待整个任务完成
- 每个批次只创建一条detail记录，包含该批次所有箱子和结果信息
- 同一账号同时只能有一个开箱任务，防止资产分配冲突
- 系统会自动检测资产冲突，确保不会重复分配箱子和钥匙
