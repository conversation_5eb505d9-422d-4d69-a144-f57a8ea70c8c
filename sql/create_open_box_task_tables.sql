-- 创建开箱任务表
CREATE TABLE `t_steam_open_box_task` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `account_id` bigint(20) NOT NULL COMMENT '账号ID',
  `box_name` varchar(255) NOT NULL COMMENT '箱子名称',
  `key_name` varchar(255) NOT NULL COMMENT '钥匙名称',
  `total_count` int(11) NOT NULL COMMENT '总数量',
  `success_count` int(11) NOT NULL DEFAULT '0' COMMENT '成功数量',
  `failed_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败数量',
  `status` varchar(10) NOT NULL DEFAULT '0' COMMENT '任务状态 0-待处理 1-处理中 2-已完成 3-已失败',
  `callback_url` varchar(500) NOT NULL COMMENT '回调URL',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `error_message` text COMMENT '错误信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='steam开箱任务表';

-- 创建开箱任务详情表
CREATE TABLE `t_steam_open_box_task_detail` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `batch_no` int(11) NOT NULL COMMENT '批次号',
  `batch_count` int(11) NOT NULL COMMENT '批次数量',
  `case_asset_ids` text NOT NULL COMMENT '箱子资产ID列表，逗号分隔',
  `key_asset_ids` text NOT NULL COMMENT '钥匙资产ID列表，逗号分隔',
  `status` varchar(10) NOT NULL DEFAULT '0' COMMENT '状态 0-待处理 1-处理中 2-已完成 3-已失败',
  `result_asset_ids` text DEFAULT NULL COMMENT '开箱结果资产ID列表，逗号分隔',
  `result_asset_info` longtext DEFAULT NULL COMMENT '开箱结果资产信息，List<TSteamAccountAsset>的JSON',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `error_message` text COMMENT '错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='steam开箱任务详情表';
